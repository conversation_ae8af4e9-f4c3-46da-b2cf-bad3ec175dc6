import type { AppSettings } from '../types/settings';
import { electronDataManager } from './electronDataManager';

export class SettingsService {
  
  // ??????
  async getSettings(): Promise<AppSettings> {
    try {
      // ?????Electron??
      const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
      
      if (!isElectron) {
        // ???????????
        return this.getDefaultSettings();
      }
      
      const settings = await electronDataManager.getSettings();
      // ?????????????
      if (!settings) {
        return this.getDefaultSettings();
      }
      return settings;
    } catch (error) {
      console.error('??????:', error);
      return this.getDefaultSettings();
    }
  }

  // ??????
  async saveSettings(settings: AppSettings): Promise<void> {
    try {
      // ?????Electron??
      const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
      
      if (!isElectron) {
        // ???????????
        console.log('????????????', settings);
        return;
      }
      
      await electronDataManager.saveSettings(settings);
    } catch (error) {
      console.error('??????:', error);
      throw error;
    }
  }

  // ??????
  private getDefaultSettings(): AppSettings {
    return {
      user: {
        profile: {
          name: '',
          title: ''
        },
        preferences: {
          defaultSessionDuration: 60,
          workingHours: {
            start: '09:00',
            end: '18:00'
          },
          autoSave: true
        }
      },

      notifications: {
        appointments: {
          enabled: true,
          advanceTime: 15,
          desktopNotification: true
        },
        system: {
          updates: true,
          errors: true,
          maintenance: true
        }
      },
      dataManagement: {
        backup: {
          autoBackup: true,
          backupTime: '02:00',
          frequency: 'daily',
          location: 'local',
          encryption: false
        },
        export: {
          format: 'xlsx',
          dateRange: 'all',
          includeImages: true,
          compression: true
        },
        cleanup: {
          tempFiles: true,
          logs: true,
          cache: true,
          oldBackups: true
        }
      },
      version: '1.0.0',
      lastUpdated: new Date().toISOString()
    };
  }

  // ????????
  async resetSettings(): Promise<void> {
    const defaultSettings = this.getDefaultSettings();
    await this.saveSettings(defaultSettings);
  }

  // ???????
  async updateSetting(path: string, value: unknown): Promise<void> {
    const currentSettings = await this.getSettings();
    
    // ????????????
    const pathParts = path.split('.');
    let target = currentSettings as any;
    
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (!target[part]) {
        target[part] = {};
      }
      target = target[part];
    }
    
    target[pathParts[pathParts.length - 1]] = value;
    
    await this.saveSettings(currentSettings);
  }

  // ???????
  async getSetting(path: string): Promise<any> {
    const settings = await this.getSettings();
    const pathParts = path.split('.');
    let result = settings as any;
    
    for (const part of pathParts) {
      if (result && typeof result === 'object' && part in result) {
        result = result[part];
      } else {
        return undefined;
      }
    }
    
    return result;
  }
}

// ????
export const settingsService = new SettingsService();
