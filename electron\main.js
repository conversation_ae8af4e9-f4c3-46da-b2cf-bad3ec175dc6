const { app, BrowserWindow, Menu, ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const Database = require('better-sqlite3');
const fs = require('fs');

// 数据库路径
const userDataPath = app.getPath('userData');
const dbPath = path.join(userDataPath, 'xlsp.db');

let mainWindow;
let db;

// 初始化数据库
function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    console.log('用户数据路径:', userDataPath);
    console.log('数据库路径:', dbPath);
    
    // 确保用户数据目录存在
    if (!fs.existsSync(userDataPath)) {
      console.log('创建用户数据目录:', userDataPath);
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    // 连接数据库
    console.log('连接数据库...');
    db = new Database(dbPath);
    
    // 启用外键约束
    db.pragma('foreign_keys = ON');
    
    // 创建表结构
    console.log('创建数据库表...');
    createTables();
    
    console.log('数据库初始化成功:', dbPath);
  } catch (error) {
    console.error('数据库初始化失败:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 创建数据库表
function createTables() {
  const tables = [
    // 来访者表
    `CREATE TABLE IF NOT EXISTS visitors (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      gender TEXT CHECK(gender IN ('男', '女', '其他')),
      age INTEGER,
      phone TEXT UNIQUE,
      email TEXT,
      emergency_contact TEXT,
      emergency_phone TEXT,
      occupation TEXT,
      education TEXT,
      address TEXT,
      notes TEXT,
      status TEXT DEFAULT '活跃' CHECK(status IN ('活跃', '暂停', '完成')),
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 个案表
    `CREATE TABLE IF NOT EXISTS cases (
      id TEXT PRIMARY KEY,
      visitor_id TEXT,
      name TEXT NOT NULL,
      summary TEXT,
      therapy_method TEXT DEFAULT '箱庭疗法',
      selected_sand_tools TEXT,
      last_date TEXT,
      next_date TEXT,
      total INTEGER DEFAULT 0,
      star BOOLEAN DEFAULT FALSE,
      duration INTEGER DEFAULT 50,
      crisis TEXT CHECK(crisis IN ('⚠️', '⚡', '✅')),
      homework TEXT CHECK(homework IN ('📋', '✅', '❌')),
      progress TEXT CHECK(progress IN ('⬆️', '➡️', '⬇️')),
      keywords TEXT,
      supervision TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (visitor_id) REFERENCES visitors (id)
    )`,
    
    // 团体会话表
    `CREATE TABLE IF NOT EXISTS group_sessions (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      therapist_id TEXT,
      therapist_name TEXT,
      max_participants INTEGER DEFAULT 8,
      current_participants INTEGER DEFAULT 0,
      session_type TEXT,
      target_age TEXT,
      duration INTEGER DEFAULT 90,
      frequency TEXT,
      total_sessions INTEGER,
      current_session INTEGER DEFAULT 0,
      start_date TEXT,
      end_date TEXT,
      meeting_time TEXT,
      location TEXT,
      status TEXT DEFAULT '计划中' CHECK(status IN ('计划中', '进行中', '已完成', '已取消')),
      requirements TEXT,
      materials TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 团体参与者表
    `CREATE TABLE IF NOT EXISTS group_participants (
      id TEXT PRIMARY KEY,
      group_session_id TEXT NOT NULL,
      visitor_id TEXT NOT NULL,
      visitor_name TEXT NOT NULL,
      age INTEGER,
      gender TEXT CHECK(gender IN ('男', '女')),
      join_date TEXT NOT NULL,
      status TEXT DEFAULT '进行中' CHECK(status IN ('进行中', '已完成', '已退出', '暂停')),
      attendance_rate REAL DEFAULT 0,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (group_session_id) REFERENCES group_sessions (id),
      FOREIGN KEY (visitor_id) REFERENCES visitors (id)
    )`,

    // 团体活动记录表
    `CREATE TABLE IF NOT EXISTS group_session_records (
      id TEXT PRIMARY KEY,
      group_session_id TEXT NOT NULL,
      session_number INTEGER NOT NULL,
      date TEXT NOT NULL,
      start_time TEXT NOT NULL,
      end_time TEXT NOT NULL,
      actual_duration INTEGER NOT NULL,
      attendees TEXT, -- JSON array of participant IDs
      absentees TEXT, -- JSON array of participant IDs
      theme TEXT,
      objectives TEXT,
      activities TEXT,
      observations TEXT NOT NULL,
      interactions TEXT,
      outcomes TEXT,
      next_plan TEXT,
      therapist_notes TEXT NOT NULL,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (group_session_id) REFERENCES group_sessions (id)
    )`,

    // 团体参与者沙具使用记录表
    `CREATE TABLE IF NOT EXISTS group_participant_sand_tool_usage (
      id TEXT PRIMARY KEY,
      session_record_id TEXT NOT NULL,
      participant_id TEXT NOT NULL,
      participant_name TEXT NOT NULL,
      sand_tool_id TEXT NOT NULL,
      sand_tool_name TEXT NOT NULL,
      usage_order INTEGER NOT NULL,
      placement_position TEXT,
      placement_meaning TEXT,
      interaction_with TEXT, -- JSON array of related sand tool IDs
      participant_expression TEXT,
      therapist_observation TEXT,
      usage_time INTEGER,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (session_record_id) REFERENCES group_session_records (id),
      FOREIGN KEY (participant_id) REFERENCES group_participants (id),
      FOREIGN KEY (sand_tool_id) REFERENCES sand_tools (id)
    )`,

    // 沙具分类表
    `CREATE TABLE IF NOT EXISTS sand_tool_categories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      color TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 沙具表
    `CREATE TABLE IF NOT EXISTS sand_tools (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      category_id TEXT,
      description TEXT,
      material TEXT,
      size TEXT,
      color TEXT,
      quantity INTEGER DEFAULT 1,
      available_quantity INTEGER DEFAULT 1,
      location TEXT,
      purchase_date TEXT,
      price REAL,
      supplier TEXT,
      condition TEXT DEFAULT '良好' CHECK(condition IN ('良好', '一般', '需维修', '报废')),
      image_url TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES sand_tool_categories (id)
    )`,
    
    // 沙具使用记录表
    `CREATE TABLE IF NOT EXISTS sand_tool_usage_records (
      id TEXT PRIMARY KEY,
      sand_tool_id TEXT NOT NULL,
      visitor_id TEXT,
      case_id TEXT,
      session_date TEXT NOT NULL,
      quantity_used INTEGER DEFAULT 1,
      usage_duration INTEGER,
      usage_notes TEXT,
      therapist_notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sand_tool_id) REFERENCES sand_tools (id),
      FOREIGN KEY (visitor_id) REFERENCES visitors (id),
      FOREIGN KEY (case_id) REFERENCES cases (id)
    )`,
    
    // 沙具维护记录表
    `CREATE TABLE IF NOT EXISTS sand_tool_maintenance_records (
      id TEXT PRIMARY KEY,
      sand_tool_id TEXT NOT NULL,
      maintenance_type TEXT CHECK(maintenance_type IN ('清洁', '维修', '更换', '检查')),
      maintenance_date TEXT NOT NULL,
      description TEXT,
      cost REAL,
      performed_by TEXT,
      next_maintenance_date TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sand_tool_id) REFERENCES sand_tools (id)
    )`,
    
    // 房间表
    `CREATE TABLE IF NOT EXISTS rooms (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      capacity INTEGER DEFAULT 1,
      equipment TEXT,
      location TEXT,
      description TEXT,
      is_available BOOLEAN DEFAULT TRUE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 预约表
    `CREATE TABLE IF NOT EXISTS appointments (
      id TEXT PRIMARY KEY,
      visitor_id TEXT,
      case_id TEXT,
      therapist_id TEXT,
      therapist_name TEXT,
      room_id TEXT,
      title TEXT NOT NULL,
      description TEXT,
      appointment_date TEXT NOT NULL,
      start_time TEXT NOT NULL,
      end_time TEXT NOT NULL,
      duration INTEGER DEFAULT 50,
      appointment_type TEXT CHECK(appointment_type IN ('个体咨询', '团体咨询', '沙盘游戏', '心理测评', '其他')),
      status TEXT DEFAULT '已预约' CHECK(status IN ('已预约', '已确认', '进行中', '已完成', '已取消', '未到场')),
      urgency_level TEXT DEFAULT '普通' CHECK(urgency_level IN ('紧急', '重要', '普通')),
      recurring_type TEXT CHECK(recurring_type IN ('无', '每日', '每周', '每月')),
      recurring_end_date TEXT,
      reminder_time INTEGER DEFAULT 30,
      notes TEXT,
      cancellation_reason TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (visitor_id) REFERENCES visitors (id),
      FOREIGN KEY (case_id) REFERENCES cases (id),
      FOREIGN KEY (room_id) REFERENCES rooms (id)
    )`,
    
    // 工作时间表
    `CREATE TABLE IF NOT EXISTS working_hours (
      id TEXT PRIMARY KEY,
      therapist_id TEXT,
      therapist_name TEXT,
      day_of_week INTEGER CHECK(day_of_week BETWEEN 0 AND 6),
      start_time TEXT NOT NULL,
      end_time TEXT NOT NULL,
      is_available BOOLEAN DEFAULT TRUE,
      break_start_time TEXT,
      break_end_time TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 节假日表
    `CREATE TABLE IF NOT EXISTS holidays (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      date TEXT NOT NULL,
      type TEXT CHECK(type IN ('法定节假日', '调休', '特殊假期')),
      is_working_day BOOLEAN DEFAULT FALSE,
      description TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 系统设置表
    `CREATE TABLE IF NOT EXISTS settings (
      id TEXT PRIMARY KEY,
      category TEXT NOT NULL,
      key TEXT NOT NULL,
      value TEXT,
      data_type TEXT CHECK(data_type IN ('string', 'number', 'boolean', 'object', 'array')),
      description TEXT,
      is_system BOOLEAN DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(category, key)
    )`
  ];

  tables.forEach(sql => {
    db.exec(sql);
  });

  // === Help Center Schema (Stage5 Step1) ===
  // 独立执行，避免污染上面数组的可读性
  const helpCenterStatements = [
    `CREATE TABLE IF NOT EXISTS help_meta (key TEXT PRIMARY KEY, value TEXT)`,
    `CREATE TABLE IF NOT EXISTS help_categories (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      ` + '`order`' + ` INTEGER NOT NULL
    )`,
    `CREATE TABLE IF NOT EXISTS help_topics (
      id TEXT PRIMARY KEY,
      id_category TEXT NOT NULL,
      title TEXT NOT NULL,
      summary TEXT,
      ` + '`order`' + ` INTEGER NOT NULL,
      FOREIGN KEY(id_category) REFERENCES help_categories(id)
    )`,
    `CREATE TABLE IF NOT EXISTS help_articles (
      id TEXT PRIMARY KEY,
      id_topic TEXT NOT NULL,
      title TEXT NOT NULL,
      body TEXT NOT NULL,
      tags TEXT,
      ` + '`order`' + ` INTEGER NOT NULL,
      FOREIGN KEY(id_topic) REFERENCES help_topics(id)
    )`,
    `CREATE TABLE IF NOT EXISTS help_steps (
      id TEXT PRIMARY KEY,
      id_article TEXT NOT NULL,
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      type TEXT,
      ` + '`order`' + ` INTEGER NOT NULL,
      FOREIGN KEY(id_article) REFERENCES help_articles(id)
    )`,
    `CREATE TABLE IF NOT EXISTS help_faqs (
      id TEXT PRIMARY KEY,
      id_article TEXT NOT NULL,
      question TEXT NOT NULL,
      answer TEXT NOT NULL,
      ` + '`order`' + ` INTEGER NOT NULL,
      FOREIGN KEY(id_article) REFERENCES help_articles(id)
    )`
  ];
  helpCenterStatements.forEach(sql => db.exec(sql));

  // 初始化 schema_version
  try {
    const metaRow = db.prepare('SELECT value FROM help_meta WHERE key = ?').get('schema_version');
    if (!metaRow) {
      db.prepare('INSERT INTO help_meta(key,value) VALUES (?,?)').run('schema_version','1');
    }
  } catch (err) {
    console.warn('[HelpCenter] 初始化 meta 失败:', err.message);
  }

  // 可选：如果没有任何分类，保持空（使用 mockProvider 供演示）；后续通过迁移或导入填充。

  // === Existing migration logic ===
  try {
    // 检查cases表是否有therapy_method字段，如果没有则添加
    const caseColumns = db.prepare("PRAGMA table_info(cases)").all();
    const hasTherapyMethod = caseColumns.some(col => col.name === 'therapy_method');
    if (!hasTherapyMethod) {
      db.exec('ALTER TABLE cases ADD COLUMN therapy_method TEXT DEFAULT "箱庭疗法"');
      console.log('已添加therapy_method字段到cases表');
    }

    // 检查是否有selected_sand_tools字段
    const hasSelectedSandTools = caseColumns.some(col => col.name === 'selected_sand_tools');
    if (!hasSelectedSandTools) {
      db.exec('ALTER TABLE cases ADD COLUMN selected_sand_tools TEXT');
      console.log('已添加selected_sand_tools字段到cases表');
    }
  } catch (error) {
    console.warn('数据库升级警告:', error.message);
  }
}

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      // 开发期间允许的权限
      webSecurity: isDev ? false : true, // 开发时关闭web安全限制
      allowRunningInsecureContent: isDev ? true : false, // 允许不安全内容
      experimentalFeatures: true, // 启用实验性功能
    },
    icon: path.join(__dirname, '../public/icon.png'),
    titleBarStyle: 'hidden', // 隐藏标题栏但保持窗口拖拽功能
    titleBarOverlay: false,
    show: false,
    roundedCorners: true // 添加窗口圆角边框效果
  });

  // 加载应用
  if (isDev) {
    // 检测开发服务器端口
    const devPort = process.env.DEV_PORT || '5173';
    mainWindow.loadURL(`http://localhost:${devPort}`);
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 处理外部链接 - 在系统默认浏览器中打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 处理导航事件 - 允许外部链接
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    // 如果是外部链接，在系统浏览器中打开
    if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.origin !== 'file://') {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });

  // 设置菜单
  setApplicationMenu();
}

// 设置应用菜单
function setApplicationMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入数据',
          accelerator: 'CmdOrCtrl+I',
          click: () => importData()
        },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: () => exportData()
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '工具',
      submenu: [
        {
          label: '打开系统浏览器',
          accelerator: 'CmdOrCtrl+B',
          click: () => {
            shell.openExternal('https://www.baidu.com');
          }
        },
        {
          label: '清除缓存',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.session.clearCache();
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: '清除缓存',
                message: '缓存已清除'
              });
            }
          }
        },
        { type: 'separator' },
        {
          label: '应用设置',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            // 这里可以打开设置页面
            console.log('打开设置页面');
          }
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '在线帮助',
          click: () => {
            shell.openExternal('https://github.com/');
          }
        },
        { type: 'separator' },
        {
          label: '关于沙盘管理系统',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '沙盘管理系统',
              detail: '专业的心理健康沙盘疗法管理系统\\n版本: 1.0.0\\n\\n© 2024 版权所有'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 导入数据
async function importData() {
  console.log('导入数据功能');
}

// 导出数据
async function exportData() {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: '保存数据文件',
      defaultPath: `xlsp-backup-${new Date().toISOString().split('T')[0]}.json`,
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled) {
      const data = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        visitors: db.prepare('SELECT * FROM visitors').all(),
        cases: db.prepare('SELECT * FROM cases').all(),
        groupSessions: db.prepare('SELECT * FROM group_sessions').all()
      };

      fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));
      
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '导出成功',
        message: '数据导出完成'
      });
    }
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
  }
}

// IPC 处理程序
ipcMain.handle('db-query', async (event, sql, params = []) => {
  try {
    const stmt = db.prepare(sql);
    return stmt.all(params);
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
});

ipcMain.handle('db-run', async (event, sql, params = []) => {
  try {
    const stmt = db.prepare(sql);
    return stmt.run(params);
  } catch (error) {
    console.error('Database run error:', error);
    throw error;
  }
});

ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', (event, name) => {
  return app.getPath(name);
});

// 浏览器相关功能
ipcMain.handle('open-external', async (event, url) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    console.error('Failed to open external URL:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-item-in-folder', async (event, path) => {
  try {
    shell.showItemInFolder(path);
    return { success: true };
  } catch (error) {
    console.error('Failed to show item in folder:', error);
    return { success: false, error: error.message };
  }
});

// 系统通知
ipcMain.handle('show-notification', async (event, title, options) => {
  try {
    if (!Notification.isSupported()) {
      console.warn('系统不支持通知');
      return { success: false, error: '系统不支持通知' };
    }

    const notification = new Notification({
      title,
      body: options.body || '',
      icon: options.icon ? path.join(__dirname, '..', 'public', options.icon) : undefined,
      silent: false,
      urgency: 'critical', // 设置为高优先级
      timeoutType: 'never' // 不自动消失
    });

    // 处理通知点击事件
    notification.on('click', () => {
      // 让主窗口获得焦点
      if (mainWindow) {
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.focus();
        mainWindow.show();
      }
    });

    // 显示通知
    notification.show();

    return { success: true };
  } catch (error) {
    console.error('显示通知失败:', error);
    return { success: false, error: error.message };
  }
});

// 应用事件
app.whenReady().then(() => {
  initDatabase();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (db) {
      db.close();
    }
    app.quit();
  }
});

app.on('before-quit', () => {
  if (db) {
    db.close();
  }
});

// 单实例应用
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
