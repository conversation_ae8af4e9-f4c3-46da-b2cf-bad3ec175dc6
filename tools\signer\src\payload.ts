/**
 * 激活码Payload处理模块
 */

import { toB64u, fromB64u } from './base64url.js';

export interface LicensePayload {
  v: number;    // license schema version
  kv: number;   // key version (密钥轮换)
  d: string;    // device hash (base64url)
  sid: string;  // salt 128bit (base64url)
  ia: number;   // issued at (timestamp)
}

/**
 * 编码payload为base64url
 */
export const encodePayload = (p: LicensePayload): string => {
  return toB64u(Buffer.from(JSON.stringify(p), 'utf8'));
};

/**
 * 解码base64url为payload
 */
export const decodePayload = (b64u: string): LicensePayload => {
  return JSON.parse(fromB64u(b64u).toString('utf8'));
};
