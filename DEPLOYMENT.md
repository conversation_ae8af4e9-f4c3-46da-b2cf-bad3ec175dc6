# 部署指南

本文档提供沙盘管理系统的完整部署指南，包括开发环境设置、生产环境部署、打包发布和运维管理。

## 🚀 快速部署

### 环境要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux (Ubuntu 18.04+)

### 开发环境部署

#### 1. 克隆项目
```bash
git clone <repository-url>
cd xlsp2
```

#### 2. 安装依赖
```bash
npm install
```

#### 3. 启动开发服务器
```bash
# 启动完整开发环境（Electron + React）
npm start

# 或仅启动前端开发服务器
npm run dev

# 或启动Electron开发模式
npm run electron-dev
```

#### 4. 访问应用
- 开发服务器: http://localhost:5173
- Electron应用: 自动启动桌面窗口

### 生产环境部署

#### 1. 构建应用
```bash
# 构建前端代码
npm run build

# 构建Electron应用
npm run dist
```

#### 2. 打包发布
```bash
# 打包为可分发文件
npm run dist

# 仅构建不打包
npm run pack
```

## 📦 打包配置

### Electron Builder配置
```json
{
  "build": {
    "appId": "com.company.xlsp",
    "productName": "沙盘管理系统",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "package.json"
    ],
    "win": {
      "target": "nsis",
      "icon": "public/icon.ico",
      "publish": ["github"]
    },
    "mac": {
      "target": "dmg",
      "icon": "public/icon.png",
      "category": "public.app-category.productivity"
    },
    "linux": {
      "target": "AppImage",
      "icon": "public/icon.png",
      "category": "Office"
    }
  }
}
```

### 平台特定配置

#### Windows配置
```json
{
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64", "ia32"]
      }
    ],
    "signingHashAlgorithms": ["sha256"],
    "certificateFile": "./cert.pfx",
    "certificatePassword": ""
  }
}
```

#### macOS配置
```json
{
  "mac": {
    "target": [
      {
        "target": "dmg",
        "arch": ["x64", "arm64"]
      }
    ],
    "category": "public.app-category.productivity",
    "entitlements": "./entitlements.mac.plist",
    "entitlementsInherit": "./entitlements.mac.plist"
  }
}
```

#### Linux配置
```json
{
  "linux": {
    "target": [
      {
        "target": "AppImage",
        "arch": ["x64"]
      }
    ],
    "category": "Office",
    "maintainer": "<EMAIL>"
  }
}
```

## 🗄️ 数据库部署

### 数据库初始化

#### 自动初始化
应用首次启动时自动创建数据库：
```typescript
// 数据库初始化脚本
const initDatabase = async () => {
  const dbPath = path.join(app.getPath('userData'), '沙盘管理系统', 'xlsp.db');
  
  // 确保目录存在
  await fs.ensureDir(path.dirname(dbPath));
  
  // 创建数据库连接
  const db = new sqlite3.Database(dbPath);
  
  // 执行初始化SQL
  await execSqlFile(db, 'schema.sql');
  await execSqlFile(db, 'seed.sql');
  
  return db;
};
```

#### 手动初始化
如果需要手动初始化数据库：
```bash
# 运行数据库初始化脚本
npm run db:init

# 或运行数据迁移
npm run db:migrate
```

### 数据库迁移

#### 迁移脚本示例
```javascript
// migrations/001-initial-schema.js
export const up = async (db) => {
  await db.run(`
    CREATE TABLE visitors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
};

export const down = async (db) => {
  await db.run('DROP TABLE visitors');
};
```

#### 运行迁移
```bash
# 运行所有待处理迁移
npm run db:migrate

# 回滚最近一次迁移
npm run db:rollback

# 查看迁移状态
npm run db:status
```

## 🔧 环境配置

### 环境变量
创建 `.env` 文件：
```env
# 应用配置
VITE_APP_NAME=沙盘管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# 数据库配置
DATABASE_PATH=./data/xlsp.db
DATABASE_ENCRYPTION_KEY=your-encryption-key

# 功能开关
FEATURE_EXPORT_ENABLED=true
FEATURE_IMPORT_ENABLED=true
FEATURE_BACKUP_ENABLED=true
```

### 环境特定配置

#### 开发环境
```env
# 开发环境配置
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:5173
DEBUG=true
```

#### 生产环境
```env
# 生产环境配置
NODE_ENV=production
VITE_API_BASE_URL=./
DEBUG=false
```

#### 测试环境
```env
# 测试环境配置
NODE_ENV=test
VITE_API_BASE_URL=http://localhost:3001
DEBUG=true
```

## 🚢 发布流程

### 版本管理

#### 版本号规范
遵循语义化版本控制：
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

#### 发布脚本
```bash
#!/bin/bash
# 发布脚本

# 1. 更新版本号
npm version patch

# 2. 构建应用
npm run build

# 3. 运行测试
npm test

# 4. 打包应用
npm run dist

# 5. 创建发布标签
git tag v$(node -p "require('./package.json').version")

# 6. 推送到仓库
git push origin main --tags
```

### 自动更新

#### 更新配置
```javascript
// 自动更新配置
autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'your-username',
  repo: 'xlsp2',
  releaseType: 'release'
});

// 检查更新
autoUpdater.checkForUpdates();

// 监听更新事件
autoUpdater.on('update-available', () => {
  // 通知用户有更新可用
});

autoUpdater.on('update-downloaded', () => {
  // 提示用户安装更新
});
```

## 🛡️ 安全部署

### 代码签名

#### Windows代码签名
```bash
# 使用SignTool进行代码签名
signtool sign /f certificate.pfx /p password /t http://timestamp.digicert.com application.exe
```

#### macOS代码签名
```bash
# 使用codesign进行代码签名
codesign --deep --force --verify --verbose --sign "Developer ID Application: Your Name (TEAM_ID)" application.app
```

### 安全加固

#### 应用程序加固
```javascript
// 主进程安全配置
const mainWindow = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    enableRemoteModule: false,
    preload: path.join(__dirname, 'preload.js')
  }
});
```

#### 数据库安全
```typescript
// 数据库加密
const encryptDatabase = async (dbPath: string, key: string) => {
  // 使用SQLCipher或类似库加密数据库
  const encryptedDb = new SQLCipher.Database(dbPath);
  await encryptedDb.run(`PRAGMA key = '${key}'`);
  return encryptedDb;
};
```

## 📊 监控和日志

### 应用监控

#### 性能监控
```typescript
// 性能指标收集
const collectMetrics = () => {
  return {
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    uptime: process.uptime(),
    timestamp: Date.now()
  };
};

// 定期收集指标
setInterval(() => {
  const metrics = collectMetrics();
  // 发送到监控服务
}, 60000);
```

#### 错误监控
```typescript
// 错误处理中间件
process.on('uncaughtException', (error) => {
  console.error('未捕获异常:', error);
  // 发送到错误监控服务
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  // 发送到错误监控服务
});
```

### 日志管理

#### 日志配置
```javascript
// 日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});
```

#### 日志轮转
```bash
# 使用logrotate管理日志
/var/log/xlsp/*.log {
  daily
  missingok
  rotate 30
  compress
  delaycompress
  notifempty
  copytruncate
}
```

## 🔄 备份和恢复

### 数据备份

#### 自动备份
```typescript
// 自动备份脚本
const autoBackup = async () => {
  const backupPath = path.join(backupDir, `backup-${Date.now()}.json`);
  
  // 导出所有数据
  const data = await exportAllData();
  
  // 保存备份文件
  await fs.writeJson(backupPath, data, { spaces: 2 });
  
  // 清理旧备份
  await cleanupOldBackups(backupDir);
};

// 每天凌晨执行备份
cron.schedule('0 0 * * *', autoBackup);
```

#### 手动备份
```bash
# 手动创建备份
npm run db:backup

# 恢复备份
npm run db:restore --backup=backup-1234567890.json
```

### 灾难恢复

#### 恢复流程
1. **停止应用**: 确保应用已完全停止
2. **恢复备份**: 使用最新备份文件恢复数据
3. **验证数据**: 检查数据完整性和一致性
4. **重启应用**: 正常启动应用程序

#### 恢复脚本
```bash
#!/bin/bash
# 灾难恢复脚本

# 1. 停止应用
pkill -f "沙盘管理系统"

# 2. 恢复最新备份
LATEST_BACKUP=$(ls -t backups/ | head -1)
npm run db:restore --backup="backups/$LATEST_BACKUP"

# 3. 启动应用
npm start
```

## 📋 部署检查清单

### 预部署检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 版本号已更新
- [ ] 更新日志已填写
- [ ] 文档已更新

### 部署检查
- [ ] 构建成功
- [ ] 打包完成
- [ ] 安装包测试通过
- [ ] 数字签名有效

### 后部署检查
- [ ] 应用正常启动
- [ ] 数据库连接正常
- [ ] 功能测试通过
- [ ] 性能指标正常

## 📞 故障排除

### 常见问题

#### 构建失败
```bash
# 清理缓存并重新安装依赖
npm run clean
rm -rf node_modules
npm install
```

#### 数据库连接失败
```bash
# 检查数据库文件权限
chmod 644 data/xlsp.db

# 检查磁盘空间
df -h
```

#### 应用无法启动
```bash
# 查看日志文件
cat ~/Library/Logs/沙盘管理系统/main.log

# 重置用户数据
rm -rf ~/Library/Application\ Support/沙盘管理系统
```

### 获取支持
- 查看 [帮助文档](../docs/)
- 提交 [Issue](<repository-url>/issues)
- 联系技术支持团队

---
*本文档基于企业级应用程序部署最佳实践，确保应用程序的可靠性和可维护性*