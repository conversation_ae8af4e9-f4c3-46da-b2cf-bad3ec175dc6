// 通知功能测试脚本
const { systemNotificationService } = require('./dist-electron/main.js');

console.log('测试系统通知功能...');

// 测试系统通知服务
try {
  // 测试更新通知
  console.log('测试更新通知...');
  systemNotificationService.testNotification('update');
  
  // 测试错误通知
  console.log('测试错误通知...');
  systemNotificationService.testNotification('error');
  
  // 测试维护通知
  console.log('测试维护通知...');
  systemNotificationService.testNotification('maintenance');
  
  console.log('所有通知测试完成！');
} catch (error) {
  console.error('通知测试失败:', error.message);
}

// 测试提醒服务
const { reminderService } = require('./dist-electron/main.js');

console.log('测试预约提醒功能...');

// 创建一个测试预约
const testAppointment = {
  id: 'test-123',
  visitorName: '测试用户',
  subject: '测试预约',
  date: new Date().toISOString().split('T')[0],
  startTime: new Date(Date.now() + 2 * 60 * 1000).toLocaleTimeString('zh-CN', { hour12: false }),
  reminderEnabled: true,
  reminderTime: 1 // 1分钟后提醒
};

try {
  // 设置提醒
  reminderService.setReminder(testAppointment);
  console.log('预约提醒设置成功！');
  
  // 检查活动提醒数量
  const activeCount = reminderService.getActiveRemindersCount();
  console.log(`当前活动提醒数量: ${activeCount}`);
  
} catch (error) {
  console.error('提醒测试失败:', error.message);
}