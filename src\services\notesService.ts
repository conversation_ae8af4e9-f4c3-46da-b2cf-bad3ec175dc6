export interface QuickNote {
  id?: number;
  content: string;
  date: string; // YYYY-MM-DD 格式
  pinned: boolean;
  createdAt: string;
  updatedAt: string;
}

export class NotesService {
  
  // 检查环境
  private isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI?.isElectron;
  }

  // 数据库查询封装
  private async query(sql: string, params: unknown[] = []): Promise<unknown[]> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbQuery(sql, params);
    } catch (error) {
      console.error('数据库查询失败:', error);
      return [];
    }
  }

  private async run(sql: string, params: unknown[] = []): Promise<{ changes: number; lastInsertRowid: number | null }> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      return { changes: 0, lastInsertRowid: null };
    }
  }

  // 添加快速备注
  async addQuickNote(noteData: { content: string; date: string; pinned?: boolean }): Promise<QuickNote | null> {
    try {
      const now = new Date().toISOString();
      const sql = `
        INSERT INTO quick_notes (content, date, pinned, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const params = [
        noteData.content,
        noteData.date,
        noteData.pinned ? 1 : 0,
        now,
        now
      ];

      const result = await this.run(sql, params);
      
      if (result.lastInsertRowid) {
        return await this.getQuickNote(result.lastInsertRowid);
      }
      
      return null;
    } catch (error) {
      console.error('添加快速备注失败:', error);
      return null;
    }
  }

  // 获取单个快速备注
  async getQuickNote(id: number): Promise<QuickNote | null> {
    try {
      const rows = await this.query('SELECT * FROM quick_notes WHERE id = ?', [id]);
      return rows.length > 0 ? this.mapRowToNote(rows[0] as { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }) : null;
    } catch (error) {
      console.error('获取快速备注失败:', error);
      return null;
    }
  }

  // 获取今日快速备注
  async getTodayQuickNotes(date: string): Promise<QuickNote[]> {
    try {
      const sql = `
        SELECT * FROM quick_notes 
        WHERE date = ? 
        ORDER BY created_at DESC
      `;
      const rows = await this.query(sql, [date]);
      return rows.map((row) => this.mapRowToNote(row as { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }));
    } catch (error) {
      console.error('获取今日快速备注失败:', error);
      return [];
    }
  }

  // 获取最新的快速备注
  async getLatestQuickNote(date: string): Promise<QuickNote | null> {
    try {
      const sql = `
        SELECT * FROM quick_notes 
        WHERE date = ? 
        ORDER BY created_at DESC 
        LIMIT 1
      `;
      const rows = await this.query(sql, [date]);
      return rows.length > 0 ? this.mapRowToNote(rows[0] as { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }) : null;
    } catch (error) {
      console.error('获取最新快速备注失败:', error);
      return null;
    }
  }

  // 更新快速备注
  async updateQuickNote(id: number, updates: Partial<QuickNote>): Promise<QuickNote | null> {
    try {
      const existingNote = await this.getQuickNote(id);
      if (!existingNote) return null;

      const updatedAt = new Date().toISOString();
      const sql = `
        UPDATE quick_notes 
        SET content = ?, pinned = ?, updated_at = ?
        WHERE id = ?
      `;
      
      const params = [
        updates.content !== undefined ? updates.content : existingNote.content,
        updates.pinned !== undefined ? (updates.pinned ? 1 : 0) : (existingNote.pinned ? 1 : 0),
        updatedAt,
        id
      ];

      await this.run(sql, params);
      return await this.getQuickNote(id);
    } catch (error) {
      console.error('更新快速备注失败:', error);
      return null;
    }
  }

  // 删除快速备注
  async deleteQuickNote(id: number): Promise<boolean> {
    try {
      await this.run('DELETE FROM quick_notes WHERE id = ?', [id]);
      return true;
    } catch (error) {
      console.error('删除快速备注失败:', error);
      return false;
    }
  }

  // 获取指定日期范围的备注
  async getQuickNotesByDateRange(startDate: string, endDate: string): Promise<QuickNote[]> {
    try {
      const sql = `
        SELECT * FROM quick_notes 
        WHERE date >= ? AND date <= ? 
        ORDER BY date DESC, created_at DESC
      `;
      const rows = await this.query(sql, [startDate, endDate]);
      return rows.map((row) => this.mapRowToNote(row as { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }));
    } catch (error) {
      console.error('获取日期范围备注失败:', error);
      return [];
    }
  }

  // 搜索备注
  async searchQuickNotes(query: string): Promise<QuickNote[]> {
    try {
      const searchTerm = `%${query.toLowerCase()}%`;
      const sql = `
        SELECT * FROM quick_notes 
        WHERE LOWER(content) LIKE ?
        ORDER BY created_at DESC
      `;
      const rows = await this.query(sql, [searchTerm]);
      return rows.map((row) => this.mapRowToNote(row as { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }));
    } catch (error) {
      console.error('搜索备注失败:', error);
      return [];
    }
  }

  // 清理旧备注（超过指定天数的备注）
  async cleanupOldNotes(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

      const result = await this.run(
        'DELETE FROM quick_notes WHERE date < ? AND pinned = 0',
        [cutoffDateStr]
      );
      
      console.log(`清理了 ${result.changes} 条旧备注`);
      return result.changes || 0;
    } catch (error) {
      console.error('清理旧备注失败:', error);
      return 0;
    }
  }

  // 数据映射方法
  private mapRowToNote(row: { id: number; content: string; date: string; pinned: number; created_at: string; updated_at: string }): QuickNote {
    return {
      id: row.id,
      content: row.content,
      date: row.date,
      pinned: Boolean(row.pinned),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}

// 导出实例
export const notesService = new NotesService();
