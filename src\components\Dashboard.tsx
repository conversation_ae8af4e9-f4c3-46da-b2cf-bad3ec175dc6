import React, { useState, useEffect } from 'react';
import './Dashboard.css';

// 动态导入每日短语组件
const DailyPhrase = React.lazy(() => import('./daily-workspace/DailyPhrase'));
const MoodSelection = React.lazy(() => import('./daily-workspace/MoodSelection'));





interface DashboardProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help') => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNavigate }) => {
  // 使用useState创建状态来存储当前时间
  const [currentDate, setCurrentDate] = useState(new Date());
  
  // 轮播图状态管理
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  
  // 轮播图数据（文案保留）
  const carouselSlides = [
    {
      id: 1,
      image: '/1.jpg',
      title: '心理健康服务',
      subtitle: '专业的沙盘治疗管理平台',
      description: '为心理咨询师提供全面的沙盘治疗工具和管理功能'
    },
    {
      id: 2,
      image: '/2.jpg',
      title: '智能管理系统',
      subtitle: '高效的来访者与个案管理',
      description: '完整的预约调度、档案管理和数据统计功能'
    },
    {
      id: 3,
      image: '/3.jpg',
      title: '专业沙具库',
      subtitle: '丰富的沙具分类与管理',
      description: '支持沙具分类、库存管理和使用记录追踪'
    },
    {
      id: 4,
      image: '/4.jpg',
      title: '团体辅导室',
      subtitle: '共创支持与成长空间',
      description: '用于团体心理辅导与团沙活动，支持多维互动与即时记录'
    },
    {
      id: 5,
      image: '/5.jpg',
      title: '生物反馈放松室',
      subtitle: '科学放松 量化减压',
      description: '整合生物反馈与放松训练数据，辅助情绪调节与疗效评估'
    },
    {
      id: 6,
      image: '/6.jpg',
      title: '心理测评室',
      subtitle: '标准化评估体系',
      description: '统一管理测评工具、量表执行与结果追踪'
    },
    {
      id: 7,
      image: '/7.jpg',
      title: '自助接待室',
      subtitle: '来访者自助体验',
      description: '支持签到、初筛与信息收集，提升接待效率'
    }
  ];
  
  // 企微客服链接处理（保留）
  const handleLearnMore = () => {
    window.open('https://work.weixin.qq.com/kfid/kfcd145b2cfdee55d77', '_blank');
  };

  // 轮播图控制函数
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselSlides.length) % carouselSlides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const handleCarouselMouseEnter = () => {
    setIsAutoPlaying(false);
  };

  const handleCarouselMouseLeave = () => {
    setIsAutoPlaying(true);
  };

  // 避免未使用警告（临时保留数据结构）
  console.log('轮播数据已保留:', carouselSlides.length, '条记录');
  console.log('客服链接处理器已保留:', typeof handleLearnMore);
  

  
  const formatDate = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
  };

  // 根据当前时间返回合适的问候语
  const getGreeting = (date: Date) => {
    const hour = date.getHours();
    if (hour >= 5 && hour < 12) {
      return '早上好';
    } else if (hour >= 12 && hour < 14) {
      return '中午好';
    } else if (hour >= 14 && hour < 18) {
      return '下午好';
    } else if (hour >= 18 && hour < 22) {
      return '晚上好';
    } else {
      return '夜深了';
    }
  };



  // 使用useEffect设置定时器，每秒更新当前时间
  useEffect(() => {
    // 设置每秒更新一次时间的定时器
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 1000);

    // 清理函数，组件卸载时清除定时器
    return () => clearInterval(timer);
  }, []);

  // 轮播图自动播放
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const autoPlayTimer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
    }, 5000); // 5秒切换一次

    return () => clearInterval(autoPlayTimer);
  }, [isAutoPlaying, carouselSlides.length]);

  return (
    <div className="dashboard">
      {/* 欢迎横幅 */}
      <div className="welcome-banner">
        <div className="welcome-content">
          <div className="welcome-text">
            <h1>💛 {getGreeting(currentDate)}，欢迎回来！</h1>
            <p>今天是 {formatDate(currentDate)}</p>
            <p>我们一起为心理健康事业贡献力量 💪</p>
          </div>
          <div className="welcome-icon">
            <img 
              src="/logo.png" 
              alt="沙盘管理系统Logo" 
              className="logo-image"
            />
          </div>
        </div>
      </div>

      {/* 轮播图区域 */}
      <div className="carousel-section" 
           onMouseEnter={handleCarouselMouseEnter}
           onMouseLeave={handleCarouselMouseLeave}>
        <div className="carousel-container">
          <div className="carousel-slides" style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
            {carouselSlides.map((slide) => (
              <div key={slide.id} className="carousel-slide">
                <div 
                  className="carousel-image"
                  style={{ backgroundImage: `url(${slide.image})` }}
                />
                <div className="carousel-content">
                  <h2 className="carousel-title">{slide.title}</h2>
                  <h3 className="carousel-subtitle">{slide.subtitle}</h3>
                  <p className="carousel-description">{slide.description}</p>
                  <button className="carousel-button" onClick={handleLearnMore}>
                    了解更多
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          {/* 左右箭头 */}
          <button className="carousel-arrow carousel-arrow-left" onClick={prevSlide} aria-label="上一张">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <button className="carousel-arrow carousel-arrow-right" onClick={nextSlide} aria-label="下一张">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          
          {/* 指示器 */}
          <div className="carousel-indicators">
            {carouselSlides.map((_, index) => (
              <button
                key={index}
                className={`carousel-indicator ${index === currentSlide ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
                aria-label={`跳转到第${index + 1}张幻灯片`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 每日工作台区域 - 1x2布局 */}
      <div className="daily-workspace-section">
        <div className="daily-workspace-grid">
          <React.Suspense fallback={<div className="daily-workspace-loading">正在加载每日短语...</div>}>
            <DailyPhrase onNavigate={onNavigate} />
          </React.Suspense>
          
          <React.Suspense fallback={<div className="daily-workspace-loading">正在加载心情选择...</div>}>
            <MoodSelection onNavigate={onNavigate} />
          </React.Suspense>
        </div>
      </div>

    </div>
  );
};

export default Dashboard;
