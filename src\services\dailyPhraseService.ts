import { sqliteDataManager } from './sqliteDataManager';
import type { DailyPhrase, DailyPhraseStats } from '../types/dailyPhrase';

class DailyPhraseService {
  
  // 环境检查
  private isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI?.isElectron;
  }

  // 获取模拟数据（用于浏览器环境）
  private getMockPhrases(): DailyPhrase[] {
    return [
      {
        id: 'mock_1',
        chinese: "今天也要元气满满呀！",
        pinyin: "J<PERSON><PERSON>ān yě yào yuánqì mǎnmǎn ya!",
        english: "Today, let's be full of energy too!",
        category: "积极向上",
        description: "充满活力的正能量短语",
        isActive: true,
        usageCount: 0,
        tags: ["元气", "活力", "正能量"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'mock_2',
        chinese: "每一天都是新的开始",
        pinyin: "<PERSON>ě<PERSON> yītiān dōu shì xīn de kāishǐ",
        english: "Every day is a new beginning",
        category: "积极向上",
        description: "鼓励人们把握每一天的机会",
        isActive: true,
        usageCount: 0,
        tags: ["新开始", "希望", "机会"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'mock_3',
        chinese: "慢慢来，比较快",
        pinyin: "Mànmàn lái, bǐjiào kuài",
        english: "Take it slow, it's actually faster",
        category: "生活智慧",
        description: "急躁反而会事倍功半",
        isActive: true,
        usageCount: 0,
        tags: ["耐心", "智慧", "节奏"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'mock_4',
        chinese: "愿你被这个世界温柔以待",
        pinyin: "Yuàn nǐ bèi zhège shìjiè wēnróu yǐ dài",
        english: "May you be treated gently by this world",
        category: "温暖治愈",
        description: "对他人的美好祝愿",
        isActive: true,
        usageCount: 0,
        tags: ["温柔", "祝福", "美好"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  // 获取所有短语
  async getAllPhrases(): Promise<DailyPhrase[]> {
    if (!this.isElectronEnvironment()) {
      return this.getMockPhrases();
    }
    
    const sql = `
      SELECT * FROM daily_phrases 
      WHERE is_active = 1 
      ORDER BY created_at DESC
    `;
    const rows = await sqliteDataManager.executeQuery(sql);
    return rows.map(row => this.mapRowToPhrase(row as Record<string, unknown>));
  }

  // 根据分类获取短语
  async getPhrasesByCategory(category: string): Promise<DailyPhrase[]> {
    const sql = `
      SELECT * FROM daily_phrases 
      WHERE category = ? AND is_active = 1 
      ORDER BY usage_count ASC, RANDOM()
    `;
    const rows = await sqliteDataManager.executeQuery(sql, [category]);
    return rows.map(row => this.mapRowToPhrase(row as Record<string, unknown>));
  }

  // 获取今日短语（优先显示使用次数少的）
  async getTodayPhrase(): Promise<DailyPhrase | null> {
    if (!this.isElectronEnvironment()) {
      const mockPhrases = this.getMockPhrases();
      const randomIndex = Math.floor(Math.random() * mockPhrases.length);
      return mockPhrases[randomIndex];
    }
    
    const today = new Date().toISOString().split('T')[0];
    
    // 先查看是否已经为今天选择了短语
    let sql = `
      SELECT * FROM daily_phrases 
      WHERE DATE(last_used) = ? AND is_active = 1
      ORDER BY last_used DESC LIMIT 1
    `;
    let rows = await sqliteDataManager.executeQuery(sql, [today]);
    
    if (rows.length > 0) {
      return this.mapRowToPhrase(rows[0] as Record<string, unknown>);
    }

    // 如果今天还没有选择短语，选择使用次数最少的
    sql = `
      SELECT * FROM daily_phrases 
      WHERE is_active = 1 
      ORDER BY usage_count ASC, RANDOM() 
      LIMIT 1
    `;
    rows = await sqliteDataManager.executeQuery(sql);
    
    if (rows.length > 0) {
      const phrase = this.mapRowToPhrase(rows[0] as Record<string, unknown>);
      // 更新使用记录
      await this.markPhraseAsUsed(phrase.id);
      return phrase;
    }

    return null;
  }

  // 获取随机短语
  async getRandomPhrase(): Promise<DailyPhrase | null> {
    if (!this.isElectronEnvironment()) {
      const mockPhrases = this.getMockPhrases();
      const randomIndex = Math.floor(Math.random() * mockPhrases.length);
      return mockPhrases[randomIndex];
    }
    
    const sql = `
      SELECT * FROM daily_phrases 
      WHERE is_active = 1 
      ORDER BY RANDOM() 
      LIMIT 1
    `;
    const rows = await sqliteDataManager.executeQuery(sql);
    
    if (rows.length > 0) {
      const phrase = this.mapRowToPhrase(rows[0] as Record<string, unknown>);
      await this.markPhraseAsUsed(phrase.id);
      return phrase;
    }
    
    return null;
  }

  // 标记短语为已使用
  async markPhraseAsUsed(id: string): Promise<void> {
    if (!this.isElectronEnvironment()) {
      console.log('浏览器环境：模拟标记短语使用', id);
      return;
    }
    
    const sql = `
      UPDATE daily_phrases 
      SET usage_count = usage_count + 1, 
          last_used = datetime('now'), 
          updated_at = datetime('now')
      WHERE id = ?
    `;
    await sqliteDataManager.executeRun(sql, [id]);
  }

  // 保存短语
  async savePhrase(phrase: DailyPhrase): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO daily_phrases (
        id, chinese, pinyin, english, category, description, source,
        tags, is_active, usage_count, last_used, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      phrase.id,
      phrase.chinese,
      phrase.pinyin,
      phrase.english,
      phrase.category,
      phrase.description || null,
      phrase.source || null,
      JSON.stringify(phrase.tags || []),
      phrase.isActive ? 1 : 0,
      phrase.usageCount,
      phrase.lastUsed || null,
      phrase.createdAt,
      phrase.updatedAt
    ];

    await sqliteDataManager.executeRun(sql, params);
  }

  // 删除短语
  async deletePhrase(id: string): Promise<void> {
    const sql = 'DELETE FROM daily_phrases WHERE id = ?';
    await sqliteDataManager.executeRun(sql, [id]);
  }

  // 获取短语统计
  async getPhraseStats(): Promise<DailyPhraseStats> {
    const totalSql = 'SELECT COUNT(*) as count FROM daily_phrases WHERE is_active = 1';
    const totalRows = await sqliteDataManager.executeQuery(totalSql);
    const totalPhrases = (totalRows[0] as any)?.count || 0;

    const todayPhrase = await this.getTodayPhrase();

    const categorySql = `
      SELECT category, COUNT(*) as count 
      FROM daily_phrases 
      WHERE is_active = 1 
      GROUP BY category 
      ORDER BY count DESC 
      LIMIT 1
    `;
    const categoryRows = await sqliteDataManager.executeQuery(categorySql);
    const popularCategory = (categoryRows[0] as any)?.category || '积极向上';

    const mostUsedSql = `
      SELECT * FROM daily_phrases 
      WHERE is_active = 1 
      ORDER BY usage_count DESC 
      LIMIT 1
    `;
    const mostUsedRows = await sqliteDataManager.executeQuery(mostUsedSql);
    const mostUsedPhrase = mostUsedRows.length > 0 ? this.mapRowToPhrase(mostUsedRows[0] as Record<string, unknown>) : undefined;

    return {
      totalPhrases,
      todayPhrase: todayPhrase || undefined,
      popularCategory,
      mostUsedPhrase
    };
  }

  // 初始化默认短语数据
  async initializeDefaultPhrases(): Promise<void> {
    if (!this.isElectronEnvironment()) {
      console.log('浏览器环境：跳过短语数据初始化');
      return;
    }

    // 确保表存在
    await sqliteDataManager.executeRun(`CREATE TABLE IF NOT EXISTS daily_phrases (
      id TEXT PRIMARY KEY,
      chinese TEXT NOT NULL,
      pinyin TEXT,
      english TEXT,
      category TEXT,
      description TEXT,
      source TEXT,
      tags TEXT,
      is_active INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`);

    const existingCountRows = await sqliteDataManager.executeQuery('SELECT COUNT(*) as cnt FROM daily_phrases');
    const existingCount = (existingCountRows[0] as any)?.cnt || 0;
    if (existingCount >= 1000) {
      console.log('已有足够的短语数据，跳过初始化');
      return;
    }
    
    // 导入3年短语数据
    const { generateThreeYearPhrases } = await import('../data/dailyPhrasesData');
    const defaultPhrases = generateThreeYearPhrases();

    for (const phraseData of defaultPhrases) {
      const phrase: DailyPhrase = {
        ...phraseData,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      try {
        await this.savePhrase(phrase);
      } catch (error) {
        console.error('初始化短语失败:', error);
      }
    }
    
    console.log(`成功初始化 ${defaultPhrases.length} 条每日短语数据`);
  }

  // 映射数据库行到短语对象
  private mapRowToPhrase(row: Record<string, unknown>): DailyPhrase {
    return {
      id: row.id as string,
      chinese: row.chinese as string,
      pinyin: row.pinyin as string,
      english: row.english as string,
      category: row.category as string,
      description: row.description as string || undefined,
      source: row.source as string || undefined,
      tags: row.tags ? JSON.parse(row.tags as string) : [],
      isActive: Boolean(row.is_active),
      usageCount: row.usage_count as number || 0,
      lastUsed: row.last_used as string || undefined,
      createdAt: row.created_at as string,
      updatedAt: row.updated_at as string
    };
  }

  // 生成唯一ID
  private generateId(): string {
    return 'phrase_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export const dailyPhraseService = new DailyPhraseService();
