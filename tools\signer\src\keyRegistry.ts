/**
 * 密钥注册表 - 支持多公钥轮换
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface KeyMeta {
  activeKeyVersion: number;
}

export class KeyRegistry {
  private static readonly META_FILE = join('keys', 'metadata.json');
  
  /**
   * 获取当前活跃的密钥版本
   */
  public static getActiveKeyVersion(): number {
    if (!existsSync(this.META_FILE)) {
      return 1; // 默认版本
    }
    
    try {
      const meta: KeyMeta = JSON.parse(readFileSync(this.META_FILE, 'utf8'));
      return meta.activeKeyVersion;
    } catch {
      return 1;
    }
  }
  
  /**
   * 设置活跃的密钥版本
   */
  public static setActiveKeyVersion(version: number): void {
    const meta: KeyMeta = { activeKeyVersion: version };
    writeFileSync(this.META_FILE, JSON.stringify(meta, null, 2));
    console.log(`活跃密钥版本已设置为: ${version}`);
  }
}

export default KeyRegistry;
