# API 参考文档

本文档提供沙盘管理系统的完整API参考，包括主进程IPC接口、数据服务接口和组件接口。

## 📋 API架构概览

### API层次结构
```
主进程API (IPC) → 数据服务API → 组件API → UI
```

### API分类
- **数据库API** - SQLite数据库操作接口
- **文件API** - 文件导入导出操作接口
- **系统API** - 原生系统功能接口
- **业务API** - 业务逻辑服务接口

## 🔌 IPC通信接口

### 数据库操作接口

#### query - 执行查询语句
```typescript
/**
 * 执行SQL查询语句
 * @param sql SQL查询语句
 * @param params 查询参数
 * @returns 查询结果数组
 */
interface QueryParams {
  sql: string;
  params?: any[];
}

// 调用示例
const result = await window.electronAPI.query({
  sql: 'SELECT * FROM visitors WHERE id = ?',
  params: [1]
});
```

#### run - 执行更新语句
```typescript
/**
 * 执行SQL更新语句
 * @param sql SQL更新语句
 * @param params 更新参数
 * @returns 影响的行数
 */
interface RunParams {
  sql: string;
  params?: any[];
}

// 调用示例
const result = await window.electronAPI.run({
  sql: 'UPDATE visitors SET name = ? WHERE id = ?',
  params: ['张三', 1]
});
```

#### transaction - 执行事务
```typescript
/**
 * 执行事务操作
 * @param operations 事务操作数组
 * @returns 事务执行结果
 */
interface TransactionOperation {
  sql: string;
  params?: any[];
}

// 调用示例
const result = await window.electronAPI.transaction([
  {
    sql: 'INSERT INTO visitors (name, phone) VALUES (?, ?)',
    params: ['李四', '13800138000']
  },
  {
    sql: 'INSERT INTO cases (visitor_id, title) VALUES (?, ?)',
    params: [lastInsertId, '初次咨询']
  }
]);
```

### 文件操作接口

#### exportData - 导出数据
```typescript
/**
 * 导出数据到文件
 * @param data 要导出的数据
 * @param format 导出格式
 * @param options 导出选项
 * @returns 导出的文件Blob
 */
interface ExportOptions {
  format: 'csv' | 'docx' | 'pdf' | 'json';
  filename?: string;
  includeHeaders?: boolean;
}

// 调用示例
const blob = await window.electronAPI.exportData({
  data: caseData,
  format: 'pdf',
  filename: '个案报告.pdf'
});
```

#### importData - 导入数据
```typescript
/**
 * 从文件导入数据
 * @param file 导入的文件
 * @param format 文件格式
 * @returns 导入结果
 */
interface ImportResult {
  success: boolean;
  count: number;
  errors?: string[];
}

// 调用示例
const result = await window.electronAPI.importData({
  file: selectedFile,
  format: 'csv'
});
```

### 系统功能接口

#### getAppInfo - 获取应用信息
```typescript
/**
 * 获取应用程序信息
 * @returns 应用信息对象
 */
interface AppInfo {
  version: string;
  platform: string;
  userDataPath: string;
  isPackaged: boolean;
}

// 调用示例
const appInfo = await window.electronAPI.getAppInfo();
```

#### showDialog - 显示系统对话框
```typescript
/**
 * 显示系统对话框
 * @param options 对话框选项
 * @returns 对话框结果
 */
interface DialogOptions {
  type: 'info' | 'warning' | 'error' | 'question';
  title: string;
  message: string;
  buttons?: string[];
}

// 调用示例
const result = await window.electronAPI.showDialog({
  type: 'question',
  title: '确认删除',
  message: '确定要删除这个来访者记录吗？',
  buttons: ['取消', '确定']
});
```

## 📊 数据服务接口

### VisitorService - 来访者服务

#### getVisitors - 获取来访者列表
```typescript
/**
 * 获取来访者列表
 * @param filters 筛选条件
 * @param pagination 分页信息
 * @returns 来访者列表
 */
interface VisitorFilters {
  name?: string;
  phone?: string;
  status?: string;
}

interface Pagination {
  page: number;
  pageSize: number;
}

// 调用示例
const visitors = await VisitorService.getVisitors({
  filters: { name: '张' },
  pagination: { page: 1, pageSize: 20 }
});
```

#### createVisitor - 创建来访者
```typescript
/**
 * 创建新来访者
 * @param visitorData 来访者数据
 * @returns 创建结果
 */
interface VisitorData {
  name: string;
  phone?: string;
  email?: string;
  age?: number;
  gender?: string;
  notes?: string;
}

// 调用示例
const result = await VisitorService.createVisitor(visitorData);
```

#### updateVisitor - 更新来访者
```typescript
/**
 * 更新来访者信息
 * @param id 来访者ID
 * @param updates 更新数据
 * @returns 更新结果
 */
// 调用示例
const result = await VisitorService.updateVisitor(1, {
  phone: '13900139000',
  notes: '更新联系方式'
});
```

#### deleteVisitor - 删除来访者
```typescript
/**
 * 删除来访者
 * @param id 来访者ID
 * @returns 删除结果
 */
// 调用示例
const result = await VisitorService.deleteVisitor(1);
```

### CaseService - 个案服务

#### getCases - 获取个案列表
```typescript
/**
 * 获取个案列表
 * @param visitorId 来访者ID
 * @param filters 筛选条件
 * @returns 个案列表
 */
// 调用示例
const cases = await CaseService.getCases(1, {
  status: 'active',
  dateRange: {
    start: '2024-01-01',
    end: '2024-12-31'
  }
});
```

#### createCase - 创建个案
```typescript
/**
 * 创建新个案
 * @param caseData 个案数据
 * @returns 创建结果
 */
interface CaseData {
  visitorId: number;
  title: string;
  description?: string;
  sessionDate: string;
  duration: number;
  notes?: string;
}

// 调用示例
const result = await CaseService.createCase(caseData);
```

#### exportCase - 导出个案
```typescript
/**
 * 导出个案报告
 * @param caseId 个案ID
 * @param format 导出格式
 * @returns 导出的文件
 */
// 调用示例
const pdfBlob = await CaseService.exportCase(1, 'pdf');
```

### ScheduleService - 日程服务

#### getAppointments - 获取预约列表
```typescript
/**
 * 获取预约列表
 * @param date 日期
 * @returns 预约列表
 */
// 调用示例
const appointments = await ScheduleService.getAppointments('2024-01-15');
```

#### createAppointment - 创建预约
```typescript
/**
 * 创建新预约
 * @param appointmentData 预约数据
 * @returns 创建结果
 */
interface AppointmentData {
  visitorId: number;
  title: string;
  startTime: string;
  endTime: string;
  description?: string;
  reminder?: boolean;
}

// 调用示例
const result = await ScheduleService.createAppointment(appointmentData);
```

## 🎨 组件接口

### 表单组件接口

#### Form组件
```typescript
interface FormProps {
  initialData?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  validationSchema?: any;
}
```

#### Table组件
```typescript
interface TableProps {
  data: any[];
  columns: TableColumn[];
  pagination?: Pagination;
  onPageChange?: (page: number) => void;
  onSort?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  loading?: boolean;
}
```

### 模态框组件接口

#### Modal组件
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showCloseButton?: boolean;
}
```

## 🔐 权限和验证

### 数据验证
```typescript
// 来访者数据验证规则
const visitorValidation = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入正确的邮箱地址'
  }
};
```

### 错误处理
```typescript
// API错误处理示例
try {
  const result = await VisitorService.createVisitor(data);
} catch (error) {
  if (error.code === 'SQLITE_CONSTRAINT') {
    throw new Error('手机号码已存在');
  }
  throw error;
}
```

## 📈 性能优化

### 数据分页
```typescript
// 分页查询优化
const getVisitors = async (page: number, pageSize: number) => {
  return await window.electronAPI.query({
    sql: 'SELECT * FROM visitors LIMIT ? OFFSET ?',
    params: [pageSize, (page - 1) * pageSize]
  });
};
```

### 批量操作
```typescript
// 批量导入优化
const importVisitors = async (visitors: VisitorData[]) => {
  return await window.electronAPI.transaction(
    visitors.map(visitor => ({
      sql: 'INSERT INTO visitors (name, phone, email) VALUES (?, ?, ?)',
      params: [visitor.name, visitor.phone, visitor.email]
    }))
  );
};
```

## 🧪 测试用例

### API测试示例
```typescript
// VisitorService测试
describe('VisitorService', () => {
  it('should create a new visitor', async () => {
    const visitorData = {
      name: '测试用户',
      phone: '13800138000'
    };
    
    const result = await VisitorService.createVisitor(visitorData);
    expect(result.success).toBe(true);
    expect(result.id).toBeDefined();
  });

  it('should validate visitor data', async () => {
    const invalidData = { name: '' };
    await expect(VisitorService.createVisitor(invalidData))
      .rejects.toThrow('姓名不能为空');
  });
});
```

## 🔄 版本历史

### v1.0.0 (2024-01-01)
- 初始API版本发布
- 基础数据库操作接口
- 文件导入导出功能
- 系统对话框接口

### v1.1.0 (2024-02-01)
- 新增事务处理接口
- 优化错误处理机制
- 添加数据验证规则

## 📞 支持

如有API使用问题，请：
1. 查看本文档相关章节
2. 检查错误信息和日志
3. 提交Issue并提供复现步骤

---
*本文档基于RESTful API设计和现代前端架构最佳实践*