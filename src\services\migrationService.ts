// 数据库迁移服务

export class MigrationService {
  
  // 检查环境
  private isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI?.isElectron;
  }

  // 执行SQL命令
  private async run(sql: string, params: unknown[] = []): Promise<{ changes: number; lastInsertRowid: number | null }> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      throw error;
    }
  }

  // 创建预约表
  async createAppointmentsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        visitor_id TEXT,
        case_id TEXT,
        visitor_name TEXT NOT NULL,
        visitor_phone TEXT,
        visitor_age INTEGER,
        visitor_gender TEXT CHECK (visitor_gender IN ('男', '女')),
        
        date TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        duration INTEGER NOT NULL,
        
        type TEXT NOT NULL CHECK (type IN ('个体咨询', '团体咨询', '家庭咨询', '沙盘疗法', '评估', '督导', '其他')),
        status TEXT NOT NULL CHECK (status IN ('待确认', '已确认', '进行中', '已完成', '已取消', '缺席')),
        urgency TEXT NOT NULL CHECK (urgency IN ('普通', '紧急', '危机干预')),
        
        room TEXT NOT NULL,
        therapist_id TEXT NOT NULL,
        therapist_name TEXT NOT NULL,
        
        subject TEXT NOT NULL,
        description TEXT,
        notes TEXT,
        
        reminder_enabled INTEGER DEFAULT 0,
        reminder_time INTEGER DEFAULT 15,
        
        is_first_session INTEGER DEFAULT 0,
        session_number INTEGER,
        total_planned_sessions INTEGER,
        
        fee REAL,
        payment_status TEXT CHECK (payment_status IN ('未支付', '已支付', '部分支付')),
        
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        created_by TEXT NOT NULL,
        
        FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE SET NULL,
        FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE SET NULL
      );
    `;
    
    await this.run(sql);
    console.log('预约表创建成功');
  }

  // 创建预约表索引
  async createAppointmentsIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(date);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_visitor_id ON appointments(visitor_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_case_id ON appointments(case_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_therapist_id ON appointments(therapist_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('预约表索引创建成功');
  }

  // 创建快速备注表
  async createQuickNotesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS quick_notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        date TEXT NOT NULL,
        pinned INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('快速备注表创建成功');
  }

  // 创建每日工作台相关表（已移除树洞功能）
  async createDailyWorkspaceTables(): Promise<void> {
    // 树洞功能已移除，此方法保留为兼容性
    console.log('每日工作台功能已精简，跳过树洞相关表创建');
  }

  // 创建每日工作台索引（已移除树洞功能）
  async createDailyWorkspaceTableIndexes(): Promise<void> {
    // 树洞功能已移除，此方法保留为兼容性
    console.log('每日工作台功能已精简，跳过树洞相关索引创建');
  }

  // 创建每日短语表
  async createDailyPhrasesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS daily_phrases (
        id TEXT PRIMARY KEY,
        chinese TEXT NOT NULL,
        pinyin TEXT NOT NULL,
        english TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        source TEXT,
        tags TEXT,
        is_active INTEGER DEFAULT 1,
        usage_count INTEGER DEFAULT 0,
        last_used TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('每日短语表创建成功');
  }

  // 创建每日短语索引
  async createDailyPhrasesIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_daily_phrases_category ON daily_phrases(category);',
      'CREATE INDEX IF NOT EXISTS idx_daily_phrases_is_active ON daily_phrases(is_active);',
      'CREATE INDEX IF NOT EXISTS idx_daily_phrases_usage_count ON daily_phrases(usage_count);',
      'CREATE INDEX IF NOT EXISTS idx_daily_phrases_last_used ON daily_phrases(last_used);'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('每日短语索引创建成功');
  }

  // 创建设置表（如果不存在）
  async createSettingsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        data TEXT,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('设置表创建成功');
  }

  // 创建心情记录表
  async createMoodRecordsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS mood_records (
        id TEXT PRIMARY KEY,
        mood_type TEXT NOT NULL,
        selected_at TEXT NOT NULL,
        response_id TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('心情记录表创建成功');
  }

  // 创建心情记录表索引
  async createMoodRecordsIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_mood_records_mood_type ON mood_records(mood_type);',
      'CREATE INDEX IF NOT EXISTS idx_mood_records_selected_at ON mood_records(selected_at);',
      'CREATE INDEX IF NOT EXISTS idx_mood_records_is_active ON mood_records(is_active);',
      'CREATE INDEX IF NOT EXISTS idx_mood_records_date ON mood_records(DATE(selected_at));'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('心情记录索引创建成功');
  }

  // 初始化每日心语数据
  async initializeDailyMessages(): Promise<void> {
    console.log('每日短语功能已移除，跳过数据初始化');
  }

  // 创建今日工作台相关索引
  async createDailyWorkspaceIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_quick_notes_date ON quick_notes(date);',
      'CREATE INDEX IF NOT EXISTS idx_quick_notes_pinned ON quick_notes(pinned);',
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('今日工作台索引创建成功');
  }

  // 检查表是否存在
  async checkTableExists(tableName: string): Promise<boolean> {
    if (!this.isElectronEnvironment()) {
      return false;
    }
    
    try {
      const result = await window.electronAPI!.dbQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
        [tableName]
      );
      return result.length > 0;
    } catch (error) {
      console.error('检查表存在性失败:', error);
      return false;
    }
  }

  // 运行所有迁移
  async runMigrations(): Promise<void> {
    try {
      console.log('开始数据库迁移...');
      
      // 检查预约表是否存在
      const appointmentsExists = await this.checkTableExists('appointments');
      
      if (!appointmentsExists) {
        await this.createAppointmentsTable();
        await this.createAppointmentsIndexes();
        console.log('预约功能迁移完成');
      } else {
        console.log('预约表已存在，跳过迁移');
      }

      // 检查今日工作台相关表
      const quickNotesExists = await this.checkTableExists('quick_notes');
      const settingsExists = await this.checkTableExists('settings');
      const dailyPhrasesExists = await this.checkTableExists('daily_phrases');

      if (!quickNotesExists) {
        await this.createQuickNotesTable();
        console.log('快速备注功能迁移完成');
      }

      if (!settingsExists) {
        await this.createSettingsTable();
        console.log('设置表迁移完成');
      }

      if (!dailyPhrasesExists) {
        await this.createDailyPhrasesTable();
        await this.createDailyPhrasesIndexes();
        console.log('每日短语功能迁移完成');
      }

      // 检查心情记录表
      const moodRecordsExists = await this.checkTableExists('mood_records');
      if (!moodRecordsExists) {
        await this.createMoodRecordsTable();
        await this.createMoodRecordsIndexes();
        console.log('心情记录功能迁移完成');
      }

      // 树洞功能已移除，跳过相关表创建

      // 创建索引
      await this.createDailyWorkspaceIndexes();
      
      console.log('数据库迁移完成');
    } catch (error) {
      console.error('数据库迁移失败:', error);
      throw error;
    }
  }

  // 获取数据库版本信息
  async getDatabaseVersion(): Promise<string> {
    if (!this.isElectronEnvironment()) {
      return '0.0.0';
    }
    
    try {
      // 检查是否有版本表
      const versionTableExists = await this.checkTableExists('database_version');
      
      if (!versionTableExists) {
        // 创建版本表
        await this.run(`
          CREATE TABLE IF NOT EXISTS database_version (
            id INTEGER PRIMARY KEY,
            version TEXT NOT NULL,
            updated_at TEXT NOT NULL
          );
        `);
        
        // 插入初始版本
        await this.run(
          'INSERT INTO database_version (version, updated_at) VALUES (?, ?)',
          ['1.0.0', new Date().toISOString()]
        );
        
        return '1.0.0';
      }
      
      const result = await window.electronAPI!.dbQuery(
        'SELECT version FROM database_version ORDER BY id DESC LIMIT 1'
      ) as Array<{ version: string }>;
      
      return result.length > 0 ? result[0].version : '0.0.0';
    } catch (error) {
      console.error('获取数据库版本失败:', error);
      return '0.0.0';
    }
  }

  // 更新数据库版本
  async updateDatabaseVersion(version: string): Promise<void> {
    if (!this.isElectronEnvironment()) {
      return;
    }
    
    try {
      await this.run(
        'INSERT INTO database_version (version, updated_at) VALUES (?, ?)',
        [version, new Date().toISOString()]
      );
      console.log(`数据库版本更新为: ${version}`);
    } catch (error) {
      console.error('更新数据库版本失败:', error);
    }
  }

  // 树洞功能已移除，此方法保留为兼容性
  async upgradeTreeHoleTable(): Promise<void> {
    console.log('树洞功能已移除，跳过表结构升级');
  }
}

// 导出实例
export const migrationService = new MigrationService();

// 导出迁移函数供直接调用
export const runDatabaseMigrations = async (): Promise<void> => {
  await migrationService.runMigrations();
};

export const checkDatabaseVersion = async (): Promise<string> => {
  return await migrationService.getDatabaseVersion();
};