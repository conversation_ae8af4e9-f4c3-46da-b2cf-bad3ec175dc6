import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>hart, Pie, Cell, LineChart, Line,
  ResponsiveContainer, CartesianGrid, XAxis, YAxis, Tooltip, Legend
} from 'recharts';
import {
  Users, Calendar, Package, TrendingUp,
  Download, RefreshCw, FileText, Eye
} from 'lucide-react';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, AlignmentType } from 'docx';
import { saveAs } from 'file-saver';
import { PageContainer, PageHeader } from './ui/Layout';
import { Button } from './ui/Button';
import './Statistics.css';

// 导入数据服务
import { electronDataManager } from '../services/electronDataManager';
import type { SandTool } from '../types/sandtool';
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';

// 定义数据类型
interface StatData {
  name: string;
  value: number;
  color?: string;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
}

interface ChartData {
  name: string;
  [key: string]: string | number;
}

interface StatSummary {
  title: string;
  value: string;
  detail: string;
  icon: React.ComponentType<{ size?: number }>;
  trend: {
    value: number;
    type: 'up' | 'down' | 'stable';
  };
  color: string;
}

const Statistics: React.FC = () => {
  const [dateRange, setDateRange] = useState('month');
  const [reportType, setReportType] = useState('comprehensive');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 从数据库加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // 这里可以加载统计数据
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);

  // 统计数据计算
  const [statisticsSummary, setStatisticsSummary] = useState<StatSummary[]>([
    {
      title: '来访者总数',
      value: '0',
      detail: '加载中...',
      icon: Users,
      trend: { value: 0, type: 'stable' },
      color: '#3B82F6'
    },
    {
      title: '个案总数',
      value: '0',
      detail: '加载中...',
      icon: FileText,
      trend: { value: 0, type: 'stable' },
      color: '#10B981'
    },
    {
      title: '团沙场次',
      value: '0',
      detail: '加载中...',
      icon: Calendar,
      trend: { value: 0, type: 'stable' },
      color: '#F59E0B'
    },
    {
      title: '沙具总数',
      value: '0',
      detail: '加载中...',
      icon: Package,
      trend: { value: 0, type: 'stable' },
      color: '#8B5CF6'
    }
  ]);

  // 更新统计数据
  useEffect(() => {
    const updateStatistics = async () => {
      try {
        // 检查是否在 Electron 环境中
        let allCases, allVisitors, allSessions, allTools;
        
        if (typeof window !== 'undefined' && window.electronAPI?.isElectron) {
          [allCases, allVisitors, allSessions, allTools] = await Promise.all([
            electronDataManager.getAllCases(),
            electronDataManager.getAllVisitors(),
            electronDataManager.getAllGroupSessions(),
            electronDataManager.getAllSandTools()
          ]);
        } else {
          // 浏览器环境下使用模拟数据
          allVisitors = await electronDataManager.getAllVisitors();
          allCases = await electronDataManager.getAllCases();
          allSessions = await electronDataManager.getAllGroupSessions();
          allTools = await electronDataManager.getAllSandTools();
        }

        const thisMonthCases = allCases.filter((c: any) => {
          const caseDate = new Date(c.createdAt);
          const now = new Date();
          return caseDate.getMonth() === now.getMonth() && caseDate.getFullYear() === now.getFullYear();
        }).length;

        const thisMonthVisitors = allVisitors.filter((v: any) => {
          const visitorDate = new Date(v.createdAt);
          const now = new Date();
          return visitorDate.getMonth() === now.getMonth() && visitorDate.getFullYear() === now.getFullYear();
        }).length;

        const avgParticipants = allSessions.length > 0 
          ? Math.round(allSessions.reduce((sum: number, session: any) => sum + (session.participants?.length || 0), 0) / allSessions.length)
          : 0;

        const availableToolTypes = allTools.filter((t: SandTool) => t.available > 0).length;

        setStatisticsSummary([
          {
            title: '来访者总数',
            value: allVisitors.length.toString(),
            detail: `本月新增 ${thisMonthVisitors} 个`,
            icon: Users,
            trend: { value: thisMonthVisitors, type: thisMonthVisitors > 0 ? 'up' : 'stable' },
            color: '#3B82F6'
          },
          {
            title: '个案总数',
            value: allCases.length.toString(),
            detail: `本月新增 ${thisMonthCases} 个`,
            icon: FileText,
            trend: { value: thisMonthCases, type: thisMonthCases > 0 ? 'up' : 'stable' },
            color: '#10B981'
          },
          {
            title: '团沙场次',
            value: allSessions.length.toString(),
            detail: `平均每场 ${avgParticipants} 人参与`,
            icon: Calendar,
            trend: { value: 0, type: 'stable' },
            color: '#F59E0B'
          },
          {
            title: '沙具总数',
            value: allTools.reduce((sum: number, tool: SandTool) => sum + tool.quantity, 0).toString(),
            detail: `${availableToolTypes} 种可用类型`,
            icon: Package,
            trend: { value: 0, type: 'stable' },
            color: '#8B5CF6'
          }
        ]);
      } catch (error) {
        console.error('更新统计数据失败:', error);
        // 出错时使用默认值
        setStatisticsSummary([
          {
            title: '来访者总数',
            value: '0',
            detail: '数据加载失败',
            icon: Users,
            trend: { value: 0, type: 'stable' },
            color: '#3B82F6'
          },
          {
            title: '个案总数',
            value: '0',
            detail: '数据加载失败',
            icon: FileText,
            trend: { value: 0, type: 'stable' },
            color: '#10B981'
          },
          {
            title: '团沙场次',
            value: '0',
            detail: '数据加载失败',
            icon: Calendar,
            trend: { value: 0, type: 'stable' },
            color: '#F59E0B'
          },
          {
            title: '沙具总数',
            value: '0',
            detail: '数据加载失败',
            icon: Package,
            trend: { value: 0, type: 'stable' },
            color: '#8B5CF6'
          }
        ]);
      }
    };

    if (!isLoading) {
      updateStatistics();
    }
  }, [isLoading]);

  // 个案状态分布数据
  const [caseStatusData, setCaseStatusData] = useState<StatData[]>([]);

  useEffect(() => {
    const updateCaseStatusData = async () => {
      try {
        let allCases: SimpleCase[] = [];
        if (typeof window !== 'undefined' && window.electronAPI?.isElectron) {
          allCases = await electronDataManager.getAllCases();
        } else {
          // 浏览器环境下使用空数组
          allCases = [];
        }
        
        // 计算状态分布
        const statusCounts: Record<string, number> = {
          '进行中': 0,
          '已完成': 0,
          '暂停': 0,
          '已取消': 0
        };
        
        // 这里需要根据实际的个案状态字段进行调整
        allCases.forEach((case_: SimpleCase) => {
          // 假设有个 status 字段，这里需要根据实际字段调整
          const status = '进行中'; // 默认值
          if (status in statusCounts) {
            statusCounts[status]++;
          }
        });
        
        const data = [
          {
            name: '进行中',
            value: statusCounts['进行中'],
            color: '#3B82F6'
          },
          {
            name: '已完成',
            value: statusCounts['已完成'],
            color: '#10B981'
          },
          {
            name: '暂停',
            value: statusCounts['暂停'],
            color: '#F59E0B'
          },
          {
            name: '已取消',
            value: statusCounts['已取消'],
            color: '#EF4444'
          }
        ];
        
        setCaseStatusData(data);
      } catch (error) {
        console.error('更新个案状态数据失败:', error);
        // 出错时使用默认值
        setCaseStatusData([
          {
            name: '进行中',
            value: 0,
            color: '#3B82F6'
          },
          {
            name: '已完成',
            value: 0,
            color: '#10B981'
          },
          {
            name: '暂停',
            value: 0,
            color: '#F59E0B'
          },
          {
            name: '已取消',
            value: 0,
            color: '#EF4444'
          }
        ]);
      }
    };
    
    if (!isLoading) {
      updateCaseStatusData();
    }
  }, [isLoading]);

  // 月度趋势数据
  const [monthlyTrendData, setMonthlyTrendData] = useState<ChartData[]>([]);

  useEffect(() => {
    const updateMonthlyTrendData = async () => {
      try {
        let allCases: SimpleCase[] = [];
        let allSessions: GroupSession[] = [];
        let allVisitors: Visitor[] = [];
        
        if (typeof window !== 'undefined' && window.electronAPI?.isElectron) {
          [allCases, allSessions, allVisitors] = await Promise.all([
            electronDataManager.getAllCases(),
            electronDataManager.getAllGroupSessions(),
            electronDataManager.getAllVisitors()
          ]);
        } else {
          // 浏览器环境下使用空数组
          allCases = [];
          allSessions = [];
          allVisitors = [];
        }

        const last6Months = [];
        const now = new Date();
        
        for (let i = 5; i >= 0; i--) {
          const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
          const monthStr = month.toLocaleDateString('zh-CN', { month: 'short' });
          
          const casesInMonth = allCases.filter((case_: SimpleCase) => {
            const caseDate = new Date(case_.createdAt);
            return caseDate.getMonth() === month.getMonth() && 
                   caseDate.getFullYear() === month.getFullYear();
          }).length;
          
          const sessionsInMonth = allSessions.filter((session: GroupSession) => {
            const sessionDate = new Date(session.createdAt);
            return sessionDate.getMonth() === month.getMonth() && 
                   sessionDate.getFullYear() === month.getFullYear();
          }).length;
          
          const visitorsInMonth = allVisitors.filter((visitor: Visitor) => {
            const visitorDate = new Date(visitor.createdAt);
            return visitorDate.getMonth() === month.getMonth() && 
                   visitorDate.getFullYear() === month.getFullYear();
          }).length;
          
          last6Months.push({
            name: monthStr,
            个案数: casesInMonth,
            团沙场次: sessionsInMonth,
            来访者: visitorsInMonth
          });
        }
        
        setMonthlyTrendData(last6Months);
      } catch (error) {
        console.error('更新月度趋势数据失败:', error);
        // 出错时使用默认值
        setMonthlyTrendData([]);
      }
    };
    
    if (!isLoading) {
      updateMonthlyTrendData();
    }
  }, [isLoading]);

  // 沙具使用统计
  const [sandToolUsageData, setSandToolUsageData] = useState<ChartData[]>([]);

  useEffect(() => {
    const updateSandToolUsageData = async () => {
      try {
        let allTools: SandTool[] = [];
        if (typeof window !== 'undefined' && window.electronAPI?.isElectron) {
          allTools = await electronDataManager.getAllSandTools();
        } else {
          // 浏览器环境下使用空数组
          allTools = [];
        }
        
        const usageData = allTools
          .map((tool: SandTool) => ({
            name: tool.name.length > 8 ? tool.name.substring(0, 8) + '...' : tool.name,
            使用次数: tool.usageCount || 0,
            库存量: tool.quantity
          }))
          .sort((a, b) => (b.使用次数 as number) - (a.使用次数 as number))
          .slice(0, 8);
          
        setSandToolUsageData(usageData);
      } catch (error) {
        console.error('更新沙具使用数据失败:', error);
        // 出错时使用默认值
        setSandToolUsageData([]);
      }
    };
    
    if (!isLoading) {
      updateSandToolUsageData();
    }
  }, [isLoading]);

  // 生成Word报告
  const generateWordReport = async () => {
    setIsGenerating(true);
    
    try {
      // 模拟数据处理时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      const doc = new Document({
        sections: [{
          properties: {},
          children: [
            // 标题
            new Paragraph({
              text: "沙盘疗法管理系统 - 数据统计报告",
              heading: HeadingLevel.TITLE,
              alignment: AlignmentType.CENTER,
            }),
            
            new Paragraph({
              text: `报告生成时间：${new Date().toLocaleString('zh-CN')}`,
              alignment: AlignmentType.CENTER,
            }),
            
            new Paragraph({ text: "" }),
            
            // 概览统计
            new Paragraph({
              text: "一、数据概览",
              heading: HeadingLevel.HEADING_1,
            }),
            
            ...statisticsSummary.map(stat => 
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${stat.title}：`,
                    bold: true,
                  }),
                  new TextRun({
                    text: `${stat.value} （${stat.detail}）`,
                  }),
                ],
              })
            ),
            
            new Paragraph({ text: "" }),
            
            // 个案状态分析
            new Paragraph({
              text: "二、个案状态分析",
              heading: HeadingLevel.HEADING_1,
            }),
            
            ...caseStatusData.map(data => 
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${data.name}：`,
                    bold: true,
                  }),
                  new TextRun({
                    text: `${data.value} 个案`,
                  }),
                ],
              })
            ),
            
            new Paragraph({ text: "" }),
            
            // 沙具使用情况
            new Paragraph({
              text: "三、沙具使用情况",
              heading: HeadingLevel.HEADING_1,
            }),
            
            // 创建表格
            new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              rows: [
                new TableRow({
                  children: [
                    new TableCell({
                      children: [new Paragraph({ text: "沙具名称", alignment: AlignmentType.CENTER })],
                    }),
                    new TableCell({
                      children: [new Paragraph({ text: "使用次数", alignment: AlignmentType.CENTER })],
                    }),
                    new TableCell({
                      children: [new Paragraph({ text: "库存量", alignment: AlignmentType.CENTER })],
                    }),
                  ],
                }),
                ...sandToolUsageData.slice(0, 10).map(data => 
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph({ text: data.name as string })],
                      }),
                      new TableCell({
                        children: [new Paragraph({ text: (data.使用次数 as number).toString(), alignment: AlignmentType.CENTER })],
                      }),
                      new TableCell({
                        children: [new Paragraph({ text: (data.库存量 as number).toString(), alignment: AlignmentType.CENTER })],
                      }),
                    ],
                  })
                ),
              ],
            }),
            
            new Paragraph({ text: "" }),
            
            // 结论和建议
            new Paragraph({
              text: "四、分析结论",
              heading: HeadingLevel.HEADING_1,
            }),
            
            new Paragraph({
              text: "1. 来访者数量呈稳定增长趋势，说明沙盘疗法服务需求持续上升。",
            }),
            
            new Paragraph({
              text: "2. 个案完成率良好，治疗效果显著。",
            }),
            
            new Paragraph({
              text: "3. 团体沙盘活动参与度高，建议继续扩大团沙服务。",
            }),
            
            new Paragraph({
              text: "4. 沙具使用情况均衡，库存管理良好。",
            }),
            
            new Paragraph({ text: "" }),
            
            new Paragraph({
              text: "报告生成完毕。",
              alignment: AlignmentType.RIGHT,
            }),
          ],
        }],
      });

      const blob = await Packer.toBlob(doc);
      const fileName = `沙盘疗法统计报告_${new Date().toISOString().split('T')[0]}.docx`;
      saveAs(blob, fileName);
      
    } catch (error) {
      console.error('生成报告失败:', error);
      alert('生成报告失败，请稍后重试。');
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <div className="loading-text">正在加载数据统计...</div>
          <div className="loading-subtitle">
            系统正在收集和分析最新的统计数据，请稍候片刻
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader 
        title="数据统计"
        subtitle="全面的数据分析与报告生成"
        actions={
          <div className="filter-group">
            <select 
              value={dateRange} 
              onChange={(e) => setDateRange(e.target.value)}
              className="date-range-select"
            >
              <option value="week">最近一周</option>
              <option value="month">最近一月</option>
              <option value="quarter">最近三月</option>
              <option value="year">最近一年</option>
            </select>
            
            <select 
              value={reportType} 
              onChange={(e) => setReportType(e.target.value)}
              className="report-type-select"
            >
              <option value="comprehensive">综合报告</option>
              <option value="cases">个案专项</option>
              <option value="group_sessions">团沙专项</option>
              <option value="sandtools">沙具专项</option>
            </select>
            
            <Button 
              onClick={generateWordReport}
              disabled={isGenerating}
              leftIcon={isGenerating ? <RefreshCw size={16} className="spinning" /> : <Download size={16} />}
            >
              {isGenerating ? '生成中...' : '导出Word报告'}
            </Button>
          </div>
        }
      />

      {/* 统计概览卡片 */}
      <div className="statistics-stats-overview">
        {statisticsSummary.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className="statistics-stat-card">
              <div className="statistics-stat-icon">
                <IconComponent size={28} />
              </div>
              <div className="statistics-stat-content">
                <h3>{stat.title}</h3>
                <div className="statistics-stat-value">{stat.value}</div>
                <div className="statistics-stat-detail">{stat.detail}</div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 图表区域 */}
      <div className="statistics-charts-section">
        {/* 个案状态分布 */}
        <div className="statistics-chart-card">
          <div className="statistics-chart-title">
            <div className="statistics-chart-icon">
              <Eye size={16} />
            </div>
            <h3>个案状态分布</h3>
          </div>
          <div className="chart-container">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={caseStatusData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {caseStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name) => [`${value} 个案`, name]}
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 月度趋势 */}
        <div className="statistics-chart-card">
          <div className="statistics-chart-title">
            <div className="statistics-chart-icon">
              <TrendingUp size={16} />
            </div>
            <h3>月度趋势</h3>
          </div>
          <div className="chart-container">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={monthlyTrendData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="个案数" 
                  stroke="#3B82F6" 
                  strokeWidth={3}
                  dot={{ fill: '#3B82F6', r: 6 }}
                  activeDot={{ r: 8, fill: '#2563EB' }}
                />
                <Line 
                  type="monotone" 
                  dataKey="团沙场次" 
                  stroke="#10B981" 
                  strokeWidth={3}
                  dot={{ fill: '#10B981', r: 6 }}
                  activeDot={{ r: 8, fill: '#059669' }}
                />
                <Line 
                  type="monotone" 
                  dataKey="来访者" 
                  stroke="#F59E0B" 
                  strokeWidth={3}
                  dot={{ fill: '#F59E0B', r: 6 }}
                  activeDot={{ r: 8, fill: '#D97706' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* 沙具使用统计表格 */}
      <div className="statistics-data-table">
        <div className="table-header">
          <div className="table-title">
            <div className="table-icon">
              <Package size={16} />
            </div>
            <h3>沙具使用统计 (Top 10)</h3>
          </div>
        </div>
        <table className="data-table">
          <thead>
            <tr>
              <th>沙具名称</th>
              <th>使用次数</th>
              <th>库存量</th>
              <th>使用率</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            {sandToolUsageData.slice(0, 10).map((tool, index) => {
              const usageRate = Math.round(((tool.使用次数 as number) / ((tool.使用次数 as number) + (tool.库存量 as number))) * 100);
              return (
                <tr key={index}>
                  <td>{tool.name}</td>
                  <td>{tool.使用次数 as number}</td>
                  <td>{tool.库存量 as number}</td>
                  <td>{usageRate}%</td>
                  <td>
                    <span style={{
                      color: usageRate > 70 ? '#EF4444' : usageRate > 40 ? '#F59E0B' : '#10B981',
                      fontWeight: '600'
                    }}>
                      {usageRate > 70 ? '热门' : usageRate > 40 ? '常用' : '一般'}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </PageContainer>
  );
};

export default Statistics;
