/* 每日短语组件样式 */
.daily-phrase-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.daily-phrase-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0;
}

.daily-phrase-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.daily-phrase-card {
  flex: 1;
  padding: 16px 20px 20px;
  display: flex;
  flex-direction: column;
  min-height: 140px;
}

.phrase-category {
  display: inline-block;
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
  margin-bottom: 12px;
}

.phrase-content {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.phrase-chinese {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
}

.phrase-pinyin {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 13px;
  font-style: italic;
}

.phrase-translation {
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
  margin-top: 8px;
}

.phrase-english {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.4;
}

/* 外部操作按钮区域 */
.phrase-actions-external {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.btn-action-external {
  background: white;
  border: 1px solid #e5e7eb;
  color: #374151;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  min-width: fit-content;
}

.btn-action-external:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.btn-action-external:active {
  transform: translateY(0);
}

.btn-like.liked {
  color: #ef4444;
  border-color: #fecaca;
  background: #fef2f2;
}

.btn-refresh:hover {
  color: #2563eb;
  border-color: #dbeafe;
  background: #eff6ff;
}

.btn-translate:hover {
  color: #059669;
  border-color: #d1fae5;
  background: #f0fdf4;
}

/* 加载状态 */
.daily-phrase-loading,
.daily-phrase-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 16px;
  color: #6b7280;
}

.phrase-skeleton {
  width: 200px;
  height: 60px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .daily-phrase-container {
    margin: 0 -4px;
  }
  
  .daily-phrase-header {
    padding: 14px 16px 10px;
  }
  
  .daily-phrase-card {
    padding: 14px 16px 16px;
    min-height: 120px;
  }
  
  .phrase-chinese {
    font-size: 16px;
  }
  
  .phrase-actions-external {
    padding: 12px 16px;
    gap: 8px;
  }
  
  .btn-action-external {
    padding: 6px 12px;
    font-size: 13px;
    flex: 1;
    justify-content: center;
  }
}
