# 沙盘管理系统 - 激活码生成器使用说明

## 📋 工具概述

这是一个专为沙盘管理系统设计的激活码生成工具，供软件发行方使用。工具完全离线运行，确保私钥安全。

## 🚀 快速开始

### 第一步：获取用户设备码
1. 用户在沙盘管理系统登录页点击"设备码"按钮
2. 用户复制完整的设备码（格式如：ABCDE-12345-FGHIJ-67890-KLMNO）
3. 用户将设备码发送给您

### 第二步：转换设备码
1. 打开桌面上的"沙盘管理系统-激活码生成器.html"
2. 在"设备码转换"区域粘贴用户提供的设备码
3. 点击"转换为设备哈希"按钮
4. 系统自动生成设备哈希值（用于后续激活码生成）

### 第三步：配置私钥和参数
1. **RSA私钥**：粘贴您的PKCS#8格式私钥
2. **公钥版本(kv)**：选择对应的公钥版本（默认版本1）
3. **许可证版本(v)**：选择许可证版本（默认版本1）
4. **盐ID(sid)**：输入唯一的盐标识符（如：SALT2025A）
5. **签发时间**：默认为当前时间，可手动调整
6. **额外字段**：可选，JSON格式的额外信息

### 第四步：生成激活码
1. 确认所有参数填写正确
2. 点击"生成激活码"按钮
3. 复制生成的激活码
4. 将激活码发送给用户

### 第五步：用户激活
1. 用户在登录页点击"激活"按钮
2. 粘贴激活码到弹出的激活窗口
3. 点击"激活"完成软件激活

## 🔧 技术参数说明

### 私钥格式要求
- **格式**：PKCS#8（推荐）
- **算法**：RSA-2048或更高
- **示例格式**：
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----END PRIVATE KEY-----
```

### 如果您的私钥是PKCS#1格式
使用OpenSSL转换为PKCS#8：
```bash
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in private_key.pem -out private_key_pkcs8.pem
```

### 参数说明
- **kv (公钥版本)**：与软件内置公钥版本对应，确保一致性
- **v (许可证版本)**：许可证格式版本，用于向后兼容
- **sid (盐ID)**：用于增强安全性的随机盐值
- **ia (签发时间)**：Unix时间戳，用于许可证有效期管理

## 🛡️ 安全注意事项

### 私钥安全
- ⚠️ **绝对不要**在联网环境中使用私钥
- ⚠️ **绝对不要**将私钥发送给任何人
- ⚠️ **绝对不要**在不安全的设备上使用此工具
- ✅ 建议在专用的离线设备上运行此工具
- ✅ 使用完毕后清空浏览器缓存和历史记录

### 激活码管理
- 每个设备码只能生成一次激活码
- 激活码与用户设备硬件绑定，无法转移
- 建议记录激活码发放日志，便于管理

### 数据安全
- 本工具完全在浏览器本地运行，不会上传任何数据
- 所有计算都在客户端完成，确保数据安全
- 建议定期备份私钥（安全存储）

## 🔍 故障排除

### 常见问题

**Q: 提示"私钥导入失败"**
A: 请确认私钥格式为PKCS#8，如果是PKCS#1格式请先转换

**Q: 生成的激活码无法激活**
A: 检查以下项目：
- 公钥版本(kv)是否与软件一致
- 设备码是否正确转换
- 私钥是否与软件内置公钥匹配

**Q: 设备码转换失败**
A: 确认设备码格式正确，应为5组5位字符，用连字符分隔

**Q: 用户更换硬件后无法激活**
A: 硬件变更会导致设备码改变，需要重新获取设备码并生成新的激活码

### 技术支持
如遇到技术问题，请提供以下信息：
- 错误信息截图
- 使用的浏览器版本
- 私钥格式和长度
- 设备码示例（脱敏处理）

## 📝 更新日志

### v1.0.0 (2024-12-09)
- 初始版本发布
- 支持设备码转换
- 支持RSA-PKCS1-v1_5签名
- 完整的用户界面
- 离线运行支持

---

**重要提醒**：此工具仅供授权的软件发行方使用，请严格遵守软件许可协议和相关法律法规。
