// 基于种子 RDTCKT2619JN0270 生成的公钥 (版本 1)
const KMAP_RAW = [
  {
    v: 1,
    p: [
      '-----BEGIN PUBLIC KEY-----\n',
      'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxK6mYkVR++XOPGPSvqwQ\n',
      'xZpz59+P51SgWdaLuwD1V4i55AnGI4tSXmxcXisIp4bci01NbQgBIbbqJ7mAeOkn\n',
      'e6Bf9NaN8XeOMNWqwrkQ5juRKLPQddr0flA2m+Yt6rHNPOZlB0UeDVM0+5couj3Z\n',
      'vWt06qCSrd37ivgdMKeX+ce+7mwvTgxf+wRT3I59OR7lrqeQ9J8UCAcn0Ei8eerc\n',
      'FhLNuKwPAV8GZlI0MsPQaQi6jUeKtbqtk2KlOBz+oCBYeaD+S/fqZLelNap5qGDf\n',
      'KzTpRLjQDCMeKEkImpI3Tf0x7To2IKP3fMSxMMQnr0byR3YpnC9QbhE05jBxovEX\n',
      '1QIDAQAB\n',
      '-----END PUBLIC KEY-----'
    ]
  }
];

export function getPublicKey(version: number): string | undefined {
  const item = KMAP_RAW.find(i => i.v === version);
  if (!item) {
    console.warn(`未找到版本 ${version} 的公钥`);
    return undefined;
  }
  return item.p.join('');
}