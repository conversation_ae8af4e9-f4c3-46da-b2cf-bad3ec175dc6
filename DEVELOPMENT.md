# 开发文档架构

基于现代开发文档最佳实践，本项目采用结构化的文档架构，参考Azure Portal等企业级项目的文档标准。

## 📋 文档架构概览

### 1. 核心文档层
- **README.md** - 项目概览、快速入门、功能特性
- **DEVELOPMENT.md** - 详细开发指南、架构说明（本文档）
- **API.md** - API参考、接口文档、使用示例

### 2. 功能模块文档层
- **CASES.md** - 个案管理模块详细说明
- **VISITORS.md** - 来访者管理模块说明
- **SCHEDULE.md** - 日程管理模块说明
- **SANDBOX.md** - 沙具管理模块说明
- **STATISTICS.md** - 数据统计模块说明

### 3. 技术文档层
- **ARCHITECTURE.md** - 系统架构设计、技术选型
- **DEPLOYMENT.md** - 部署指南、运维说明
- **TESTING.md** - 测试策略、测试指南
- **SECURITY.md** - 安全规范、数据保护

### 4. 流程文档层
- **CONTRIBUTING.md** - 贡献指南、代码规范
- **RELEASE.md** - 发布流程、版本管理
- **SUPPORT.md** - 支持流程、问题处理

## 🏗️ 架构设计原则

### 模块化架构
基于Azure Portal的扩展架构理念，采用模块化设计：
- **主进程模块** - Electron主进程，负责原生功能
- **渲染进程模块** - React前端，负责UI展示
- **数据服务模块** - 业务逻辑和数据管理
- **工具模块** - 通用工具和工具函数

### 数据流架构
参考现代前端架构模式：
```typescript
// 数据流示意图
主进程 (SQLite) ↔ IPC通信 ↔ 数据服务 ↔ React组件 ↔ UI
```

## 🔧 开发环境设置

### 环境要求
- Node.js 18+
- npm 或 yarn
- Git

### 开发工具链
- **构建工具**: Vite 7 + TypeScript
- **代码检查**: ESLint
- **桌面框架**: Electron 37
- **数据库**: SQLite3
- **样式**: CSS3 + 现代设计系统

### 开发命令
```bash
# 开发模式（推荐）
npm start

# 仅前端开发
npm run dev

# 构建应用
npm run build

# 代码检查
npm run lint
```

## 📊 数据架构

### SQLite数据库设计
基于医疗数据管理最佳实践：
- **来访者表** - 个人信息、联系方式、病史
- **个案表** - 治疗记录、进度跟踪、评估结果
- **沙具表** - 沙盘工具库存、使用记录
- **日程表** - 预约安排、提醒设置

### 数据安全
- 本地加密存储
- 数据备份机制
- 访问权限控制
- 隐私数据保护

## 🎨 UI/UX架构

### 设计系统
基于现代设计原则：
- **色彩系统** - 主色、辅助色、语义色
- **排版系统** - 字体、字号、行高
- **组件库** - 按钮、表单、卡片、模态框
- **图标系统** - Lucide图标库

### 响应式设计
- 桌面端优化布局
- 高DPI屏幕支持
- 无障碍访问支持

## 🔌 API架构

### IPC通信接口
主进程与渲染进程通信规范：
```typescript
// 数据库操作接口
interface DatabaseAPI {
  query(sql: string, params?: any[]): Promise<any[]>;
  run(sql: string, params?: any[]): Promise<{ changes: number }>;
  close(): Promise<void>;
}

// 文件操作接口
interface FileAPI {
  exportCase(data: CaseData, format: 'csv' | 'docx' | 'pdf'): Promise<Blob>;
  importData(file: File): Promise<void>;
}
```

### 服务层接口
业务逻辑服务规范：
- **CaseService** - 个案管理服务
- **VisitorService** - 来访者服务
- **ScheduleService** - 日程服务
- **StatisticsService** - 统计服务

## 🧪 测试架构

### 测试策略
基于测试金字塔模型：
- **单元测试** - 函数和组件测试
- **集成测试** - 模块间集成测试
- **端到端测试** - 完整流程测试

### 测试工具
- **Jest** - 单元测试框架
- **Testing Library** - React组件测试
- **Playwright** - 端到端测试

## 🚀 部署架构

### 构建流程
```
源代码 → 构建 → 打包 → 分发
```

### 平台支持
- **Windows** - NSIS安装程序
- **macOS** - DMG磁盘映像
- **Linux** - AppImage格式

## 📈 监控和日志

### 性能监控
- 应用启动时间
- 数据库查询性能
- 内存使用情况
- CPU占用率

### 错误日志
- 前端错误捕获
- 主进程错误日志
- 用户操作日志
- 系统异常记录

## 🔄 更新和维护

### 版本管理
基于语义化版本控制：
- **主版本** - 不兼容的API修改
- **次版本** - 向下兼容的功能性新增
- **修订版本** - 向下兼容的问题修正

### 数据迁移
- 数据库架构升级
- 数据格式转换
- 备份和恢复机制

## 🤝 贡献流程

### 开发流程
1. **需求分析** - 明确功能和需求
2. **技术设计** - 设计架构和接口
3. **编码实现** - 遵循代码规范
4. **测试验证** - 确保质量
5. **代码审查** - 团队评审
6. **部署发布** - 上线运行

### 代码规范
- TypeScript严格模式
- ESLint规则检查
- Prettier代码格式化
- Git提交规范

## 📚 相关文档

- [设计系统](docs/design_system.md)
- [开发指南](docs/development_guidelines.md)
- [API参考](API.md)
- [架构设计](ARCHITECTURE.md)

## 📞 支持

如有技术问题或建议，请：
1. 查看相关文档
2. 提交Issue
3. 联系开发团队

---
*本文档基于Azure Portal等企业级项目的文档架构最佳实践*