/* 来访者管理引导遮罩与气泡样式 */
.visitor-onboarding-overlay {
  position: fixed;
  inset: 0;
  background: rgba(17,24,39,.55);
  backdrop-filter: blur(2px);
  z-index: 9999;
  font-family: var(--font-sans, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial);
}

.visitor-onboarding-overlay[data-hidden='true'] { display:none; }

.visitor-onboarding-hole {
  position: fixed;
  pointer-events: none;
  box-shadow: 0 0 0 9999px rgba(17,24,39,.55);
  border-radius: 10px;
  transition: all .28s cubic-bezier(.4,.0,.2,1);
}

.visitor-onboarding-tooltip {
  position: absolute;
  max-width: 360px;
  background: #fff;
  color: #1f2937;
  border-radius: 12px;
  padding: 16px 20px 14px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px -4px rgba(0,0,0,.25), 0 2px 8px rgba(0,0,0,.08);
  line-height: 1.5;
  animation: fadeIn .25s ease;
  font-size: 14px;
}
.visitor-onboarding-tooltip:after {
  content: '';
  position: absolute;
  width: 12px; height: 12px;
  background: #fff;
  transform: rotate(45deg);
  box-shadow: 0 2px 6px rgba(0,0,0,.08);
  z-index: -1;
}
/* 动态定位时会加 data-arrow-pos */
.visitor-onboarding-tooltip[data-arrow-pos='top']:after { bottom: -6px; left: 32px; }
.visitor-onboarding-tooltip[data-arrow-pos='bottom']:after { top: -6px; left: 32px; }
.visitor-onboarding-tooltip[data-arrow-pos='left']:after { right: -6px; top: 28px; }
.visitor-onboarding-tooltip[data-arrow-pos='right']:after { left: -6px; top: 28px; }

.visitor-onboarding-tooltip h4 {
  margin: 0 0 6px;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: .5px;
}

/* 新结构 */
.vo-header { display:flex; align-items:center; justify-content:space-between; gap:12px; }
.vo-title { font-size:15px; font-weight:600; margin:0; letter-spacing:.5px; }
.vo-step { font-size:12px; color:#64748b; white-space:nowrap; }
.vo-body { margin-top:4px; font-size:14px; color:#1e293b; line-height:1.55; }
.vo-progress { height:4px; background:#e2e8f0; border-radius:2px; margin:10px 0 4px; position:relative; overflow:hidden; }
.vo-progress:before { display:none; }
.vo-progress span { /* 统一蓝青渐变 */
  position:absolute; inset:0; width:0;
  background:linear-gradient(90deg,#0ea5e9,#2563eb);
  box-shadow:none; border-radius:2px; transition:width .3s ease;
}
.vo-progress span:after { display:none; }
.vo-footer { display:flex; align-items:center; justify-content:space-between; gap:12px; margin-top:10px; flex-wrap:wrap; }
.vo-actions { display:flex; gap:8px; }
.vo-actions button { /* 复用原按钮样式 - 明确声明以避免空规则警告 */
  font-size:13px;
  line-height: 1;
  padding: 6px 14px;
  border-radius: 6px;
  border: 1px solid #cbd5e1;
  background: #fff;
  cursor: pointer;
  transition: .15s;
  display: inline-flex;
  align-items: center;
}
.vo-actions button.primary { /* 统一主按钮色 */
  background: #0ea5e9;
  border-color: #0ea5e9;
  color: #fff;
}
.vo-actions button:hover { box-shadow: 0 2px 6px rgba(0,0,0,.12); transform:translateY(-1px); }
.vo-actions button:active { transform:translateY(0); }
.vo-dismiss-option { display:flex; align-items:center; gap:6px; font-size:12px; color:#475569; cursor:pointer; user-select:none; }
.vo-dismiss-option input { width:14px; height:14px; }

.visitor-onboarding-steps {
  display:flex;
  gap:4px;
  margin-top:4px;
  justify-content:flex-end;
}
.visitor-onboarding-steps span {
  width:6px;height:6px;border-radius:50%;background:#e2e8f0;display:inline-block;
  transition:background .2s, transform .2s;
}
.visitor-onboarding-steps span.active { background:#0ea5e9; }

.visitor-onboarding-actions { display:none !important; }

@keyframes fadeIn { from { opacity:0; transform: translateY(4px);} to { opacity:1; transform: translateY(0);} }

/* 辅助：在无目标元素时居中 */
.visitor-onboarding-center {
  position: fixed; top: 50%; left:50%; transform: translate(-50%, -50%);
}

/* 小屏优化 */
@media (max-width: 560px) {
  .visitor-onboarding-tooltip { max-width: calc(100vw - 24px); }
  .vo-footer { flex-direction:column; align-items:stretch; }
  .vo-actions { width:100%; justify-content:space-between; }
  .vo-dismiss-option { order:2; }
}

/* 居中模式箭头隐藏 */
.visitor-onboarding-center:after { display:none; }
