/* 日程安排页面样式 */

/* 主布局容器 */
.schedule-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 24px;
  align-items: start;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .schedule-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .schedule-sidebar {
    order: 2;
  }
  
  .schedule-main {
    order: 1;
  }
}

@media (max-width: 768px) {
  .schedule-layout {
    gap: 12px;
  }
}

/* 左侧信息面板 */
.schedule-sidebar {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 24px;
}

.sidebar-header {
  margin-bottom: 32px;
}

.schedule-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.create-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.create-btn:hover {
  background: #2563eb;
}

/* 今日概览 */
.today-summary {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 8px 10px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
}

.stat-card.urgent {
  background: #fef2f2;
  border-color: #fecaca;
}

/* 今日概览统计数字 - 参考个案管理样式 */
.schedule-layout .today-summary .stat-card {
  background: white;
  padding: 16px 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.schedule-layout .today-summary .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schedule-layout .today-summary .stat-card .stat-number {
  font-size: 24px !important;
  font-weight: 700 !important;
  line-height: 1 !important;
  margin-bottom: 6px !important;
  color: var(--primary-blue) !important;
}

.schedule-layout .today-summary .stat-card .stat-label {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #6b7280 !important;
  margin: 0 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.next-appointment {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.next-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
}

.next-time {
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 4px;
}

.next-visitor {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.next-subject {
  font-size: 12px;
  color: #64748b;
}

/* 日期导航 */
.date-navigator {
  margin-bottom: 24px;
}

.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.nav-btn {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.current-date {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
  text-align: center;
  flex: 1;
  margin: 0 12px;
}

.today-btn {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.today-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

/* 快速筛选 */
.quick-filters {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.filter-title {
  font-size: 14px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px 0;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 6px;
}

.filter-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  color: #334155;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 主内容区 */
.schedule-main {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 工具栏 */
.schedule-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.toolbar-left .view-switcher {
  display: flex;
  background: #f8fafc;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-btn:hover:not(.active) {
  color: #334155;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  left: 12px;
  color: #94a3b8;
  pointer-events: none;
}

.search-input {
  padding: 8px 12px 8px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #334155;
  width: 240px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #94a3b8;
}

/* 搜索表单样式 */
.form-search {
  margin-bottom: 8px !important;
  border: 1px solid #e1e5e9 !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  background-color: #f8fafc !important;
}

.form-search:focus {
  border-color: #0969da !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1) !important;
  background-color: #ffffff !important;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-field .mt-2 {
  margin-top: 8px;
}

/* 确保表单字段垂直对齐 */
.form-grid .form-field,
.form-grid > .form-field,
.form-grid > input,
.form-grid > select,
.form-grid > div > input,
.form-grid > div > select {
  height: auto;
  align-self: start;
}

/* 自定义字段样式统一 */
.mt-2 {
  margin-top: 8px !important;
  border: 1px dashed #d0d7de !important;
  background-color: #f6f8fa !important;
}

.mt-2:focus {
  border-style: solid !important;
  border-color: #0969da !important;
  background-color: #ffffff !important;
}

/* 预约列表区域 */
.appointments-area {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.area-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: white;
}

.area-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.area-title .count {
  font-weight: 400;
  color: #64748b;
  margin-left: 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 24px;
  text-align: center;
  color: #64748b;
}

.empty-state svg {
  margin-bottom: 16px;
  color: #cbd5e1;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #475569;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 24px 0;
}

/* 预约网格 */
.appointments-grid {
  padding: 24px;
}

/* 日视图预约卡片 */
.appointment-card.day-view {
  display: flex;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 16px;
  transition: all 0.2s;
  position: relative;
}

.appointment-card.day-view:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-time {
  flex-shrink: 0;
  width: 120px;
  padding: 20px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  border-radius: 12px 0 0 12px;
  text-align: center;
}

.time-main {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.time-duration {
  font-size: 12px;
  color: #64748b;
}

.card-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.visitor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.card-body .subject {
  font-size: 14px;
  color: #475569;
  margin-bottom: 8px;
}

.details {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.details span {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.details .type {
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.card-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 统一所有操作按钮样式 - 无描边柔和风格 */
.card-actions .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
  white-space: nowrap;
  text-decoration: none;
}

.card-actions .btn:hover {
  background-color: #f1f5f9;
}

.card-actions .btn:active {
  /* transform: translateY(1px); */
}

/* 查看按钮 - 柔和的蓝色文字 */
.card-actions .btn.btn-ghost {
  color: #0ea5e9;
}

.card-actions .btn.btn-ghost:hover {
  background-color: #e0f2fe;
  color: #0284c7;
}

/* 确认按钮 - 柔和的蓝色文字 */
.card-actions .btn:not(.btn-ghost) {
  color: #2563eb;
}

.card-actions .btn:not(.btn-ghost):hover {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.urgency-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #f59e0b;
  background: #fef3c7;
  padding: 4px;
  border-radius: 50%;
}

/* 日期分组（周视图/月视图） */
.day-group {
  margin-bottom: 24px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.group-date {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.group-count {
  font-size: 14px;
  color: #64748b;
}

.group-appointments {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.appointment-item.compact {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s;
}

.appointment-item.compact:hover {
  background: white;
  border-color: #cbd5e1;
}

.item-time {
  flex-shrink: 0;
  width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.item-content {
  flex: 1;
  margin-left: 16px;
}

.item-visitor {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.item-subject {
  font-size: 12px;
  color: #64748b;
}

.item-status {
  margin-left: 16px;
  margin-right: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .schedule-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .schedule-sidebar {
    position: static;
    order: 2;
  }
  
  .schedule-main {
    order: 1;
  }
  
  .summary-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .quick-filters {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .filter-group {
    margin-bottom: 0;
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .schedule-page {
    padding: 16px;
  }
  
  .schedule-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }
  
  .create-btn {
    width: 100%;
    justify-content: center;
  }
  
  .schedule-layout {
    gap: 12px;
  }
  
  .schedule-sidebar {
    padding: 16px;
  }
  
  .schedule-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }
  
  .toolbar-right {
    justify-content: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .stat-card {
    padding: 8px;
  }
  
  .quick-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex: none;
  }
}

/* 模态框头部操作按钮样式 */
.modal-header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
}

.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.action-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #374151;
}

.action-btn.edit {
  color: #3b82f6;
}

.action-btn.edit:hover {
  background: #eff6ff;
  border-color: #93c5fd;
  color: #1d4ed8;
}

.action-btn.delete {
  color: #ef4444;
}

.action-btn.delete:hover {
  background: #fef2f2;
  border-color: #fca5a5;
  color: #dc2626;
}
