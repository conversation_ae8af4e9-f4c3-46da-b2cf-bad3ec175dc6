// 日程安排主组件
import React, { useState, useEffect } from 'react';
import { 
  Calendar,
  Plus,
  ChevronLeft,
  ChevronRight,
  Search,
  Bell,
  AlertTriangle,
  Users,
  MapPin,
  Calendar as CalendarIcon,
  Eye
} from 'lucide-react';
import { 
  getStatusColor
} from '../../data/mockSchedule';
import { scheduleService } from '../../services';
import { reminderService } from '../../services/reminderService';
import type { Appointment, ViewType } from '../../types/schedule';
import { Button } from '../ui/Button';
import { PageContainer, PageHeader, EmptyState } from '../ui/Layout';
import CreateAppointmentModal from './CreateAppointmentModal';
import AppointmentDetailModal from './AppointmentDetailModal';
import EditAppointmentModal from './EditAppointmentModal';
import './Schedule.css';
import './ButtonFix.css'; // 究极按钮样式修复
import ScheduleOnboarding from './ScheduleOnboarding';

// 辅助函数：获取周的开始日期
function getStartOfWeek(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一作为开始
  return new Date(d.setDate(diff));
}

// 辅助函数：获取周的结束日期
function getEndOfWeek(date: Date): Date {
  const startOfWeek = getStartOfWeek(date);
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  return endOfWeek;
}

const Schedule: React.FC = () => {
  // 使用当前日期作为默认日期
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<ViewType>('day');
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterType, setFilterType] = useState('');
  
  // 数据状态
  const [allAppointments, setAllAppointments] = useState<Appointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [todayOverview, setTodayOverview] = useState({
    total: 0,
    confirmed: 0,
    pending: 0,
    urgent: 0,
    nextAppointment: null as Appointment | null
  });
  const [, setLoading] = useState(true);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const [appointments, overview] = await Promise.all([
        scheduleService.getAllAppointments(),
        scheduleService.getTodayOverview()
      ]);
      setAllAppointments(appointments);
      setTodayOverview({
        ...overview,
        nextAppointment: overview.nextAppointment || null
      });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 设置提醒
  useEffect(() => {
    if (allAppointments.length > 0) {
      // 为所有启用提醒的预约设置提醒
      reminderService.setMultipleReminders(allAppointments);
    }

    // 清理函数：组件卸载时清除所有提醒
    return () => {
      reminderService.clearAllReminders();
    };
  }, [allAppointments]);

  useEffect(() => {
    let appointments = [...allAppointments];

    // 根据视图类型过滤日期
    if (viewType === 'day') {
      const dateStr = currentDate.toISOString().split('T')[0];
      appointments = allAppointments.filter(apt => apt.date === dateStr);
    } else if (viewType === 'week') {
      // 获取本周的预约
      const startOfWeek = getStartOfWeek(currentDate);
      const endOfWeek = getEndOfWeek(currentDate);
      appointments = allAppointments.filter(apt => {
        const aptDate = new Date(apt.date);
        return aptDate >= startOfWeek && aptDate <= endOfWeek;
      });
    } else if (viewType === 'month') {
      // 获取本月的预约
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();
      appointments = allAppointments.filter(apt => {
        const aptDate = new Date(apt.date);
        return aptDate.getFullYear() === year && aptDate.getMonth() === month;
      });
    }

    // 应用搜索和过滤
    let filtered = appointments;

    if (searchQuery) {
      filtered = filtered.filter(apt => 
        apt.visitorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        apt.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
        apt.therapistName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (filterStatus) {
      filtered = filtered.filter(apt => apt.status === filterStatus);
    }

    if (filterType) {
      filtered = filtered.filter(apt => apt.type === filterType);
    }

    // 按时间排序
    filtered.sort((a, b) => a.startTime.localeCompare(b.startTime));

    setFilteredAppointments(filtered);
  }, [allAppointments, currentDate, viewType, searchQuery, filterStatus, filterType]);

  // 日期导航函数
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (viewType === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (viewType === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else if (viewType === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setCurrentDate(newDate);
  };

  // 回到今天
  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // 创建预约
  const handleCreateAppointment = async (appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await scheduleService.createAppointment(appointmentData as any);
      setShowCreateModal(false);
      // 重新加载数据
      await loadData();
    } catch (error) {
      console.error('创建预约失败:', error);
      alert('创建预约失败，请重试');
    }
  };

  // 查看预约详情
  const handleViewAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailModal(true);
  };

  // 编辑预约
  const handleEditAppointment = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setSelectedAppointment(null);
    setShowDetailModal(false);
    setShowEditModal(true);
  };

  // 更新预约
  const handleUpdateAppointment = async (updatedAppointment: Appointment) => {
    try {
      await scheduleService.updateAppointment(updatedAppointment.id, updatedAppointment);
      setShowEditModal(false);
      setEditingAppointment(null);
      // 重新加载数据
      await loadData();
    } catch (error) {
      console.error('更新预约失败:', error);
      alert('更新预约失败，请重试');
    }
  };

  // 删除预约
  const handleDeleteAppointment = async (appointment: Appointment) => {
    if (confirm(`确定要删除 ${appointment.visitorName} 的预约吗？`)) {
      try {
        await scheduleService.deleteAppointment(appointment.id);
        setShowDetailModal(false);
        setSelectedAppointment(null);
        // 重新加载数据
        await loadData();
      } catch (error) {
        console.error('删除预约失败:', error);
        alert('删除预约失败，请重试');
      }
    }
  };

  // 格式化日期显示
  const formatCurrentDate = () => {
    if (viewType === 'day') {
      return currentDate.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });
    } else if (viewType === 'week') {
      const startOfWeek = getStartOfWeek(currentDate);
      const endOfWeek = getEndOfWeek(currentDate);
      return `${startOfWeek.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}`;
    } else {
      return currentDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long'
      });
    }
  };

  return (
    <PageContainer>
      <ScheduleOnboarding />
      <PageHeader
        title="日程安排"
        subtitle="管理和查看预约安排"
        actions={
          <Button
            leftIcon={<Plus size={16} />}
            onClick={() => setShowCreateModal(true)}
            className="create-btn"
          >
            新建预约
          </Button>
        }
      />

      {/* 布局容器 */}
      <div className="schedule-layout">
        {/* 左侧信息面板 */}
        <div className="schedule-sidebar">
          {/* 今日概览 */}
          <div className="today-summary">
            <h3 className="summary-title">
              <CalendarIcon size={16} />
              今日概览
            </h3>
            <div className="summary-stats">
              <div className="stat-card">
                <div className="stat-number">{todayOverview.total}</div>
                <div className="stat-label">总预约</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{todayOverview.confirmed}</div>
                <div className="stat-label">已确认</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{todayOverview.pending}</div>
                <div className="stat-label">待确认</div>
              </div>
              <div className="stat-card urgent">
                <div className="stat-number">{todayOverview.urgent}</div>
                <div className="stat-label">紧急</div>
              </div>
            </div>

            {todayOverview.nextAppointment && (
              <div className="next-appointment">
                <div className="next-label">下一个预约</div>
                <div className="next-info">
                  <div className="next-time">{todayOverview.nextAppointment.startTime}</div>
                  <div className="next-visitor">{todayOverview.nextAppointment.visitorName}</div>
                  <div className="next-subject">{todayOverview.nextAppointment.subject}</div>
                </div>
              </div>
            )}
          </div>

          {/* 日期导航 */}
          <div className="date-navigator">
            <div className="nav-header">
              <button className="nav-btn" onClick={() => navigateDate('prev')}>
                <ChevronLeft size={16} />
              </button>
              <div className="current-date">{formatCurrentDate()}</div>
              <button className="nav-btn" onClick={() => navigateDate('next')}>
                <ChevronRight size={16} />
              </button>
            </div>
            <Button 
              variant="ghost" 
              onClick={goToToday}
              className="today-btn"
            >
              返回今天
            </Button>
          </div>

          {/* 快速筛选 */}
          <div className="quick-filters">
            <h4 className="filter-title">快速筛选</h4>
            <div className="filter-group">
              <label>状态</label>
              <select 
                value={filterStatus} 
                onChange={(e) => setFilterStatus(e.target.value)}
                className="filter-select"
              >
                <option value="">全部状态</option>
                <option value="待确认">待确认</option>
                <option value="已确认">已确认</option>
                <option value="进行中">进行中</option>
                <option value="已完成">已完成</option>
                <option value="已取消">已取消</option>
              </select>
            </div>
            <div className="filter-group">
              <label>类型</label>
              <select 
                value={filterType} 
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="">全部类型</option>
                <option value="个体咨询">个体咨询</option>
                <option value="团体咨询">团体咨询</option>
                <option value="家庭咨询">家庭咨询</option>
                <option value="沙盘疗法">沙盘疗法</option>
                <option value="评估">评估</option>
                <option value="督导">督导</option>
                <option value="自定义">自定义</option>
              </select>
            </div>
          </div>
        </div>

        {/* 主内容区 */}
        <div className="schedule-main">
          {/* 工具栏 */}
          <div className="schedule-toolbar">
            <div className="toolbar-left">
              <div className="view-switcher">
                <button
                  className={`view-btn ${viewType === 'day' ? 'active' : ''}`}
                  onClick={() => setViewType('day')}
                >
                  日视图
                </button>
                <button
                  className={`view-btn ${viewType === 'week' ? 'active' : ''}`}
                  onClick={() => setViewType('week')}
                >
                  周视图
                </button>
                <button
                  className={`view-btn ${viewType === 'month' ? 'active' : ''}`}
                  onClick={() => setViewType('month')}
                >
                  月视图
                </button>
              </div>
            </div>

            <div className="toolbar-right">
              <div className="search-box">
                <Search size={16} />
                <input
                  type="text"
                  placeholder="搜索来访者、主题..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input"
                />
              </div>
              <Button
                variant="secondary"
                leftIcon={<Bell size={16} />}
                onClick={() => console.log('查看提醒')}
              >
                提醒
              </Button>
            </div>
          </div>

          {/* 预约列表区域 */}
          <div className="appointments-area">
            <div className="area-header">
              <h2 className="area-title">
                {viewType === 'day' && '今日预约'}
                {viewType === 'week' && '本周预约'}
                {viewType === 'month' && '本月预约'}
                <span className="count">({filteredAppointments.length})</span>
              </h2>
            </div>

            {filteredAppointments.length === 0 ? (
              <EmptyState
                icon={<Calendar size={64} className="opacity-30" />}
                title={searchQuery || filterStatus || filterType ? '没有找到匹配的预约' : '暂无预约'}
                description={searchQuery || filterStatus || filterType ? '请尝试调整筛选条件' : '该时间段内没有预约记录'}
                action={
                  <Button
                    leftIcon={<Plus size={16} />}
                    onClick={() => setShowCreateModal(true)}
                  >
                    创建新预约
                  </Button>
                }
              />
            ) : (
              <div className="appointments-grid">
                {viewType === 'day' ? (
                  // 日视图：时间线布局
                  filteredAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card day-view">
                      <div className="card-time">
                        <div className="time-main">{appointment.startTime}</div>
                        <div className="time-duration">{appointment.duration}分钟</div>
                      </div>
                      <div className="card-content">
                        <div className="card-header">
                          <h4 className="visitor-name">{appointment.visitorName}</h4>
                          <span 
                            className="status-badge"
                            style={{ backgroundColor: getStatusColor(appointment.status) }}
                          >
                            {appointment.status}
                          </span>
                        </div>
                        <div className="card-body">
                          <div className="subject">{appointment.subject}</div>
                          <div className="details">
                            <span className="type">{appointment.type}</span>
                            <span className="therapist">
                              <Users size={12} />
                              {appointment.therapistName}
                            </span>
                            <span className="room">
                              <MapPin size={12} />
                              {appointment.room}
                            </span>
                          </div>
                        </div>
                        <div className="card-actions">
                          <Button
                            variant="ghost"
                            size="sm"
                            leftIcon={<Eye size={14} />}
                            onClick={() => handleViewAppointment(appointment)}
                          >
                            详情
                          </Button>
                          {appointment.status === '待确认' && (
                            <Button
                              size="sm"
                              onClick={() => console.log('确认预约', appointment.id)}
                            >
                              确认
                            </Button>
                          )}
                        </div>
                      </div>
                      {appointment.urgency !== '普通' && (
                        <div className="urgency-indicator">
                          <AlertTriangle size={14} />
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  // 周视图和月视图：按日期分组
                  (() => {
                    const groups = filteredAppointments.reduce((groups: { [key: string]: Appointment[] }, appointment) => {
                      const date = appointment.date;
                      if (!groups[date]) {
                        groups[date] = [];
                      }
                      groups[date].push(appointment);
                      return groups;
                    }, {});

                    return Object.entries(groups)
                      .sort(([a], [b]) => a.localeCompare(b))
                      .map(([date, dayAppointments]) => (
                        <div key={date} className="day-group">
                          <div className="group-header">
                            <h3 className="group-date">
                              {new Date(date).toLocaleDateString('zh-CN', {
                                month: 'long',
                                day: 'numeric',
                                weekday: 'long'
                              })}
                            </h3>
                            <span className="group-count">{dayAppointments.length}个预约</span>
                          </div>
                          <div className="group-appointments">
                            {dayAppointments.map(appointment => (
                              <div key={appointment.id} className="appointment-item compact">
                                <div className="item-time">{appointment.startTime}</div>
                                <div className="item-content">
                                  <div className="item-visitor">{appointment.visitorName}</div>
                                  <div className="item-subject">{appointment.subject}</div>
                                </div>
                                <div className="item-status">
                                  <span 
                                    className="status-dot"
                                    style={{ backgroundColor: getStatusColor(appointment.status) }}
                                  ></span>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  leftIcon={<Eye size={12} />}
                                  onClick={() => handleViewAppointment(appointment)}
                                >
                                  查看
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ));
                  })()
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建预约弹窗 */}
      {showCreateModal && (
        <CreateAppointmentModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateAppointment}
          initialDate={currentDate.toISOString().split('T')[0]}
        />
      )}

      {/* 预约详情弹窗 */}
      {showDetailModal && selectedAppointment && (
        <AppointmentDetailModal
          isOpen={showDetailModal}
          appointment={selectedAppointment}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedAppointment(null);
          }}
          onEdit={handleEditAppointment}
          onDelete={handleDeleteAppointment}
        />
      )}

      {/* 编辑预约弹窗 */}
      {showEditModal && editingAppointment && (
        <EditAppointmentModal
          isOpen={showEditModal}
          appointment={editingAppointment}
          onClose={() => {
            setShowEditModal(false);
            setEditingAppointment(null);
          }}
          onUpdate={handleUpdateAppointment}
        />
      )}
    </PageContainer>
  );
};

export default Schedule;
