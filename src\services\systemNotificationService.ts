// 系统通知服务
import type { AppSettings } from '../types/settings';

export class SystemNotificationService {
  private static instance: SystemNotificationService;
  private settings: AppSettings | null = null;

  static getInstance(): SystemNotificationService {
    if (!SystemNotificationService.instance) {
      SystemNotificationService.instance = new SystemNotificationService();
    }
    return SystemNotificationService.instance;
  }

  /**
   * 初始化系统通知服务
   */
  initialize(settings: AppSettings): void {
    this.settings = settings;
    this.setupNotificationListeners();
  }

  /**
   * 更新设置
   */
  updateSettings(settings: AppSettings): void {
    this.settings = settings;
  }

  /**
   * 设置通知监听器
   */
  private setupNotificationListeners(): void {
    // 监听系统事件并触发相应通知
    this.listenForUpdates();
    this.listenForErrors();
    this.listenForMaintenance();
  }

  /**
   * 监听系统更新事件
   */
  private listenForUpdates(): void {
    // 这里可以监听实际的系统更新事件
    // 例如：版本检查、自动更新等
    
    // 模拟更新检查
    setInterval(() => {
      this.checkForUpdates();
    }, 24 * 60 * 60 * 1000); // 每天检查一次
  }

  /**
   * 检查系统更新
   */
  private async checkForUpdates(): Promise<void> {
    if (!this.settings?.notifications.system.updates) {
      return;
    }

    try {
      // 这里实现实际的更新检查逻辑
      const hasUpdate = await this.checkUpdateAvailability();
      
      if (hasUpdate) {
        this.showUpdateNotification();
      }
    } catch (error) {
      console.error('检查更新失败:', error);
    }
  }

  /**
   * 检查是否有可用更新
   */
  private async checkUpdateAvailability(): Promise<boolean> {
    // 实现实际的更新检查逻辑
    // 这里返回false作为示例
    return false;
  }

  /**
   * 显示更新通知
   */
  private showUpdateNotification(): void {
    const title = '系统更新可用';
    const body = '发现新版本的系统更新，请点击查看详情。';
    
    this.showSystemNotification(title, body, 'update');
  }

  /**
   * 监听错误事件
   */
  private listenForErrors(): void {
    // 监听全局错误事件
    window.addEventListener('error', (event) => {
      if (this.settings?.notifications.system.errors) {
        this.showErrorNotification(event.error);
      }
    });

    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      if (this.settings?.notifications.system.errors) {
        this.showErrorNotification(event.reason);
      }
    });
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(error: Error): void {
    const title = '系统错误';
    const body = `发生了一个错误：${error.message}`;
    
    this.showSystemNotification(title, body, 'error');
  }

  /**
   * 监听维护事件
   */
  private listenForMaintenance(): void {
    // 这里可以监听维护计划事件
    // 例如：定期维护通知、紧急维护等
  }

  /**
   * 显示维护通知
   */
  showMaintenanceNotification(title: string, body: string): void {
    if (this.settings?.notifications.system.maintenance) {
      this.showSystemNotification(title, body, 'maintenance');
    }
  }

  /**
   * 显示系统通知
   */
  private async showSystemNotification(title: string, body: string, type: string): Promise<void> {
    // 检查是否为Electron环境
    if (window.electronAPI) {
      // 桌面端：使用Electron的系统通知
      try {
        await (window.electronAPI as any).showNotification(title, {
          body,
          icon: 'logo.png',
          tag: `system-${type}-${Date.now()}`,
          requireInteraction: type === 'error', // 错误通知需要交互
          actions: [
            { action: 'view', title: '查看详情' },
            { action: 'dismiss', title: '关闭' }
          ]
        });
      } catch (error) {
        console.error('桌面端系统通知失败:', error);
        this.showFallbackAlert(title, body);
      }
    } else {
      // 浏览器端：检查通知权限
      if ('Notification' in window) {
        if (Notification.permission === 'granted') {
          this.createBrowserNotification(title, body, type);
        } else if (Notification.permission === 'default') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            this.createBrowserNotification(title, body, type);
          } else {
            this.showFallbackAlert(title, body);
          }
        } else {
          this.showFallbackAlert(title, body);
        }
      } else {
        this.showFallbackAlert(title, body);
      }
    }
  }

  /**
   * 创建浏览器通知
   */
  private createBrowserNotification(title: string, body: string, type: string): void {
    const notification = new Notification(title, {
      body,
      icon: '/logo.png',
      tag: `system-${type}-${Date.now()}`,
      requireInteraction: type === 'error'
    });

    notification.onclick = () => {
      window.focus();
      notification.close();
      
      // 根据类型执行不同操作
      switch (type) {
        case 'update':
          this.handleUpdateClick();
          break;
        case 'error':
          this.handleErrorClick();
          break;
        case 'maintenance':
          this.handleMaintenanceClick();
          break;
      }
    };

    // 自动关闭时间（错误通知不自动关闭）
    if (type !== 'error') {
      setTimeout(() => {
        notification.close();
      }, 5000);
    }
  }

  /**
   * 备用提醒方式
   */
  private showFallbackAlert(title: string, body: string): void {
    const message = `${title}\n\n${body}`;
    
    // 使用系统alert作为最后的备用方案
    alert(message);
  }

  /**
   * 处理更新通知点击
   */
  private handleUpdateClick(): void {
    // 导航到更新页面或触发更新流程
    console.log('处理系统更新');
  }

  /**
   * 处理错误通知点击
   */
  private handleErrorClick(): void {
    // 导航到错误日志页面
    console.log('查看错误详情');
  }

  /**
   * 处理维护通知点击
   */
  private handleMaintenanceClick(): void {
    // 导航到维护信息页面
    console.log('查看维护信息');
  }

  /**
   * 手动触发系统通知（用于测试）
   */
  testNotification(type: 'update' | 'error' | 'maintenance'): void {
    switch (type) {
      case 'update':
        this.showUpdateNotification();
        break;
      case 'error':
        this.showErrorNotification(new Error('测试错误消息'));
        break;
      case 'maintenance':
        this.showMaintenanceNotification('系统维护', '计划于今晚进行系统维护，请提前保存工作。');
        break;
    }
  }
}

// 导出单例实例
export const systemNotificationService = SystemNotificationService.getInstance();