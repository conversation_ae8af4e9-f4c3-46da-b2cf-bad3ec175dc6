// 系统设置功能测试脚本
// 这个脚本可以帮助开发者快速测试设置功能

const { settingsService } = require('../src/services/databaseSettingsService');

async function testSettings() {
  console.log('🚀 开始测试系统设置功能...\n');

  try {
    // 1. 测试获取设置
    console.log('1. 测试获取设置...');
    const settings = await settingsService.getSettings();
    console.log('✅ 获取设置成功');
    console.log('   用户姓名:', settings.user.profile.name);
    console.log('   主题模式:', settings.interface.theme.mode);
    console.log('   版本:', settings.version);
    console.log('');

    // 2. 测试更新单个设置
    console.log('2. 测试更新单个设置...');
    const newName = '测试用户_' + Date.now();
    await settingsService.updateSetting('user.profile.name', newName);
    console.log('✅ 更新用户名成功:', newName);
    console.log('');

    // 3. 验证更新
    console.log('3. 验证设置更新...');
    const updatedSettings = await settingsService.getSettings();
    if (updatedSettings.user.profile.name === newName) {
      console.log('✅ 设置更新验证成功');
    } else {
      console.log('❌ 设置更新验证失败');
    }
    console.log('');

    // 4. 测试保存完整设置
    console.log('4. 测试保存完整设置...');
    const newSettings = {
      ...updatedSettings,
      interface: {
        ...updatedSettings.interface,
        theme: {
          ...updatedSettings.interface.theme,
          primaryColor: '#ff6b35'
        }
      }
    };
    
    await settingsService.saveSettings(newSettings);
    console.log('✅ 保存完整设置成功');
    console.log('   新主题色:', newSettings.interface.theme.primaryColor);
    console.log('');

    // 5. 测试获取特定设置
    console.log('5. 测试获取特定设置...');
    const themeColor = await settingsService.getSetting('interface.theme.primaryColor');
    console.log('✅ 获取特定设置成功:', themeColor);
    console.log('');

    console.log('🎉 所有测试通过！系统设置功能正常工作');
    console.log('\n📋 当前设置摘要:');
    console.log('   用户名:', updatedSettings.user.profile.name);
    console.log('   职位:', updatedSettings.user.profile.title);
    console.log('   主题模式:', updatedSettings.interface.theme.mode);
    console.log('   主题色:', updatedSettings.interface.theme.primaryColor);
    console.log('   语言:', updatedSettings.interface.language);
    console.log('   默认咨询时长:', updatedSettings.user.preferences.defaultSessionDuration + '分钟');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    
    if (error.code === 'SQLITE_ERROR') {
      console.log('\n💡 提示: 数据库错误，请确保:');
      console.log('   - 数据库文件存在且有写入权限');
      console.log('   - 数据库表结构正确');
      console.log('   - Electron环境正常运行');
    }
  }
}

// 运行测试
if (require.main === module) {
  testSettings().catch(console.error);
}

module.exports = { testSettings };