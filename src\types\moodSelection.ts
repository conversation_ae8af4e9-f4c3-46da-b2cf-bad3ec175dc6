export type MoodType = 'joy' | 'calm' | 'nervous' | 'melancholy' | 'tired' | 'expectant';

export interface MoodOption {
  id: MoodType;
  emoji: string;
  name: string;
  color: string;
  bgColor: string;
}

export interface MoodResponse {
  id: string;
  moodType: MoodType;
  message: string;
  category: string;
}

export interface MoodRecord {
  id: string;
  moodType: MoodType;
  selectedAt: string;
  responseId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MoodStats {
  totalRecords: number;
  lastMood?: {
    moodType: MoodType;
    selectedAt: string;
    daysAgo: number;
  };
  moodDistribution: Record<MoodType, number>;
  weeklyTrend: Array<{
    date: string;
    moodType: MoodType;
  }>;
}
