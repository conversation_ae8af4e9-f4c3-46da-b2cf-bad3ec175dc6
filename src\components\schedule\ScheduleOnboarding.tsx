import React, { useEffect, useRef, useState } from 'react';
import './ScheduleOnboarding.css';

interface ScheduleOnboardingStep { id:string; title:string; content:string; target?:string|null; }
interface ScheduleOnboardingProps { steps?: ScheduleOnboardingStep[]; storageKey?: string; onFinish?: () => void; }

const defaultSteps: ScheduleOnboardingStep[] = [
  { id: 'welcome', title: '欢迎进入日程安排', content: '这里集中管理全部预约（个体/团体/评估等）。我们带你快速了解关键操作。', target: null },
  { id: 'create', title: '创建预约', content: '点击“新建预约”录入日期、时间、来访者与类型。建议及时确认状态以便统计。', target: 'button.create-btn' },
  { id: 'todaySummary', title: '今日概览', content: '总预约、已确认、待确认与紧急统计，帮助你快速判断今日负载与风险。', target: '.today-summary' },
  { id: 'dateNavigate', title: '日期导航', content: '使用左右箭头切换日期/周/月，或点击“返回今天”快速回到当前日期。', target: '.date-navigator' },
  { id: 'quickFilters', title: '快速筛选', content: '按状态与类型快速过滤，配合右侧搜索框更精确定位目标预约。', target: '.quick-filters' },
  { id: 'viewSwitcher', title: '视图切换', content: '支持日/周/月三种视图。日视图强调时间线；周/月按日期分组。', target: '.view-switcher' },
  { id: 'search', title: '搜索与提醒', content: '搜索来访者、主题或治疗师；点击“提醒”查看或管理提醒任务。', target: '.schedule-toolbar .search-box' },
  { id: 'listArea', title: '预约列表', content: '这里展示筛选后的预约。日视图含时间线/状态标签；周/月按日期组块。', target: '.appointments-area .appointments-grid' },
  { id: 'done', title: '完成', content: '引导结束。可清除 localStorage(schedule_onboarding_done) 重新触发。祝工作顺利！', target: null }
];

const ScheduleOnboarding: React.FC<ScheduleOnboardingProps> = ({ steps = defaultSteps, storageKey='schedule_onboarding_done', onFinish }) => {
  const [current, setCurrent] = useState(0);
  const [visible, setVisible] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const holeRef = useRef<HTMLDivElement|null>(null);
  const tooltipRef = useRef<HTMLDivElement|null>(null);

  useEffect(()=>{ const done = localStorage.getItem(storageKey); if(!done){ const t=setTimeout(()=>setVisible(true),800); return ()=>clearTimeout(t);} },[storageKey]);
  useEffect(()=>{ if(!visible) return; position(); const h=()=>position(); window.addEventListener('resize',h); window.addEventListener('scroll',h,true); return ()=>{window.removeEventListener('resize',h); window.removeEventListener('scroll',h,true);} },[visible,current]);

  const step = steps[current];
  const qs = (sel?:string|null)=> sel? document.querySelector(sel) as HTMLElement|null : null;

  const position = () => {
    if(!holeRef.current || !tooltipRef.current) return; 
    const target = qs(step?.target); 
    const hole=holeRef.current; 
    const tip=tooltipRef.current; 
    let arrow:'top'|'bottom'|'left'|'right'|undefined='top';
    
    if(target){
      const r=target.getBoundingClientRect(); 
      const pad=8; 
      
      // 使用fixed定位避免scroll问题
      hole.style.position = 'fixed';
      hole.style.top=r.top-pad+'px'; 
      hole.style.left=r.left-pad+'px'; 
      hole.style.width=r.width+pad*2+'px'; 
      hole.style.height=r.height+pad*2+'px';
      
      let top=r.bottom+12; 
      let left=r.left; 
      arrow='top';
      
      if(left + tip.offsetWidth > window.innerWidth -16) {
        left = window.innerWidth - tip.offsetWidth -16;
      }
      if(top + tip.offsetHeight > window.innerHeight -16){ 
        top = r.top - tip.offsetHeight -12; 
        arrow='bottom'; 
      }
      left=Math.max(left, 16);
      
      tip.style.position = 'fixed';
      tip.style.top=top+'px'; 
      tip.style.left=left+'px'; 
      tip.classList.remove('schedule-onboarding-center'); 
      tip.setAttribute('data-arrow-pos',arrow);
    } else {
      hole.style.position = 'fixed';
      hole.style.top='-1000px'; 
      hole.style.left='-1000px'; 
      hole.style.width='0'; 
      hole.style.height='0'; 
      
      tip.style.position = 'fixed';
      tip.style.top='50%'; 
      tip.style.left='50%'; 
      tip.classList.add('schedule-onboarding-center'); 
      tip.removeAttribute('data-arrow-pos');
    }
  };

  const next=()=> current < steps.length-1 ? setCurrent(c=>c+1) : finish();
  const prev=()=> current>0 && setCurrent(c=>c-1);
  const skip=()=> finish();
  const finish=()=>{ if(dontShowAgain) localStorage.setItem(storageKey,'1'); else localStorage.removeItem(storageKey); setVisible(false); onFinish?.(); };

  useEffect(()=>{ if(!visible) return; const key=(e:KeyboardEvent)=>{ if(e.key==='Escape') finish(); else if(e.key==='Enter'||e.key==='ArrowRight') next(); else if(e.key==='ArrowLeft') prev(); }; window.addEventListener('keydown',key); return ()=>window.removeEventListener('keydown',key); },[visible,current]);

  if(!visible || !step) return null;

  return (
    <div className="schedule-onboarding-overlay" role="dialog" aria-modal="true">
      <div ref={holeRef} className="schedule-onboarding-hole" aria-hidden="true" />
      <div ref={tooltipRef} className="schedule-onboarding-tooltip" data-step={step.id}>
        <div className="so-header">
          <h4 className="so-title">{step.title}</h4>
          <div className="so-step">{current+1} / {steps.length}</div>
        </div>
        <div className="so-progress" aria-hidden="true"><span style={{ width: `${((current+1)/steps.length)*100}%` }} /></div>
        <div className="so-body" style={{ whiteSpace:'pre-line' }}>{step.content}</div>
        <div className="so-footer">
          <label className="so-dismiss"><input type="checkbox" checked={dontShowAgain} onChange={e=>setDontShowAgain(e.target.checked)} /> 不再显示</label>
          <div className="so-actions">
            {current>0 && <button onClick={prev}>上一步</button>}
            {current < steps.length-1 && <button onClick={skip}>跳过</button>}
            <button className="primary" onClick={next}>{current === steps.length-1 ? '完成' : '下一步'}</button>
          </div>
        </div>
        <div className="schedule-onboarding-steps" aria-label="进度指示">{steps.map((s,i)=><span key={s.id} className={i===current?'active':''} />)}</div>
      </div>
    </div>
  );
};

export default ScheduleOnboarding;
