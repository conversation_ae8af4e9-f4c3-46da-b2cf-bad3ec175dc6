/**
 * 签名器核心模块
 */

import { createSign, createHash, randomBytes } from 'crypto';
import { KeyManager } from './keyManager.js';
import { KeyRegistry } from './keyRegistry.js';
import { encodePayload, LicensePayload } from './payload.js';
import { toB64u } from './base64url.js';

/**
 * 从设备码计算设备哈希
 */
function deviceHashFromCode(deviceCode: string): string {
  const clean = deviceCode.replace(/-/g, '').toUpperCase();
  return toB64u(createHash('sha256').update(clean).digest());
}

/**
 * 构建激活码
 */
export function buildActivationCode(deviceCode: string): {
  activationCode: string;
  payload: LicensePayload;
} {
  const kv = KeyRegistry.getActiveKeyVersion();
  
  const payload: LicensePayload = {
    v: 1,
    kv: kv,
    d: deviceHashFromCode(deviceCode),
    sid: toB64u(randomBytes(16)), // 128bit随机盐
    ia: Date.now()
  };
  
  // 编码payload
  const p64 = encodePayload(payload);
  
  // 加载私钥并签名
  const privateKey = KeyManager.loadPrivateKey();
  const sign = createSign('RSA-SHA256');
  sign.update(p64);
  const signature = sign.sign(privateKey, 'base64');
  
  // 转换为base64url
  const sig64u = toB64u(Buffer.from(signature, 'base64'));
  
  // 组合激活码
  const activationCode = `${p64}.${sig64u}`;
  
  return { activationCode, payload };
}

/**
 * 格式化激活码为便于输入的格式
 */
export function formatActivationCode(code: string, segmentLength: number = 8): string {
  const regex = new RegExp(`.{1,${segmentLength}}`, 'g');
  return code.match(regex)?.join('-') || code;
}
