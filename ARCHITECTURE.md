# 系统架构设计

本文档详细描述沙盘管理系统的整体架构设计，包括技术选型、模块划分、数据流设计和性能考虑。

## 🏗️ 架构概览

### 整体架构
```
┌─────────────────────────────────────────────────┐
│                  桌面应用程序                    │
│                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌───────────┐  │
│  │  主进程     │  │  渲染进程   │  │  预加载   │  │
│  │ (Node.js)   │  │ (React)    │  │ 脚本      │  │
│  └─────────────┘  └─────────────┘  └───────────┘  │
│          │               │               │        │
│          └─────── IPC ───┴───────┘        │        │
│                 │                         │        │
│          ┌──────┴──────┐          ┌──────┴──────┐ │
│          │   SQLite    │          │  文件系统    │ │
│          │  数据库      │          │             │ │
│          └─────────────┘          └─────────────┘ │
└─────────────────────────────────────────────────┘
```

### 技术栈架构
```
前端: React 19 + TypeScript + Vite 7
桌面: Electron 37 + Node.js
数据库: SQLite3
构建: Electron Builder
样式: CSS3 + 设计系统
图标: Lucide React
图表: Recharts
```

## 📦 模块架构

### 主进程模块 (Main Process)

#### 核心职责
- 应用程序生命周期管理
- 原生系统功能集成
- 数据库操作和管理
- 文件系统操作
- 进程间通信(IPC)

#### 主要模块
```typescript
// main.cjs 主入口
const { app, BrowserWindow, ipcMain } = require('electron');
const sqlite3 = require('sqlite3');
const path = require('path');

// 数据库模块
class DatabaseManager {
  constructor() {
    this.db = null;
  }
  
  async initialize() {
    // 数据库初始化和表结构创建
  }
  
  async query(sql, params) {
    // 执行查询
  }
  
  async run(sql, params) {
    // 执行更新
  }
}

// IPC处理器模块
class IPCHandler {
  constructor(databaseManager) {
    this.db = databaseManager;
  }
  
  setupHandlers() {
    // 注册IPC处理器
    ipcMain.handle('db-query', this.handleQuery.bind(this));
    ipcMain.handle('db-run', this.handleRun.bind(this));
    ipcMain.handle('export-data', this.handleExport.bind(this));
  }
}
```

### 渲染进程模块 (Renderer Process)

#### 核心职责
- 用户界面渲染
- 用户交互处理
- 状态管理
- 数据展示

#### React应用结构
```
src/
├── components/          # React组件
│   ├── visitors/        # 来访者管理组件
│   ├── cases/           # 个案管理组件
│   ├── schedule/        # 日程管理组件
│   ├── sandtools/       # 沙具管理组件
│   ├── statistics/     # 统计组件
│   ├── settings/        # 设置组件
│   ├── help/            # 帮助组件
│   └── ui/              # 通用UI组件
├── services/            # 业务服务
│   ├── sqliteDataManager.ts  # 数据管理器
│   ├── visitorService.ts     # 来访者服务
│   ├── caseService.ts        # 个案服务
│   └── ...
├── types/               # TypeScript类型定义
├── config/              # 配置文件
├── styles/              # 样式文件
└── utils/               # 工具函数
```

### 预加载脚本模块 (Preload Script)

#### 核心职责
- 安全地暴露IPC API给渲染进程
- 提供类型安全的接口
- 处理上下文隔离

#### 预加载脚本示例
```javascript
// preload.js
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // 数据库操作API
  query: (params) => ipcRenderer.invoke('db-query', params),
  run: (params) => ipcRenderer.invoke('db-run', params),
  
  // 文件操作API
  exportData: (options) => ipcRenderer.invoke('export-data', options),
  importData: (file) => ipcRenderer.invoke('import-data', file),
  
  // 系统功能API
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  showDialog: (options) => ipcRenderer.invoke('show-dialog', options)
});
```

## 🗄️ 数据架构

### 数据库设计

#### 表结构设计
```sql
-- 来访者表
CREATE TABLE visitors (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  age INTEGER,
  gender TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 个案表
CREATE TABLE cases (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  visitor_id INTEGER NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  session_date DATE NOT NULL,
  duration INTEGER,
  status TEXT DEFAULT 'active',
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (visitor_id) REFERENCES visitors (id)
);

-- 沙具表
CREATE TABLE sand_tools (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  category TEXT,
  quantity INTEGER DEFAULT 0,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 日程表
CREATE TABLE schedules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  visitor_id INTEGER,
  title TEXT NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME NOT NULL,
  description TEXT,
  reminder BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (visitor_id) REFERENCES visitors (id)
);
```

### 数据流设计

#### 数据读取流程
```typescript
// 1. React组件发起数据请求
const VisitorList = () => {
  const [visitors, setVisitors] = useState([]);
  
  useEffect(() => {
    // 2. 调用数据服务
    loadVisitors();
  }, []);
  
  const loadVisitors = async () => {
    // 3. 数据服务调用IPC
    const data = await VisitorService.getVisitors();
    setVisitors(data);
  };
};

// 4. 数据服务处理
class VisitorService {
  static async getVisitors() {
    // 5. 通过预加载脚本调用主进程
    return await window.electronAPI.query({
      sql: 'SELECT * FROM visitors ORDER BY created_at DESC'
    });
  }
}

// 6. 主进程处理查询
ipcMain.handle('db-query', async (event, params) => {
  return await databaseManager.query(params.sql, params.params);
});
```

#### 数据写入流程
```typescript
// 1. 用户提交表单
const handleSubmit = async (formData) => {
  // 2. 数据验证
  const validation = validateVisitorData(formData);
  if (!validation.valid) {
    throw new Error(validation.errors.join(', '));
  }
  
  // 3. 调用创建服务
  const result = await VisitorService.createVisitor(formData);
  
  // 4. 处理结果
  if (result.success) {
    showSuccess('来访者创建成功');
  }
};

// 5. 服务层处理
class VisitorService {
  static async createVisitor(data) {
    // 6. 执行数据库插入
    const result = await window.electronAPI.run({
      sql: 'INSERT INTO visitors (name, phone, email) VALUES (?, ?, ?)',
      params: [data.name, data.phone, data.email]
    });
    
    return { success: result.changes > 0, id: result.lastID };
  }
}
```

## 🎨 UI架构

### 设计系统

#### 色彩系统
```css
:root {
  /* 主色调 */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-active: #1e40af;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  
  /* 语义色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
}
```

#### 排版系统
```css
:root {
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* 字号 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

### 组件架构

#### 原子设计原则
```
原子(Atoms) → 分子(Molecules) → 组织(Organisms) → 模板(Templates) → 页面(Pages)
```

#### 组件示例
```typescript
// Atom: 按钮组件
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

// Molecule: 搜索框
interface SearchBoxProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  debounce?: number;
}

// Organism: 数据表格
interface DataTableProps {
  columns: TableColumn[];
  data: any[];
  pagination?: PaginationConfig;
  onRowClick?: (row: any) => void;
}
```

## 🔧 性能架构

### 数据库性能优化

#### 索引优化
```sql
-- 为常用查询字段创建索引
CREATE INDEX idx_visitors_name ON visitors(name);
CREATE INDEX idx_visitors_phone ON visitors(phone);
CREATE INDEX idx_cases_visitor_id ON cases(visitor_id);
CREATE INDEX idx_cases_session_date ON cases(session_date);
CREATE INDEX idx_schedules_start_time ON schedules(start_time);
```

#### 查询优化
```typescript
// 分页查询避免全表扫描
const getVisitors = async (page: number, pageSize: number) => {
  return await window.electronAPI.query({
    sql: `
      SELECT * FROM visitors 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `,
    params: [pageSize, (page - 1) * pageSize]
  });
};
```

### 前端性能优化

#### 虚拟滚动
```typescript
// 大数据列表使用虚拟滚动
const VirtualizedList = ({ data, itemHeight, renderItem }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerHeight = data.length * itemHeight;
  
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 2,
    data.length
  );
  
  const visibleItems = data.slice(startIndex, endIndex);
  
  return (
    <div onScroll={e => setScrollTop(e.target.scrollTop)}>
      <div style={{ height: containerHeight }}>
        {visibleItems.map((item, index) => (
          <div key={item.id} style={{
            position: 'absolute',
            top: (startIndex + index) * itemHeight,
            height: itemHeight
          }}>
            {renderItem(item)}
          </div>
        ))}
      </div>
    </div>
  );
};
```

#### 数据缓存
```typescript
// 使用React Query进行数据缓存
const { data: visitors, isLoading } = useQuery({
  queryKey: ['visitors'],
  queryFn: () => VisitorService.getVisitors(),
  staleTime: 5 * 60 * 1000, // 5分钟
  cacheTime: 30 * 60 * 1000 // 30分钟
});
```

## 🔐 安全架构

### 数据安全

#### 输入验证
```typescript
// SQL注入防护
const safeQuery = (sql: string, params: any[]) => {
  // 使用参数化查询，避免SQL注入
  return window.electronAPI.query({ sql, params });
};

// XSS防护
const sanitizeHtml = (html: string) => {
  // 使用DOMPurify或其他库清理HTML
  return DOMPurify.sanitize(html);
};
```

#### 数据加密
```typescript
// 敏感数据加密
const encryptData = (data: string, key: string) => {
  // 使用Web Crypto API或第三方库加密数据
  return crypto.subtle.encrypt(
    { name: 'AES-GCM', iv: new Uint8Array(12) },
    key,
    new TextEncoder().encode(data)
  );
};
```

### 应用程序安全

#### 上下文隔离
```javascript
// 预加载脚本确保安全
contextBridge.exposeInMainWorld('electronAPI', {
  // 只暴露必要的API
  query: (params) => ipcRenderer.invoke('db-query', params)
});
```

#### 权限控制
```typescript
// 功能权限检查
const hasPermission = (feature: string) => {
  const userPermissions = getUserPermissions();
  return userPermissions.includes(feature);
};

// 使用示例
if (hasPermission('export_data')) {
  // 显示导出按钮
}
```

## 🚀 部署架构

### 构建流程

#### 多平台构建
```json
{
  "build": {
    "appId": "com.company.xlsp",
    "productName": "沙盘管理系统",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "package.json"
    ],
    "win": {
      "target": "nsis",
      "icon": "public/icon.ico"
    },
    "mac": {
      "target": "dmg",
      "icon": "public/icon.png"
    },
    "linux": {
      "target": "AppImage",
      "icon": "public/icon.png"
    }
  }
}
```

### 更新机制

#### 自动更新
```typescript
// 检查更新
const checkForUpdates = async () => {
  try {
    const updateInfo = await autoUpdater.checkForUpdates();
    if (updateInfo.updateAvailable) {
      // 提示用户更新
      showUpdateDialog(updateInfo);
    }
  } catch (error) {
    console.error('检查更新失败:', error);
  }
};
```

## 📈 监控和日志

### 性能监控
```typescript
// 性能指标收集
const collectMetrics = () => {
  return {
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage(),
    uptime: process.uptime(),
    timestamp: Date.now()
  };
};
```

### 错误日志
```typescript
// 错误处理中间件
const errorHandler = (error: Error) => {
  console.error('应用程序错误:', error);
  
  // 记录到日志文件
  logToFile({
    level: 'error',
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });
  
  // 显示用户友好的错误信息
  showErrorDialog('发生错误', error.message);
};
```

## 🔄 扩展性设计

### 插件架构
```typescript
// 插件接口定义
interface Plugin {
  id: string;
  name: string;
  version: string;
  init: () => Promise<void>;
  destroy: () => Promise<void>;
}

// 插件管理器
class PluginManager {
  private plugins: Map<string, Plugin> = new Map();
  
  async loadPlugin(pluginPath: string) {
    // 动态加载插件
    const plugin = await import(pluginPath);
    await plugin.init();
    this.plugins.set(plugin.id, plugin);
  }
}
```

### API扩展
```typescript
// 可扩展的API架构
interface APIExtension {
  name: string;
  methods: Record<string, Function>;
}

// API管理器
class APIManager {
  private extensions: Map<string, APIExtension> = new Map();
  
  registerExtension(extension: APIExtension) {
    this.extensions.set(extension.name, extension);
  }
  
  getMethod(namespace: string, method: string) {
    return this.extensions.get(namespace)?.methods[method];
  }
}
```

## 📚 相关文档

- [开发指南](DEVELOPMENT.md)
- [API参考](API.md)
- [部署指南](DEPLOYMENT.md)
- [测试策略](TESTING.md)

---
*本文档基于企业级应用程序架构最佳实践，参考了Azure Portal、Visual Studio Code等项目的架构设计*