# 沙盘管理系统 (桌面版) - iFlow 上下文

## 项目概述

这是一个专业的心理健康服务沙盘疗法管理桌面应用，基于 Electron 和现代 Web 技术构建。该应用提供来访者管理、个案记录、日程安排、沙具库存管理、每日心情记录、每日短语等功能，并支持数据的本地存储、备份和导出。

### 核心特性

- **原生桌面应用**：基于 Electron，支持 Windows、macOS、Linux。
- **本地数据存储**：使用 SQLite 本地数据库，确保数据安全可控。
- **高性能界面**：采用 React 19 + TypeScript 构建现代化 UI。
- **离线工作**：无需网络连接，完全离线运行。
- **数据备份**：支持数据导入导出功能（JSON、CSV、Word、PDF）。
- **心理健康辅助功能**：每日心情记录、每日短语等心理支持功能。

### 主要功能模块

- 来访者管理
- 个案管理（支持多种导出格式）
- 日程安排（预约管理）
- 沙具管理
- 团体沙盘管理（已停用）
- 数据统计与可视化
- 系统设置（部分功能待完善）
- 帮助中心
- 每日工作台（心情记录、每日短语）

## 技术栈

### 前端技术

- **UI 框架**：React 19 + TypeScript
- **构建工具**：Vite 7
- **样式**：CSS3 + 现代设计系统
- **图标**：Lucide React
- **图表**：Recharts
- **UI 组件**：自定义组件库
- **动画**：Framer Motion

### 桌面技术

- **桌面框架**：Electron 37
- **进程通信**：IPC (Inter-Process Communication)
- **原生功能**：文件系统、系统菜单、对话框
- **数据库**：better-sqlite3

### 数据存储

- **数据库**：SQLite3
- **数据导出**：CSV、Word(DOCX)、PDF、JSON 格式
- **文件处理**：file-saver, docx, jspdf

### 开发工具

- **代码检查**：ESLint
- **构建**：Electron Builder
- **并发**：concurrently, wait-on

## 构建与运行

### 环境要求

- Node.js 18+
- npm 或 yarn

### 常用命令

- **安装依赖**：`npm install`
- **开发模式**：`npm start` 或 `npm run electron-dev`
- **仅运行前端开发服务器**：`npm run dev`
- **构建应用**：`npm run build`
- **打包桌面应用**：`npm run dist`
- **代码检查**：`npm run lint`

## 开发约定

### 项目结构

```
├── electron/                # Electron主进程文件
│   ├── main.cjs             # 主进程入口（sqlite3数据库集成）
│   └── preload.js           # 预加载脚本（IPC API暴露）
├── src/                     # React前端源码
│   ├── components/          # React组件
│   │   ├── cases/           # 个案管理
│   │   ├── visitors/        # 来访者管理
│   │   ├── sandtools/       # 沙具管理
│   │   ├── schedule/        # 日程管理
│   │   ├── group-sessions/  # 团沙管理
│   │   ├── settings/        # 系统设置（部分功能待完善）
│   │   ├── help/            # 帮助中心
│   │   ├── daily-workspace/ # 每日工作台（心情记录、每日短语）
│   │   └── ui/              # 通用UI组件
│   ├── data/                # 模拟数据和种子数据
│   ├── services/            # 业务逻辑服务
│   │   ├── sqliteDataManager.ts  # SQLite数据管理器
│   │   ├── migrationService.ts   # 数据库迁移服务
│   │   ├── dailyPhraseService.ts # 每日短语服务
│   │   ├── moodSelectionService.ts # 心情选择服务
│   │   ├── databaseSettingsService.ts # 系统设置服务
│   │   └── helpDataService.ts    # 帮助数据服务
│   ├── types/               # TypeScript类型定义
│   ├── config/              # 配置文件
│   ├── styles/              # 全局样式
│   └── utils/               # 工具函数
├── public/                  # 静态资源
├── docs/                    # 项目文档
├── dist/                    # 前端构建输出
├── dist-electron/           # 桌面应用构建输出
└── package.json             # 项目配置
```

### 数据库架构

- **数据库**：SQLite3
- **位置**：用户数据目录 (`app.getPath('userData')/沙盘管理系统/`)
- **文件**：`xlsp.db`
- **引擎**：better-sqlite3 库 (原生异步API)
- **表结构**：来访者、个案、团体会话、沙具管理、每日短语、心情记录、系统设置等
- **IPC通信**：主进程提供 db-query 和 db-run 处理器

### 开发模式

1. 前端开发服务器运行在 `http://localhost:5173`
2. Electron主进程自动加载开发服务器
3. SQLite数据库自动初始化和表结构升级
4. 支持热重载和开发者工具
5. 支持浏览器开发模式（模拟API）

### 构建流程

1. 前端代码构建到 `dist/` 目录
2. Electron Builder打包整个应用
3. better-sqlite3 native模块自动处理
4. 生成平台特定的安装包

## 重要文件说明

- `README.md`：项目介绍和快速开始指南
- `package.json`：项目配置和脚本命令
- `electron/main.cjs`：Electron 主进程，负责数据库初始化和 IPC 通信
- `electron/preload.js`：预加载脚本，暴露安全的 Electron API 给前端
- `src/services/sqliteDataManager.ts`：前端 SQLite 数据访问层
- `src/services/migrationService.ts`：数据库迁移服务
- `src/services/databaseSettingsService.ts`：系统设置服务
- `src/App.tsx`：应用入口，处理初始化逻辑
- `src/services/dailyPhraseService.ts`：每日短语服务
- `src/services/moodSelectionService.ts`：心情选择服务
- `docs/development_guidelines.md`：开发规范和流程