/* 团沙管理页面样式 - 统一风格（参考个案管理） */

/* 统计概览样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
  border-radius: 8px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
  color: var(--primary-blue);
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 表格样式优化 */
.group-sessions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.group-sessions-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
  padding: 12px 8px;
  border-bottom: 2px solid #e5e7eb;
  text-align: left;
}

.group-sessions-table td {
  padding: 12px 8px;
  vertical-align: middle;
  border-bottom: 1px solid #f3f4f6;
}

.group-sessions-table tr:hover {
  background-color: #f9fafb;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-overview {
    gap: var(--spacing-md);
  }
  
  .stats-overview .card {
    min-width: 160px;
  }
  
  .stat-number {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 768px) {
  .stats-overview {
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-md);
  }
  
  .stats-overview .card {
    min-width: 140px;
    max-width: none;
  }
  
  .stat-item {
    padding: var(--spacing-md);
    min-height: 100px;
  }
  
  .stat-number {
    font-size: var(--text-2xl);
  }
  
  .stat-label {
    font-size: var(--text-xs);
  }
}

/* 团体活动卡片网格布局 */
.group-sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: var(--spacing-xl);
}

.group-session-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.group-session-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-blue-light);
}

.session-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.session-card-title {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex: 1;
}

.session-card-title h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
}

.session-card-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  align-items: center;
  flex-wrap: wrap;
}

/* 团体会话操作按钮样式 - 与ActionButtons保持一致 */
.session-card-actions .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: 6px;
  background: #f8fafc;
  color: #475569;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 30px;
  white-space: nowrap;
  text-decoration: none;
}

.session-card-actions .btn:hover {
  transform: translateY(-0.5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.session-card-actions .btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 特殊按钮样式 */
.session-card-actions .btn.btn-ghost {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #93c5fd;
}

.session-card-actions .btn.btn-ghost:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  color: #1d4ed8;
  border-color: #60a5fa;
}

.session-card-actions .btn.btn-danger {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  color: #dc2626;
  border-color: #fca5a5;
}

.session-card-actions .btn.btn-danger:hover {
  background: linear-gradient(135deg, #fecaca 0%, #f87171 100%);
  color: #b91c1c;
  border-color: #ef4444;
}

.session-card-actions .btn.btn-danger:hover,
.session-card-actions .action-btn.delete-btn:hover {
  background-color: #ffe4e6;
  color: #be123c;
}

/* 开始按钮 - 柔和的蓝色文字 */
.session-card-actions .btn.success {
  color: #2563eb;
}

.session-card-actions .btn.success:hover {
  background-color: #dbeafe;
  color: #1d4ed8;
}

/* 暂停按钮 - 柔和的黄色文字 */
.session-card-actions .btn.warning {
  color: #ca8a04;
}

.session-card-actions .btn.warning:hover {
  background-color: #fef9c3;
  color: #a16207;
}

.session-card-actions .btn.leftIcon svg,
.session-card-actions .action-btn svg {
  width: 16px;
  height: 16px;
}

.session-card-body {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.session-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

.session-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--spacing-md);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.age-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-blue-light);
  color: var(--primary-blue);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.participants-display,
.time-display,
.location-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.progress-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.progress-bar-horizontal {
  width: 100%;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-light));
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-xl);
}

.modal-container {
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .group-sessions-grid {
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .filter-controls {
    justify-content: space-between;
  }
  
  .filter-controls .form-group {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .group-sessions-grid {
    grid-template-columns: 1fr;
  }
  
  .session-card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .session-card-actions {
    justify-content: flex-end;
  }
  
  .session-details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .page-actions {
    flex-direction: column;
  }
  
  .page-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .session-card-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .session-card-actions .btn {
    width: 100%;
    justify-content: center;
  }
  
  .session-details-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
  }
}
