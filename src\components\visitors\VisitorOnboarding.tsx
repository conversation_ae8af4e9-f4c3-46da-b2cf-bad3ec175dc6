import React, { useEffect, useRef, useState } from 'react';
import './VisitorOnboarding.css';

interface VisitorOnboardingStep {
  id: string;
  title: string;
  content: string;
  target?: string | null;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
}

interface VisitorOnboardingProps {
  steps?: VisitorOnboardingStep[];
  storageKey?: string;
  onFinish?: () => void;
}

const defaultSteps: VisitorOnboardingStep[] = [
  { id: 'welcome', title: '欢迎', content: '欢迎进入来访者管理中心。通过几个步骤快速了解如何添加、筛选与查看来访者。', target: null },
  { id: 'add', title: '新增来访者', content: '点击"新增来访者"录入基本资料。建议敏感信息使用代号，保护隐私。', target: 'button:has(svg[data-icon="plus"])' },
  { id: 'search', title: '搜索与筛选', content: '页面内搜索框支持姓名、电话、邮箱模糊查询；右侧条件可组合筛选状态、性别与年龄段。', target: '.filter-bar-search-input' },
  { id: 'stats', title: '统计概览', content: '这里显示当前系统内的来访者数量及状态分布，帮助你快速判断工作量。', target: '.stats-row' },
  { id: 'table', title: '列表与快捷操作', content: '在列表中可快速查看核心字段并进行查看/编辑。支持多选批量操作。', target: '.visitors-table' },
  { id: 'select', title: '多选与批量', content: '使用左侧复选框可批量选择来访者，执行删除、查看或编辑（单选时）。', target: '.visitors-table th:nth-child(1)' },
  { id: 'done', title: '完成', content: '引导结束。可在帮助中心或设置中重新启动本引导。祝工作顺利！', target: null }
];

const VisitorOnboarding: React.FC<VisitorOnboardingProps> = ({ steps = defaultSteps, storageKey = 'visitor_onboarding_done', onFinish }) => {
  const [current, setCurrent] = useState(0);
  const [visible, setVisible] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const holeRef = useRef<HTMLDivElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const done = localStorage.getItem(storageKey);
    if (!done) {
      const t = setTimeout(() => setVisible(true), 600);
      return () => clearTimeout(t);
    }
  }, [storageKey]);

  useEffect(() => {
    if (!visible) return;
    positionTooltip();
    const handleResize = () => positionTooltip();
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize, true);
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize, true);
    };
  }, [visible, current]);

  const currentStep = steps[current];

  const queryTarget = (selector?: string | null): HTMLElement | null => {
    if (!selector) return null;
    try {
      const el = document.querySelector(selector) as HTMLElement | null;
      return el || null;
    } catch {
      return null;
    }
  };

  const positionTooltip = () => {
    if (!holeRef.current || !tooltipRef.current) return;
    const targetEl = queryTarget(currentStep?.target);
    const hole = holeRef.current;
    const tooltip = tooltipRef.current;

    let arrowPos: 'top' | 'bottom' | 'left' | 'right' | undefined = 'top';

    if (targetEl) {
      const rect = targetEl.getBoundingClientRect();
      const padding = 8;
      
      const holeRect = {
        top: rect.top - padding,
        left: rect.left - padding,
        width: rect.width + padding * 2,
        height: rect.height + padding * 2
      };
      
      hole.style.position = 'fixed';
      hole.style.top = holeRect.top + 'px';
      hole.style.left = holeRect.left + 'px';
      hole.style.width = holeRect.width + 'px';
      hole.style.height = holeRect.height + 'px';

      const preferredTop = rect.bottom + 12;
      let top = preferredTop;
      let left = rect.left;
      arrowPos = 'top';

      if (left + tooltip.offsetWidth > window.innerWidth - 16) {
        left = window.innerWidth - tooltip.offsetWidth - 16;
      }
      if (top + tooltip.offsetHeight > window.innerHeight - 16) {
        top = rect.top - tooltip.offsetHeight - 12;
        arrowPos = 'bottom';
      }
      left = Math.max(left, 16);

      tooltip.style.position = 'fixed';
      tooltip.style.top = top + 'px';
      tooltip.style.left = left + 'px';
      tooltip.classList.remove('visitor-onboarding-center');
      tooltip.setAttribute('data-arrow-pos', arrowPos);
    } else {
      hole.style.position = 'fixed';
      hole.style.top = '-1000px';
      hole.style.left = '-1000px';
      hole.style.width = '0px';
      hole.style.height = '0px';

      tooltip.style.position = 'fixed';
      tooltip.style.top = '50%';
      tooltip.style.left = '50%';
      tooltip.classList.add('visitor-onboarding-center');
      tooltip.removeAttribute('data-arrow-pos');
    }
  };

  const next = () => {
    if (current < steps.length - 1) {
      setCurrent(c => c + 1);
    } else {
      finish();
    }
  };

  const prev = () => {
    if (current > 0) setCurrent(c => c - 1);
  };

  const skip = () => finish();

  const finish = () => {
    if (dontShowAgain) {
      localStorage.setItem(storageKey, '1');
    } else {
      localStorage.removeItem(storageKey);
    }
    setVisible(false);
    onFinish?.();
  };

  useEffect(() => {
    if (!visible) return;
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        finish();
      } else if (e.key === 'ArrowRight' || e.key === 'Enter') {
        next();
      } else if (e.key === 'ArrowLeft') {
        prev();
      }
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [visible, current]);

  if (!visible) return null;
  if (!currentStep) return null;

  return (
    <div className="visitor-onboarding-overlay" role="dialog" aria-modal="true">
      <div ref={holeRef} className="visitor-onboarding-hole" aria-hidden="true" />
      <div ref={tooltipRef} className="visitor-onboarding-tooltip" data-step={currentStep.id}>
        <div className="vo-header">
          <h4 className="vo-title">{currentStep.title}</h4>
          <div className="vo-step">{current + 1} / {steps.length}</div>
        </div>
        <div className="vo-progress" aria-hidden="true" style={{ ['--total-steps' as any]: steps.length }}>
          <span style={{ width: `${((current + 1) / steps.length) * 100}%` }} />
        </div>
        <div className="vo-body" style={{ whiteSpace: 'pre-line' }}>{currentStep.content}</div>
        <div className="vo-footer">
          <label className="vo-dismiss-option">
            <input
              type="checkbox"
              checked={dontShowAgain}
              onChange={(e) => setDontShowAgain(e.target.checked)}
            />
            不再显示
          </label>
          <div className="vo-actions">
            {current > 0 && <button onClick={prev}>上一步</button>}
            {current < steps.length - 1 && <button onClick={skip}>跳过</button>}
            <button className="primary" onClick={next}>
              {current === steps.length - 1 ? '完成' : '下一步'}
            </button>
          </div>
        </div>
        <div className="visitor-onboarding-steps" aria-label="进度指示">
          {steps.map((s, i) => <span key={s.id} className={i === current ? 'active' : ''} />)}
        </div>
      </div>
    </div>
  );
};

export default VisitorOnboarding;