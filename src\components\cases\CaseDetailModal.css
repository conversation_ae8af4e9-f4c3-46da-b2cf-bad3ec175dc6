/* 个案详情模态框样式 */
@import '../../styles/variables.css';

.case-detail-content {
  max-height: 80vh;
  overflow-y: auto;
  padding: var(--spacing-xl);
}

/* 加载状态 */
.case-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6xl);
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态概览卡片 */
.case-status-overview {
  margin-bottom: var(--spacing-4xl);
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--white);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
}

.status-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.status-card.crisis .status-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-card.progress .status-icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
}

.status-card.homework .status-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-card.star .status-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--accent-amber);
}

.status-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

/* 详情区块 */
.detail-section {
  margin-bottom: var(--spacing-4xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: var(--white);
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  flex: 1;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-item label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.info-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

/* 治疗信息 */
.therapy-info {
  padding: var(--spacing-xl);
}

.therapy-method {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.method-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.therapy-icon {
  color: var(--primary-blue);
}

.method-header h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  flex: 1;
}

.method-description {
  margin: 0;
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

.problem-summary {
  padding: var(--spacing-xl);
}

.problem-summary h5 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.summary-content {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

/* 咨询安排 */
.schedule-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
}

.schedule-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.schedule-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.schedule-value {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.days-ago {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-tertiary);
  margin-left: var(--spacing-sm);
}

/* 临床评估 */
.assessment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
}

.assessment-item {
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.assessment-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.assessment-header h5 {
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.assessment-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.assessment-badge {
  align-self: flex-start;
}

.assessment-description {
  margin: 0;
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

/* 关键词显示 */
.keywords-display {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
}

.keyword-badge {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: var(--primary-blue-darker);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all var(--transition-normal);
}

.keyword-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 督导记录 */
.supervision-content {
  padding: var(--spacing-xl);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--accent-amber);
  margin: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
}

/* 咨询历史时间线 */
.notes-timeline {
  padding: var(--spacing-xl);
}

.note-timeline-item {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-4xl);
  position: relative;
}

.note-timeline-item:last-child {
  margin-bottom: 0;
}

.note-timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  position: relative;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  background: var(--primary-blue);
  border: 3px solid var(--white);
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--primary-blue);
  z-index: 1;
}

.timeline-line {
  width: 2px;
  flex: 1;
  background: var(--border-light);
  margin-top: var(--spacing-sm);
}

.note-timeline-content {
  flex: 1;
  background: var(--white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.note-session-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.session-number {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--primary-blue);
}

.session-date {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.note-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.note-mood {
  font-size: var(--text-lg);
}

.note-content {
  padding: var(--spacing-lg);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
}

.note-quote {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.note-quote em {
  font-style: italic;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.note-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.note-keyword {
  padding: var(--spacing-xs) var(--spacing-md);
  background: rgba(139, 92, 246, 0.1);
  color: var(--accent-purple-dark);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

/* 操作区域 */
.case-detail-footer {
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  padding: var(--spacing-xl);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-detail-content {
    padding: var(--spacing-lg);
  }
  
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .schedule-info {
    grid-template-columns: 1fr;
  }
  
  .assessment-grid {
    grid-template-columns: 1fr;
  }
  
  .note-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .note-session-info {
    flex-wrap: wrap;
  }
  
  .note-meta {
    flex-wrap: wrap;
  }
  
  .footer-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .case-detail-content {
    padding: var(--spacing-md);
  }
  
  .section-header {
    padding: var(--spacing-lg);
  }
  
  .info-grid,
  .therapy-info,
  .schedule-info,
  .assessment-grid,
  .keywords-display,
  .notes-timeline {
    padding: var(--spacing-lg);
  }
  
  .method-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
