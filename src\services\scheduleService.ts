import type { Appointment, ScheduleStats } from '../types/schedule';
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import { sqliteDataManager } from './sqliteDataManager';
import { caseService } from './caseService';
import { visitorService } from './visitorService';
import { settingsService } from './databaseSettingsService';

export class ScheduleService {
  
  // 获取所有预约
  async getAllAppointments(): Promise<Appointment[]> {
    try {
      return await sqliteDataManager.getAllAppointments();
    } catch (error) {
      console.error('获取预约列表失败:', error);
      return [];
    }
  }

  // 根据ID获取预约
  async getAppointment(id: string): Promise<Appointment | null> {
    try {
      return await sqliteDataManager.getAppointment(id);
    } catch (error) {
      console.error('获取预约失败:', error);
      return null;
    }
  }

  // 获取预约及其关联信息
  async getAppointmentWithDetails(id: string): Promise<(Appointment & { case?: SimpleCase; visitor?: Visitor }) | null> {
    try {
      const appointment = await this.getAppointment(id);
      if (!appointment) return null;

      const result: Appointment & { case?: SimpleCase; visitor?: Visitor } = { ...appointment };

      // 获取关联的个案信息
      if (appointment.caseId) {
        const caseData = await caseService.getCase(appointment.caseId);
        if (caseData) {
          result.case = caseData;
        }
      }

      // 获取关联的来访者信息
      if (appointment.visitorId) {
        const visitor = await visitorService.getVisitor(appointment.visitorId);
        if (visitor) {
          result.visitor = visitor;
        }
      }

      return result;
    } catch (error) {
      console.error('获取预约详细信息失败:', error);
      return null;
    }
  }

  // 创建预约
  async createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment> {
    try {
      const id = `appointment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();
      
      const newAppointment: Appointment = {
        ...appointmentData,
        id,
        createdAt: now,
        updatedAt: now
      };

      await sqliteDataManager.saveAppointment(newAppointment);
      
      // 如果关联了个案，更新个案的下次预约时间
      if (newAppointment.caseId) {
        await this.updateCaseNextDate(newAppointment.caseId);
      }

      return newAppointment;
    } catch (error) {
      console.error('创建预约失败:', error);
      throw new Error('创建预约失败');
    }
  }

  // 更新预约
  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment | null> {
    try {
      const existingAppointment = await this.getAppointment(id);
      if (!existingAppointment) return null;

      const updatedAppointment = {
        ...existingAppointment,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await sqliteDataManager.saveAppointment(updatedAppointment);

      // 如果修改了关联的个案或时间，更新个案信息
      if (updatedAppointment.caseId && (updates.date || updates.caseId)) {
        await this.updateCaseNextDate(updatedAppointment.caseId);
      }

      return updatedAppointment;
    } catch (error) {
      console.error('更新预约失败:', error);
      return null;
    }
  }

  // 删除预约
  async deleteAppointment(id: string): Promise<boolean> {
    try {
      await sqliteDataManager.deleteAppointment(id);
      return true;
    } catch (error) {
      console.error('删除预约失败:', error);
      return false;
    }
  }

  // 根据日期获取预约
  async getAppointmentsByDate(date: string): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      return allAppointments
        .filter(apt => apt.date === date)
        .sort((a, b) => a.startTime.localeCompare(b.startTime));
    } catch (error) {
      console.error('获取指定日期预约失败:', error);
      return [];
    }
  }

  // 根据日期范围获取预约
  async getAppointmentsByDateRange(startDate: string, endDate: string): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      return allAppointments
        .filter(apt => apt.date >= startDate && apt.date <= endDate)
        .sort((a, b) => {
          if (a.date === b.date) {
            return a.startTime.localeCompare(b.startTime);
          }
          return a.date.localeCompare(b.date);
        });
    } catch (error) {
      console.error('获取日期范围预约失败:', error);
      return [];
    }
  }

  // 根据个案ID获取预约
  async getAppointmentsByCase(caseId: string): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      return allAppointments
        .filter(apt => apt.caseId === caseId)
        .sort((a, b) => b.date.localeCompare(a.date)); // 按日期倒序
    } catch (error) {
      console.error('获取个案预约失败:', error);
      return [];
    }
  }

  // 根据来访者ID获取预约
  async getAppointmentsByVisitor(visitorId: string): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      return allAppointments
        .filter(apt => apt.visitorId === visitorId)
        .sort((a, b) => b.date.localeCompare(a.date)); // 按日期倒序
    } catch (error) {
      console.error('获取来访者预约失败:', error);
      return [];
    }
  }

  // 搜索预约
  async searchAppointments(query: string): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      const searchTerm = query.toLowerCase();
      
      return allAppointments.filter(appointment =>
        appointment.visitorName.toLowerCase().includes(searchTerm) ||
        appointment.subject.toLowerCase().includes(searchTerm) ||
        appointment.therapistName.toLowerCase().includes(searchTerm) ||
        (appointment.visitorPhone && appointment.visitorPhone.includes(searchTerm)) ||
        appointment.type.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('搜索预约失败:', error);
      return [];
    }
  }

  // 筛选预约
  async filterAppointments(filters: {
    status?: string;
    type?: string;
    therapistId?: string;
    urgency?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<Appointment[]> {
    try {
      const allAppointments = await this.getAllAppointments();
      
      return allAppointments.filter(appointment => {
        if (filters.status && appointment.status !== filters.status) {
          return false;
        }
        
        if (filters.type && appointment.type !== filters.type) {
          return false;
        }
        
        if (filters.therapistId && appointment.therapistId !== filters.therapistId) {
          return false;
        }
        
        if (filters.urgency && appointment.urgency !== filters.urgency) {
          return false;
        }
        
        if (filters.startDate && appointment.date < filters.startDate) {
          return false;
        }
        
        if (filters.endDate && appointment.date > filters.endDate) {
          return false;
        }
        
        return true;
      });
    } catch (error) {
      console.error('筛选预约失败:', error);
      return [];
    }
  }

  // 检查时间冲突
  async checkTimeConflict(
    date: string, 
    startTime: string, 
    endTime: string, 
    therapistId: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      const dayAppointments = await this.getAppointmentsByDate(date);
      const conflictingAppointments = dayAppointments
        .filter(apt => apt.therapistId === therapistId && apt.id !== excludeId);

      return conflictingAppointments.some(apt => {
        return (startTime < apt.endTime && endTime > apt.startTime);
      });
    } catch (error) {
      console.error('检查时间冲突失败:', error);
      return false;
    }
  }

  // 获取今日预约
  async getTodayAppointments(): Promise<Appointment[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      return await this.getAppointmentsByDate(today);
    } catch (error) {
      console.error('获取今日预约失败:', error);
      return [];
    }
  }

  // 获取下一个即将到来的预约
  async getNextUpcomingAppointment(now?: Date): Promise<Appointment | null> {
    try {
      const currentTime = now || new Date();
      const today = currentTime.toISOString().split('T')[0];
      const currentTimeStr = `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`;
      
      // 先获取今天的预约
      const todayAppointments = await this.getAppointmentsByDate(today);
      
      // 查找今天还未开始的预约
      const todayUpcoming = todayAppointments.find(apt => 
        apt.startTime > currentTimeStr && 
        (apt.status === '已确认' || apt.status === '待确认')
      );
      
      if (todayUpcoming) {
        return todayUpcoming;
      }
      
      // 如果今天没有，查找明天及之后的预约
      const tomorrow = new Date(currentTime);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toISOString().split('T')[0];
      
      // 获取未来7天的预约
      const endDate = new Date(currentTime);
      endDate.setDate(endDate.getDate() + 7);
      const endDateStr = endDate.toISOString().split('T')[0];
      
      const futureAppointments = await this.getAppointmentsByDateRange(tomorrowStr, endDateStr);
      const nextAppointment = futureAppointments.find(apt => 
        apt.status === '已确认' || apt.status === '待确认'
      );
      
      return nextAppointment || null;
    } catch (error) {
      console.error('获取下一个预约失败:', error);
      return null;
    }
  }

  // 标记预约为已完成
  async markAppointmentCompleted(id: string): Promise<boolean> {
    try {
      const updated = await this.updateAppointment(id, { 
        status: '已完成',
        updatedAt: new Date().toISOString()
      });
      return updated !== null;
    } catch (error) {
      console.error('标记预约完成失败:', error);
      return false;
    }
  }

  // 获取今日预约统计
  async getTodayAppointmentStats(): Promise<{
    total: number;
    completed: number;
    confirmed: number;
    pending: number;
    cancelled: number;
    nextAppointment: Appointment | null;
  }> {
    try {
      const todayAppointments = await this.getTodayAppointments();
      const nextAppointment = await this.getNextUpcomingAppointment();
      
      return {
        total: todayAppointments.length,
        completed: todayAppointments.filter(apt => apt.status === '已完成').length,
        confirmed: todayAppointments.filter(apt => apt.status === '已确认').length,
        pending: todayAppointments.filter(apt => apt.status === '待确认').length,
        cancelled: todayAppointments.filter(apt => apt.status === '已取消').length,
        nextAppointment
      };
    } catch (error) {
      console.error('获取今日预约统计失败:', error);
      return {
        total: 0,
        completed: 0,
        confirmed: 0,
        pending: 0,
        cancelled: 0,
        nextAppointment: null
      };
    }
  }

  // 计算到下一个预约的时间差（秒）
  async getTimeToNextAppointment(): Promise<{
    appointment: Appointment | null;
    timeRemaining: number; // 秒数，负数表示已过期
    isUpcoming: boolean; // 是否即将到来（10分钟内）
  }> {
    try {
      const now = new Date();
      const nextAppointment = await this.getNextUpcomingAppointment(now);
      
      if (!nextAppointment) {
        return {
          appointment: null,
          timeRemaining: 0,
          isUpcoming: false
        };
      }
      
      // 计算预约时间
      const appointmentDateTime = new Date(`${nextAppointment.date}T${nextAppointment.startTime}:00`);
      const timeRemaining = Math.floor((appointmentDateTime.getTime() - now.getTime()) / 1000);
      const isUpcoming = timeRemaining > 0 && timeRemaining <= 600; // 10分钟内
      
      return {
        appointment: nextAppointment,
        timeRemaining,
        isUpcoming
      };
    } catch (error) {
      console.error('计算预约时间差失败:', error);
      return {
        appointment: null,
        timeRemaining: 0,
        isUpcoming: false
      };
    }
  }

  // 获取预约统计信息
  async getScheduleStats(): Promise<ScheduleStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const todayAppointments = await this.getAppointmentsByDate(today);
      
      // 获取本周数据
      const startOfWeek = this.getStartOfWeek(new Date());
      const endOfWeek = this.getEndOfWeek(new Date());
      const weekAppointments = await this.getAppointmentsByDateRange(
        startOfWeek.toISOString().split('T')[0],
        endOfWeek.toISOString().split('T')[0]
      );
      
      // 获取本月数据
      const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
      const monthAppointments = await this.getAppointmentsByDateRange(
        startOfMonth.toISOString().split('T')[0],
        endOfMonth.toISOString().split('T')[0]
      );
      
      return {
        today: {
          total: todayAppointments.length,
          completed: todayAppointments.filter(apt => apt.status === '已完成').length,
          cancelled: todayAppointments.filter(apt => apt.status === '已取消').length,
          noShow: todayAppointments.filter(apt => apt.status === '缺席').length
        },
        thisWeek: {
          total: weekAppointments.length,
          completed: weekAppointments.filter(apt => apt.status === '已完成').length,
          utilizationRate: weekAppointments.length > 0 ? 
            Math.round((weekAppointments.filter(apt => apt.status === '已完成').length / weekAppointments.length) * 100) : 0
        },
        thisMonth: {
          total: monthAppointments.length,
          revenue: monthAppointments
            .filter(apt => apt.fee && apt.paymentStatus === '已支付')
            .reduce((sum, apt) => sum + (apt.fee || 0), 0),
          newClients: new Set(monthAppointments
            .filter(apt => apt.isFirstSession)
            .map(apt => apt.visitorId)
            .filter(Boolean)).size
        }
      };
    } catch (error) {
      console.error('获取预约统计失败:', error);
      return {
        today: { total: 0, completed: 0, cancelled: 0, noShow: 0 },
        thisWeek: { total: 0, completed: 0, utilizationRate: 0 },
        thisMonth: { total: 0, revenue: 0, newClients: 0 }
      };
    }
  }

  // 更新个案的下次预约时间
  private async updateCaseNextDate(caseId: string): Promise<void> {
    try {
      // 获取该个案的所有预约
      const caseAppointments = await this.getAppointmentsByCase(caseId);
      
      // 找到最近的未来预约
      const now = new Date().toISOString().split('T')[0];
      const futureAppointments = caseAppointments
        .filter(apt => apt.date >= now && apt.status !== '已取消')
        .sort((a, b) => a.date.localeCompare(b.date));
      
      const nextDate = futureAppointments.length > 0 ? futureAppointments[0].date : undefined;
      
      // 更新个案的nextDate
      await caseService.updateCase(caseId, { nextDate });
    } catch (error) {
      console.error('更新个案下次预约时间失败:', error);
    }
  }

  // 辅助函数：获取周的开始日期
  private getStartOfWeek(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一作为开始
    return new Date(d.setDate(diff));
  }

  // 辅助函数：获取周的结束日期
  private getEndOfWeek(date: Date): Date {
    const startOfWeek = this.getStartOfWeek(date);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    return endOfWeek;
  }

  // 获取可用时间段
  async getAvailableTimeSlots(
    date: string, 
    therapistId: string, 
    duration: number = 50
  ): Promise<{ start: string; end: string }[]> {
    try {
      // 获取工作偏好设置
      const settings = await settingsService.getSettings();
      const workingHours = settings.user.preferences.workingHours;
      
      // 解析工作时间段
      workingHours.start.split(':').map(Number);
      workingHours.end.split(':').map(Number);
      const workStart = workingHours.start;
      const workEnd = workingHours.end;
      const slots: { start: string; end: string }[] = [];
      
      // 获取当天已有预约
      const appointments = await this.getAppointmentsByDate(date);
      const therapistAppointments = appointments
        .filter(apt => apt.therapistId === therapistId)
        .sort((a, b) => a.startTime.localeCompare(b.startTime));

      let currentTime = workStart;
      
      for (const apt of therapistAppointments) {
        // 检查当前时间到预约开始时间是否有足够空隙
        const gap = this.getTimeDiffInMinutes(currentTime, apt.startTime);
        if (gap >= duration) {
          slots.push({
            start: currentTime,
            end: this.addMinutesToTime(currentTime, duration)
          });
        }
        currentTime = apt.endTime;
      }
      
      // 检查最后一个预约后到下班时间
      const finalGap = this.getTimeDiffInMinutes(currentTime, workEnd);
      if (finalGap >= duration) {
        slots.push({
          start: currentTime,
          end: this.addMinutesToTime(currentTime, duration)
        });
      }
      
      return slots;
    } catch (error) {
      console.error('获取可用时间段失败:', error);
      return [];
    }
  }

  // 辅助函数：计算时间差（分钟）
  private getTimeDiffInMinutes(startTime: string, endTime: string): number {
    const start = new Date(`2024-01-01T${startTime}:00`);
    const end = new Date(`2024-01-01T${endTime}:00`);
    return (end.getTime() - start.getTime()) / (1000 * 60);
  }

  // 辅助函数：给时间增加分钟
  private addMinutesToTime(time: string, minutes: number): string {
    const date = new Date(`2024-01-01T${time}:00`);
    date.setMinutes(date.getMinutes() + minutes);
    return date.toTimeString().slice(0, 5);
  }

  // 批量创建预约
  async createAppointmentsBatch(appointmentsData: Array<Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>>): Promise<{
    success: Appointment[];
    failed: Array<{ data: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>; error: string }>;
  }> {
    const success: Appointment[] = [];
    const failed: Array<{ data: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>; error: string }> = [];

    for (const appointmentData of appointmentsData) {
      try {
        const newAppointment = await this.createAppointment(appointmentData);
        success.push(newAppointment);
      } catch (error) {
        failed.push({
          data: appointmentData,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    return { success, failed };
  }

  // 获取今日概览
  async getTodayOverview(): Promise<{
    total: number;
    confirmed: number;
    pending: number;
    urgent: number;
    nextAppointment: Appointment | null;
  }> {
    try {
      const todayAppointments = await this.getTodayAppointments();
      const nextAppointment = await this.getNextUpcomingAppointment();
      const confirmed = todayAppointments.filter(a => a.status === '已确认' || a.status === '进行中').length;
      const pending = todayAppointments.filter(a => a.status === '待确认').length;
      const urgent = todayAppointments.filter(a => a.urgency === '紧急' || a.urgency === '危机干预').length;
      return {
        total: todayAppointments.length,
        confirmed,
        pending,
        urgent,
        nextAppointment
      };
    } catch (err) {
      console.error('获取今日概览失败:', err);
      return { total: 0, confirmed: 0, pending: 0, urgent: 0, nextAppointment: null };
    }
  }
}

// 导出实例
export const scheduleService = new ScheduleService();
