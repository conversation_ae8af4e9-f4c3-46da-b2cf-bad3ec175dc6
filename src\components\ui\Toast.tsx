import React, { createContext, useContext, useState, useCallback, useRef, useEffect, type ReactNode } from 'react';
import '../ui/Toast.css';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface ToastItem {
  id: string;
  type: ToastType;
  message: string;
  duration: number; // ms
  createdAt: number;
  dismissible?: boolean;
  icon?: ReactNode;
}

interface ToastContextValue {
  push: (message: string, options?: { type?: ToastType; duration?: number; dismissible?: boolean; icon?: ReactNode; dedupeKey?: string }) => void;
  success: (message: string, duration?: number) => void;
  error: (message: string, duration?: number) => void;
  info: (message: string, duration?: number) => void;
  warning: (message: string, duration?: number) => void;
}

const ToastContext = createContext<ToastContextValue | null>(null);

export const ToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const timersRef = useRef<Record<string, number>>({});

  const remove = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
    const timer = timersRef.current[id];
    if (timer) {
      window.clearTimeout(timer);
      delete timersRef.current[id];
    }
  }, []);

  const push = useCallback((message: string, options: { type?: ToastType; duration?: number; dismissible?: boolean; icon?: ReactNode; dedupeKey?: string } = {}) => {
    // 简单去重：如果 dedupeKey 存在且最近 1.2s 内已有同类，不再追加
    if (options.dedupeKey) {
      const now = Date.now();
      const hit = toastsRef.current.find(t => (t as any).dedupeKey === options.dedupeKey && now - t.createdAt < 1200);
      if (hit) return;
    }
    const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
    const toast: ToastItem & { dedupeKey?: string } = {
      id,
      type: options.type || 'info',
      message,
      duration: options.duration ?? 2500,
      createdAt: Date.now(),
      dismissible: options.dismissible !== false,
      icon: options.icon,
      dedupeKey: options.dedupeKey
    };
    setToasts(prev => [...prev, toast]);
    toastsRef.current = [...toastsRef.current, toast];
    timersRef.current[id] = window.setTimeout(() => remove(id), toast.duration);
  }, [remove]);

  const api: ToastContextValue = {
    push,
    success: (m, d) => push(m, { type: 'success', duration: d }),
    error: (m, d) => push(m, { type: 'error', duration: d ?? 3200 }),
    info: (m, d) => push(m, { type: 'info', duration: d }),
    warning: (m, d) => push(m, { type: 'warning', duration: d }),
  };

  useEffect(() => () => {
    Object.values(timersRef.current).forEach(id => window.clearTimeout(id));
  }, []);

  // 保存 toasts 的 ref 以便去重检测
  const toastsRef = useRef<(ToastItem & { dedupeKey?: string })[]>([]);
  useEffect(()=>{ toastsRef.current = toasts; }, [toasts]);

  return (
    <ToastContext.Provider value={api}>
      {children}
      <div className="app-toast-container" role="status" aria-live="polite">
        {toasts.map(t => (
          <div
            key={t.id}
            className={`app-toast app-toast-${t.type}`}
            role="alert"
            onClick={() => t.dismissible && remove(t.id)}
          >
            <span className="app-toast-bar" />
            {t.icon && <span className="app-toast-icon">{t.icon}</span>}
            <span className="app-toast-msg">{t.message}</span>
            {t.dismissible && (
              <button
                aria-label="关闭通知"
                className="app-toast-close"
                onClick={(e)=>{ e.stopPropagation(); remove(t.id); }}
              >×</button>
            )}
            <span className="app-toast-progress" style={{ animationDuration: `${t.duration}ms` }} />
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextValue => {
  const ctx = useContext(ToastContext);
  if (!ctx) throw new Error('useToast must be used within <ToastProvider>');
  return ctx;
};
