import React, { useState, useEffect } from 'react';
import { visitorService } from '../../services/visitorService';
import type { Visitor, VisitorFilters } from '../../types/visitor';
import {
  Card,
  CardContent,
  Button,
  Input,
  Select,
  Badge,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  PageContainer,
  PageHeader,
  Grid,
  LoadingSpinner,
  EmptyState,
  FilterBar,
  ActionButtons,
  Pagination
} from '../ui';
import { Plus, Search, UserX, Upload, Eye, Edit, Trash2, CheckSquare, Square, Database } from 'lucide-react';
import CreateVisitorModal from './CreateVisitorModal';
import ViewVisitorModal from './ViewVisitorModal';
import EditVisitorModal from './EditVisitorModal';
import BatchImportVisitorModal from './BatchImportVisitorModal.enhanced';
import { TestVisitorGenerator } from '../../utils/generateTestVisitors';
import VisitorOnboarding from './VisitorOnboarding';
import './Visitors.css';

const Visitors: React.FC = () => {
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBatchImportModal, setShowBatchImportModal] = useState(false);
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [selectedVisitorIds, setSelectedVisitorIds] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<VisitorFilters>({
    search: '',
    status: '',
    gender: '',
    ageRange: ''
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  // 加载来访者数据
  const loadVisitors = async () => {
    try {
      setLoading(true);
      const data = await visitorService.getAllVisitors();
      setVisitors(data);
    } catch (error) {
      console.error('加载来访者数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadVisitors();
  }, []);

  // 筛选逻辑
  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                         visitor.phone.includes(filters.search) ||
                         (visitor.email && visitor.email.toLowerCase().includes(filters.search.toLowerCase()));
    
    const matchesStatus = !filters.status || visitor.status === filters.status;
    const matchesGender = !filters.gender || visitor.gender === filters.gender;
    
    let matchesAge = true;
    if (filters.ageRange) {
      const [min, max] = filters.ageRange.split('-').map(Number);
      if (!isNaN(min) && !isNaN(max)) {
        matchesAge = visitor.age >= min && visitor.age <= max;
      }
    }

    return matchesSearch && matchesStatus && matchesGender && matchesAge;
  });

  // 分页计算
  const totalItems = filteredVisitors.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentPageVisitors = filteredVisitors.slice(startIndex, endIndex);

  // 当筛选条件改变时重置到第一页
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters.search, filters.status, filters.gender, filters.ageRange]);

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 改变每页显示数量时重置到第一页
  };

  const handleCreateVisitor = async (newVisitor: Visitor) => {
    setVisitors([...visitors, newVisitor]);
    setShowCreateModal(false);
    // 重新加载数据确保同步
    await loadVisitors();
  };

  const handleViewVisitor = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setShowViewModal(true);
  };

  const handleEditVisitor = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setShowViewModal(false);
    setShowEditModal(true);
  };

  const handleUpdateVisitor = async (updatedVisitor: Visitor) => {
    setVisitors(visitors.map(v => v.id === updatedVisitor.id ? updatedVisitor : v));
    setShowEditModal(false);
    // 重新加载数据确保同步
    await loadVisitors();
  };

  const handleBatchImport = async (visitorsData: Array<Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>>) => {
    try {
      // 兼容：若服务层没有createVisitorsBatch则逐条创建
      let successList: Visitor[] = [];
      let failedList: Array<{ data: unknown; error: string }>=[];

      if ((visitorService as any).createVisitorsBatch) {
        // 使用批量接口
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result: any = await (visitorService as any).createVisitorsBatch(visitorsData);
        successList = result.success || [];
        failedList = result.failed || [];
      } else {
        for (const data of visitorsData) {
          try {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const created: any = await visitorService.createVisitor(data as any);
            if (created) successList.push(created as Visitor);
          } catch (e) {
            failedList.push({ data, error: (e as Error).message });
          }
        }
      }

      if (successList.length > 0) {
        setVisitors(prev => [...prev, ...successList]);
        const successCount = successList.length;
        const failedCount = failedList.length;
        if (failedCount === 0) {
          alert(`成功导入 ${successCount} 位来访者！`);
        } else {
          alert(`成功导入 ${successCount} 位来访者，${failedCount} 位导入失败。请检查失败的数据并重新导入。`);
        }
      } else {
        alert('导入失败，请检查数据格式是否正确。');
      }
      setShowBatchImportModal(false);
    } catch (error) {
      console.error('批量导入失败:', error);
      alert('导入过程中发生错误，请稍后重试。');
    }
  };

  const handleUpdateFilters = (field: keyof VisitorFilters, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  // 选择相关处理函数
  const handleSelectVisitor = (visitorId: string) => {
    setSelectedVisitorIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(visitorId)) {
        newSet.delete(visitorId);
      } else {
        newSet.add(visitorId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    const allFilteredIds = filteredVisitors.map(v => v.id);
    const allFilteredSelected = allFilteredIds.every(id => selectedVisitorIds.has(id));
    
    if (allFilteredSelected) {
      // 取消选择所有过滤后的项目
      setSelectedVisitorIds(prev => {
        const newSet = new Set(prev);
        allFilteredIds.forEach(id => newSet.delete(id));
        return newSet;
      });
    } else {
      // 选择所有过滤后的项目
      setSelectedVisitorIds(prev => {
        const newSet = new Set(prev);
        allFilteredIds.forEach(id => newSet.add(id));
        return newSet;
      });
    }
  };

  const getSelectedVisitors = () => {
    return visitors.filter(v => selectedVisitorIds.has(v.id));
  };

  // 批量操作函数
  const handleBatchView = () => {
    const selected = getSelectedVisitors();
    if (selected.length === 1) {
      handleViewVisitor(selected[0]);
    } else {
      alert(`已选择 ${selected.length} 位来访者，请选择单个来访者进行查看`);
    }
  };

  const handleBatchEdit = () => {
    const selected = getSelectedVisitors();
    if (selected.length === 1) {
      handleEditVisitor(selected[0]);
    } else {
      alert(`已选择 ${selected.length} 位来访者，请选择单个来访者进行编辑`);
    }
  };

  const handleBatchDelete = async () => {
    const selected = getSelectedVisitors();
    if (selected.length === 0) return;
    
    const names = selected.map(v => v.name).join('、');
    if (confirm(`确定要删除以下 ${selected.length} 位来访者吗？\n${names}\n\n此操作不可恢复。`)) {
      try {
        let successCount = 0;
        let failedCount = 0;
        
        for (const visitor of selected) {
          const success = await visitorService.deleteVisitor(visitor.id);
          if (success) {
            successCount++;
          } else {
            failedCount++;
          }
        }
        
        // 更新本地状态
        const deletedIds = new Set(selected.filter((_, index) => index < successCount).map(v => v.id));
        setVisitors(visitors.filter(v => !deletedIds.has(v.id)));
        setSelectedVisitorIds(new Set());
        
        if (failedCount === 0) {
          alert(`成功删除 ${successCount} 位来访者`);
        } else {
          alert(`成功删除 ${successCount} 位来访者，${failedCount} 位删除失败`);
        }
        
        // 重新加载数据确保同步
        await loadVisitors();
      } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败，请稍后重试');
      }
    }
  };

  // 生成测试数据
  const handleGenerateTestData = async () => {
    if (confirm('确定要生成100个测试来访者数据吗？这将添加到现有数据中。')) {
      try {
        setLoading(true);
        const generator = new TestVisitorGenerator();
        const result = await generator.createTestVisitors(100);
        
        alert(`测试数据生成完成！\n成功创建: ${result.success}个\n失败: ${result.failed}个`);
        
        // 重新加载数据
        await loadVisitors();
      } catch (error) {
        console.error('生成测试数据失败:', error);
        alert('生成测试数据失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <PageContainer>
      {/* 引导组件：仅首次显示 */}
      <VisitorOnboarding />
      <PageHeader
        title="来访者管理"
        subtitle="管理和查看所有来访者信息"
        actions={
          <div className="flex gap-sm">
            <Button
              variant="secondary"
              leftIcon={<Database size={16} />}
              onClick={handleGenerateTestData}
              disabled={loading}
              className="btn btn-secondary"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                fontSize: '14px',
                fontWeight: '500',
                borderRadius: '6px',
                cursor: 'pointer',
                transition: '0.12s',
                textDecoration: 'none',
                whiteSpace: 'nowrap',
                border: '1px solid rgb(209, 213, 219)',
                lineHeight: '1',
                height: '40px',
                boxSizing: 'border-box',
                background: 'white',
                color: 'rgb(55, 65, 81)'
              }}
            >
              生成测试数据
            </Button>
            <Button
              variant="secondary"
              leftIcon={<Upload size={16} />}
              onClick={() => setShowBatchImportModal(true)}
              className="btn btn-secondary"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                fontSize: '14px',
                fontWeight: '500',
                borderRadius: '6px',
                cursor: 'pointer',
                transition: '0.12s',
                textDecoration: 'none',
                whiteSpace: 'nowrap',
                border: '1px solid rgb(209, 213, 219)',
                lineHeight: '1',
                height: '40px',
                boxSizing: 'border-box',
                background: 'white',
                color: 'rgb(55, 65, 81)'
              }}
            >
              批量导入
            </Button>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={() => setShowCreateModal(true)}
            >
              新增来访者
            </Button>
          </div>
        }
      />

      {/* 搜索和筛选区域 */}
      <Card className="mb-xl">
        <CardContent>
          <FilterBar
            searchProps={{
              value: filters.search,
              onChange: (e) => handleUpdateFilters('search', e.target.value),
              placeholder: "搜索姓名、电话或邮箱...",
              leftIcon: <Search size={16} />
            }}
            filters={[
              {
                value: filters.status,
                onChange: (e) => handleUpdateFilters('status', e.target.value),
                options: [
                  { value: '', label: '全部状态' },
                  { value: '活跃', label: '活跃' },
                  { value: '暂停', label: '暂停' },
                  { value: '完成', label: '完成' }
                ]
              },
              {
                value: filters.gender,
                onChange: (e) => handleUpdateFilters('gender', e.target.value),
                options: [
                  { value: '', label: '全部性别' },
                  { value: '男', label: '男' },
                  { value: '女', label: '女' }
                ]
              },
              {
                value: filters.ageRange,
                onChange: (e) => handleUpdateFilters('ageRange', e.target.value),
                options: [
                  { value: '', label: '全部年龄' },
                  { value: '18-25', label: '18-25岁' },
                  { value: '26-35', label: '26-35岁' },
                  { value: '36-45', label: '36-45岁' },
                  { value: '46-60', label: '46-60岁' },
                  { value: '60-100', label: '60岁以上' }
                ]
              }
            ]}
          />
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="stats-row mb-xl">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{visitors.length}</div>
              <div className="stat-label">总来访者</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">
                {visitors.filter(v => v.status === '活跃').length}
              </div>
              <div className="stat-label">活跃中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">
                {visitors.filter(v => v.status === '暂停').length}
              </div>
              <div className="stat-label">暂停中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">
                {visitors.filter(v => v.status === '完成').length}
              </div>
              <div className="stat-label">已完成</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 批量操作工具栏 */}
      {selectedVisitorIds.size > 0 && (
        <Card className="mb-lg">
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-md">
                <span className="text-sm text-gray-600">
                  已选择 {selectedVisitorIds.size} 位来访者
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedVisitorIds(new Set())}
                >
                  取消选择
                </Button>
              </div>
              <div className="flex gap-sm">
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<Eye size={14} />}
                  onClick={handleBatchView}
                  disabled={selectedVisitorIds.size !== 1}
                >
                  查看
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<Edit size={14} />}
                  onClick={handleBatchEdit}
                  disabled={selectedVisitorIds.size !== 1}
                >
                  编辑
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  leftIcon={<Trash2 size={14} />}
                  onClick={handleBatchDelete}
                  disabled={selectedVisitorIds.size === 0}
                >
                  删除 ({selectedVisitorIds.size})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 来访者列表 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner size="lg" />
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : currentPageVisitors.length === 0 ? (
            <EmptyState
              icon={<UserX size={48} />}
              title="暂无来访者数据"
              description="还没有添加任何来访者信息，点击上方按钮开始添加。"
              action={
                <Button
                  leftIcon={<Plus size={16} />}
                  onClick={() => setShowCreateModal(true)}
                >
                  新增来访者
                </Button>
              }
            />
          ) : (
            <Table className="visitors-table">
              <TableHeader>
                <TableRow>
                  <TableCell isHeader style={{ width: '50px' }}>
                    <button
                      onClick={handleSelectAll}
                      className="flex items-center justify-center w-5 h-5 text-gray-500 hover:text-gray-700"
                      title={filteredVisitors.length > 0 && filteredVisitors.every(v => selectedVisitorIds.has(v.id)) ? '取消全选' : '全选所有'}
                    >
                      {filteredVisitors.length > 0 && filteredVisitors.every(v => selectedVisitorIds.has(v.id)) ? (
                        <CheckSquare size={16} />
                      ) : (
                        <Square size={16} />
                      )}
                    </button>
                  </TableCell>
                  <TableCell isHeader>姓名</TableCell>
                  <TableCell isHeader>性别</TableCell>
                  <TableCell isHeader>年龄</TableCell>
                  <TableCell isHeader>职业</TableCell>
                  <TableCell isHeader>地址</TableCell>
                  <TableCell isHeader>状态</TableCell>
                  <TableCell isHeader>快捷操作</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageVisitors.map(visitor => (
                  <TableRow 
                    key={visitor.id}
                    className={selectedVisitorIds.has(visitor.id) ? 'selected' : ''}
                  >
                    <TableCell>
                      <button
                        onClick={() => handleSelectVisitor(visitor.id)}
                        className="flex items-center justify-center w-5 h-5 text-gray-500 hover:text-gray-700"
                        title={selectedVisitorIds.has(visitor.id) ? '取消选择' : '选择'}
                      >
                        {selectedVisitorIds.has(visitor.id) ? (
                          <CheckSquare size={16} className="text-blue-600" />
                        ) : (
                          <Square size={16} />
                        )}
                      </button>
                    </TableCell>
                    <TableCell>
                      <div className="visitor-name">
                        <div className="name">{visitor.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>{visitor.gender}</TableCell>
                    <TableCell>{visitor.age}岁</TableCell>
                    <TableCell>{visitor.occupation || '-'}</TableCell>
                    <TableCell>
                      <div className="address-cell" title={visitor.address || '-'}>
                        {visitor.address || '-'}
                      </div>
                    </TableCell>
                    <TableCell className="status-cell">
                      <Badge 
                        variant={
                          visitor.status === '活跃' ? 'success' : 
                          visitor.status === '暂停' ? 'warning' : 
                          'gray'
                        }
                      >
                        {visitor.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewVisitor(visitor)}
                          title="查看详情"
                        >
                          <Eye size={14} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditVisitor(visitor)}
                          title="编辑"
                        >
                          <Edit size={14} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          
          {/* 分页组件 */}
          {filteredVisitors.length > 0 && (
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                showTotal={true}
                pageSizeOptions={[12, 24, 48, 96]}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建来访者弹窗 */}
      <CreateVisitorModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateVisitor}
      />

      {/* 查看来访者弹窗 */}
      {selectedVisitor && (
        <ViewVisitorModal
          isOpen={showViewModal}
          visitor={selectedVisitor}
          onClose={() => setShowViewModal(false)}
          onEdit={handleEditVisitor}
        />
      )}

      {/* 编辑来访者弹窗 */}
      {selectedVisitor && (
        <EditVisitorModal
          isOpen={showEditModal}
          visitor={selectedVisitor}
          onClose={() => setShowEditModal(false)}
          onSubmit={handleUpdateVisitor}
        />
      )}

      {/* 批量导入来访者弹窗 */}
      <BatchImportVisitorModal
        isOpen={showBatchImportModal}
        onClose={() => setShowBatchImportModal(false)}
        onSubmit={handleBatchImport}
      />
    </PageContainer>
  );
};

export default Visitors;
