import React, { useState, useEffect } from 'react';
import { RefreshCw, Heart } from 'lucide-react';
import { dailyPhraseService } from '../../services/dailyPhraseService';
import type { DailyPhrase } from '../../types/dailyPhrase';
import './DailyPhrase.css';

interface DailyPhraseProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help') => void;
}

const DailyPhrase: React.FC<DailyPhraseProps> = () => {
  const [currentPhrase, setCurrentPhrase] = useState<DailyPhrase | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  const [loading, setLoading] = useState(true);

  // 加载今日短语
  const loadTodayPhrase = async () => {
    try {
      setLoading(true);
      
      // 检查环境
      const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
      
      if (isElectron) {
        const phrase = await dailyPhraseService.getTodayPhrase();
        setCurrentPhrase(phrase);
      } else {
        // 浏览器环境使用静态数据
        const mockPhrase = {
          id: 'mock_1',
          chinese: "今天也要元气满满呀！",
          pinyin: "Jīntiān yě yào yuánqì mǎnmǎn ya!",
          english: "Today, let's be full of energy too!",
          category: "积极向上",
          description: "充满活力的正能量短语",
          isActive: true,
          usageCount: 0,
          tags: ["元气", "活力", "正能量"],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setCurrentPhrase(mockPhrase);
      }
      
      setIsLiked(false);
      setShowTranslation(false);
    } catch (error) {
      console.error('加载今日短语失败:', error);
      // 发生错误时使用备用短语
      const fallbackPhrase = {
        id: 'fallback_1',
        chinese: "保持微笑，拥抱美好",
        pinyin: "Bǎochí wēixiào, yōngbào měihǎo",
        english: "Keep smiling, embrace the beautiful",
        category: "温暖治愈",
        description: "用微笑面对生活",
        isActive: true,
        usageCount: 0,
        tags: ["微笑", "美好", "治愈"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setCurrentPhrase(fallbackPhrase);
    } finally {
      setLoading(false);
    }
  };

  // 获取随机短语
  const getRandomPhrase = async () => {
    try {
      setLoading(true);
      
      const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
      
      if (isElectron) {
        const phrase = await dailyPhraseService.getRandomPhrase();
        setCurrentPhrase(phrase);
      } else {
        // 浏览器环境使用静态短语池
        const mockPhrases = [
          {
            id: 'mock_1',
            chinese: "今天也要元气满满呀！",
            pinyin: "Jīntiān yě yào yuánqì mǎnmǎn ya!",
            english: "Today, let's be full of energy too!",
            category: "积极向上"
          },
          {
            id: 'mock_2',
            chinese: "每一天都是新的开始",
            pinyin: "Měi yītiān dōu shì xīn de kāishǐ",
            english: "Every day is a new beginning",
            category: "积极向上"
          },
          {
            id: 'mock_3',
            chinese: "慢慢来，比较快",
            pinyin: "Mànmàn lái, bǐjiào kuài",
            english: "Take it slow, it's actually faster",
            category: "生活智慧"
          },
          {
            id: 'mock_4',
            chinese: "愿你被这个世界温柔以待",
            pinyin: "Yuàn nǐ bèi zhège shìjiè wēnróu yǐ dài",
            english: "May you be treated gently by this world",
            category: "温暖治愈"
          }
        ];
        
        const randomIndex = Math.floor(Math.random() * mockPhrases.length);
        const selectedPhrase = mockPhrases[randomIndex];
        
        setCurrentPhrase({
          ...selectedPhrase,
          description: "心理健康正能量短语",
          isActive: true,
          usageCount: 0,
          tags: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
      
      setIsLiked(false);
      setShowTranslation(false);
    } catch (error) {
      console.error('获取随机短语失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 切换喜欢状态
  const toggleLike = () => {
    setIsLiked(!isLiked);
  };

  // 查看更多短语 - 已删除
  // const viewMorePhrases = () => {
  //   if (onNavigate) {
  //     onNavigate('help');
  //   }
  // };

  useEffect(() => {
    loadTodayPhrase();
  }, []);

  if (loading) {
    return (
      <div className="daily-phrase-container">
        <div className="daily-phrase-loading">
          <div className="phrase-skeleton"></div>
        </div>
      </div>
    );
  }

  if (!currentPhrase) {
    return (
      <div className="daily-phrase-container">
        <div className="daily-phrase-empty">
          <p>暂无每日短语</p>
          <button onClick={getRandomPhrase} className="btn-refresh">
            <RefreshCw size={16} />
            获取短语
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="daily-phrase-container">
      <div className="daily-phrase-header">
        <h3>✨ 每日短语</h3>
      </div>

      <div className="daily-phrase-card">
        <div className="phrase-category">{currentPhrase.category}</div>
        
        <div className="phrase-content">
          <h4 className="phrase-chinese">{currentPhrase.chinese}</h4>
          <p className="phrase-pinyin">{currentPhrase.pinyin}</p>
          
          {showTranslation && (
            <div className="phrase-translation">
              <p className="phrase-english">{currentPhrase.english}</p>
            </div>
          )}
        </div>
      </div>

      {/* 操作按钮区域 - 移到卡片外部 */}
      <div className="phrase-actions-external">
        <button
          onClick={() => setShowTranslation(!showTranslation)}
          className="btn-action-external btn-translate"
        >
          {showTranslation ? "隐藏翻译" : "显示翻译"}
        </button>

        <button
          onClick={toggleLike}
          className={`btn-action-external btn-like ${isLiked ? 'liked' : ''}`}
          title="喜欢"
        >
          <Heart size={16} fill={isLiked ? 'currentColor' : 'none'} />
          {isLiked ? '已喜欢' : '喜欢'}
        </button>

        <button
          onClick={getRandomPhrase}
          className="btn-action-external btn-refresh"
          title="换一句"
        >
          <RefreshCw size={16} />
          换一句
        </button>
      </div>
    </div>
  );
};

export default DailyPhrase;
