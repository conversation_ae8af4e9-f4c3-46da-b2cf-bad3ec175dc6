import type { Visitor } from '../types/visitor';
import { visitorService } from '../services/visitorService';

// 测试数据生成器
export class TestVisitorGenerator {
  private readonly surnames = [
    '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
    '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
    '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
  ];

  private readonly maleNames = [
    '伟', '强', '磊', '军', '勇', '涛', '明', '超', '辉', '华',
    '建国', '建华', '志强', '志明', '俊杰', '俊华', '文华', '文强',
    '国强', '国华', '永强', '永华', '海涛', '海军', '晓明', '晓华',
    '德华', '德强', '立军', '立华', '成龙', '成华', '金龙', '金华'
  ];

  private readonly femaleNames = [
    '丽', '娜', '敏', '静', '秀英', '秀华', '丽华', '丽娜',
    '美丽', '美华', '春花', '春丽', '红梅', '红丽', '玉华', '玉丽',
    '雪梅', '雪丽', '月华', '月丽', '晓丽', '晓华', '小红', '小丽',
    '婷婷', '娟娟', '芳芳', '燕子', '莉莉', '萍萍', '欣欣', '佳佳'
  ];

  private readonly occupations = [
    '教师', '医生', '工程师', '会计师', '律师', '设计师', '程序员', '销售员',
    '护士', '警察', '消防员', '司机', '厨师', '服务员', '保安', '清洁工',
    '学生', '退休人员', '自由职业者', '企业家', '公务员', '银行职员',
    '记者', '编辑', '翻译', '导游', '健身教练', '美容师', '理发师', '摄影师'
  ];

  private readonly educations = [
    '小学', '初中', '高中', '中专', '大专', '本科', '硕士', '博士'
  ];

  private readonly cities = [
    '北京市朝阳区', '上海市浦东新区', '广州市天河区', '深圳市南山区',
    '杭州市西湖区', '南京市鼓楼区', '武汉市洪山区', '成都市锦江区',
    '重庆市渝中区', '天津市和平区', '苏州市姑苏区', '西安市雁塔区',
    '青岛市市南区', '大连市中山区', '宁波市海曙区', '厦门市思明区'
  ];

  private readonly streets = [
    '建设路', '人民路', '中山路', '解放路', '和平路', '友谊路',
    '文化路', '学府路', '科技路', '创新路', '发展路', '繁荣路',
    '幸福路', '健康路', '阳光路', '春天路', '花园路', '绿色路'
  ];

  private readonly statuses: Array<'活跃' | '暂停' | '完成'> = ['活跃', '暂停', '完成'];

  // 生成随机手机号码
  private generatePhoneNumber(): string {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                     '150', '151', '152', '153', '155', '156', '157', '158', '159',
                     '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
    return prefix + suffix;
  }

  // 生成随机邮箱
  private generateEmail(name: string): string {
    const domains = ['qq.com', '163.com', '126.com', 'gmail.com', 'hotmail.com', 'sina.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    const randomNum = Math.floor(Math.random() * 9999);
    return `${name.toLowerCase()}${randomNum}@${domain}`;
  }

  // 生成随机地址
  private generateAddress(): string {
    const city = this.cities[Math.floor(Math.random() * this.cities.length)];
    const street = this.streets[Math.floor(Math.random() * this.streets.length)];
    const number = Math.floor(Math.random() * 999) + 1;
    const unit = Math.floor(Math.random() * 20) + 1;
    return `${city}${street}${number}号${unit}单元`;
  }

  // 生成单个测试来访者
  private generateSingleVisitor(index: number): Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'> {
    const isUsedPhones = new Set<string>();
    const surname = this.surnames[Math.floor(Math.random() * this.surnames.length)];
    const gender: '男' | '女' = Math.random() > 0.5 ? '男' : '女';
    const namePool = gender === '男' ? this.maleNames : this.femaleNames;
    const givenName = namePool[Math.floor(Math.random() * namePool.length)];
    const name = surname + givenName;
    
    // 确保手机号码唯一
    let phone: string;
    do {
      phone = this.generatePhoneNumber();
    } while (isUsedPhones.has(phone));
    isUsedPhones.add(phone);

    const age = Math.floor(Math.random() * 70) + 18; // 18-87岁
    const occupation = this.occupations[Math.floor(Math.random() * this.occupations.length)];
    const education = this.educations[Math.floor(Math.random() * this.educations.length)];
    const status = this.statuses[Math.floor(Math.random() * this.statuses.length)];
    
    // 生成紧急联系人信息
    const emergencyContact = surname + (gender === '男' ? '父亲' : '母亲');
    const emergencyPhone = this.generatePhoneNumber();
    
    return {
      name,
      gender,
      age,
      phone,
      email: Math.random() > 0.3 ? this.generateEmail(name) : undefined,
      emergencyContact,
      emergencyPhone,
      occupation: Math.random() > 0.2 ? occupation : undefined,
      education: Math.random() > 0.2 ? education : undefined,
      address: Math.random() > 0.1 ? this.generateAddress() : undefined,
      notes: Math.random() > 0.7 ? `测试来访者 #${index + 1}` : undefined,
      status
    };
  }

  // 生成指定数量的测试来访者
  public generateTestVisitors(count: number): Array<Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>> {
    const visitors: Array<Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>> = [];
    const usedPhones = new Set<string>();
    
    for (let i = 0; i < count; i++) {
      let visitor: Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>;
      let attempts = 0;
      
      // 确保手机号码不重复
      do {
        visitor = this.generateSingleVisitor(i);
        attempts++;
        if (attempts > 10) {
          // 如果尝试10次仍然重复，强制生成新的手机号
          visitor.phone = this.generatePhoneNumber() + Math.floor(Math.random() * 100);
          break;
        }
      } while (usedPhones.has(visitor.phone));
      
      usedPhones.add(visitor.phone);
      visitors.push(visitor);
    }
    
    return visitors;
  }

  // 批量创建测试来访者到数据库
  public async createTestVisitors(count: number = 100): Promise<{
    success: number;
    failed: number;
    details: {
      success: Visitor[];
      failed: Array<{ data: any; error: string }>;
    };
  }> {
    console.log(`开始生成 ${count} 个测试来访者数据...`);
    
    const testVisitors = this.generateTestVisitors(count);
    console.log(`生成完成，开始逐个创建...`);
    
    const success: Visitor[] = [];
    const failed: Array<{ data: any; error: string }> = [];
    
    for (const visitorData of testVisitors) {
      try {
        const visitor = await visitorService.createVisitor(visitorData);
        success.push(visitor);
      } catch (error) {
        failed.push({
          data: visitorData,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    console.log(`创建完成:`);
    console.log(`- 成功: ${success.length} 个`);
    console.log(`- 失败: ${failed.length} 个`);
    
    if (failed.length > 0) {
      console.log('失败详情:');
      failed.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.data.name}: ${item.error}`);
      });
    }
    
    return {
      success: success.length,
      failed: failed.length,
      details: { success, failed }
    };
  }
}

// 导出实例
export const testVisitorGenerator = new TestVisitorGenerator();

// 便捷函数
export const createTestVisitors = (count: number = 100) => {
  return testVisitorGenerator.createTestVisitors(count);
};