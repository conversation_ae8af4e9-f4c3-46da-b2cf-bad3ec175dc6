# 测试指南

本文档提供沙盘管理系统的完整测试指南，包括测试策略、测试类型、测试框架、测试用例编写和测试执行流程。

## 🧪 测试策略

### 测试金字塔
遵循测试金字塔原则，优先编写单元测试，其次是集成测试，最后是端到端测试：

```
        E2E Tests
        /       \
   Integration   \
   Tests         /
     \         /
      Unit Tests
```

### 测试覆盖率目标
- **单元测试**: ≥80%
- **集成测试**: ≥70% 
- **E2E测试**: ≥50%
- **总体覆盖率**: ≥75%

### 测试原则
1. **测试驱动开发**: 尽可能先写测试再实现功能
2. **独立测试**: 每个测试应该独立运行，不依赖其他测试
3. **确定性测试**: 测试结果应该始终一致
4. **快速反馈**: 测试应该快速执行，提供及时反馈

## 📋 测试类型

### 单元测试 (Unit Tests)
测试单个函数、方法或类的行为

#### 测试范围
- 工具函数和工具类
- 业务逻辑组件
- 数据处理函数
- 状态管理

#### 示例
```typescript
// 测试工具函数
describe('formatPhoneNumber', () => {
  it('应该格式化手机号码', () => {
    expect(formatPhoneNumber('13800138000')).toBe('138-0013-8000');
  });

  it('应该处理无效号码', () => {
    expect(formatPhoneNumber('invalid')).toBe('invalid');
  });
});
```

### 集成测试 (Integration Tests)
测试多个模块之间的交互

#### 测试范围
- 组件与服务的集成
- 数据库操作
- API调用
- 事件处理

#### 示例
```typescript
// 测试数据库集成
describe('VisitorService', () => {
  let service: VisitorService;
  let db: Database;

  beforeEach(async () => {
    db = await createTestDatabase();
    service = new VisitorService(db);
  });

  it('应该创建和查询来访者', async () => {
    const visitor = await service.createVisitor({
      name: '张三',
      phone: '13800138000'
    });

    const found = await service.getVisitor(visitor.id);
    expect(found.name).toBe('张三');
  });
});
```

### 端到端测试 (E2E Tests)
测试完整的用户流程

#### 测试范围
- 用户界面交互
- 完整的业务流
- 跨进程通信
- 桌面应用功能

#### 示例
```typescript
// 测试完整的来访者登记流程
describe('来访者登记流程', () => {
  it('应该完成来访者登记', async () => {
    // 启动应用
    await app.start();

    // 打开来访者页面
    await app.click('来访者管理');

    // 添加新来访者
    await app.fill('姓名', '李四');
    await app.fill('手机号', '13900139000');
    await app.click('保存');

    // 验证结果
    expect(await app.getText('.success-message')).toContain('保存成功');
  });
});
```

### 性能测试 (Performance Tests)
测试应用性能和响应时间

#### 测试范围
- 页面加载性能
- 数据库查询性能
- 内存使用情况
- CPU占用率

#### 示例
```typescript
// 性能测试
describe('性能测试', () => {
  it('应该在100ms内加载来访者列表', async () => {
    const startTime = Date.now();
    
    await loadVisitorsList();
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(100);
  });
});
```

### 安全测试 (Security Tests)
测试应用安全性

#### 测试范围
- 输入验证
- 认证和授权
- 数据加密
- XSS和SQL注入防护

#### 示例
```typescript
// 安全测试
describe('安全测试', () => {
  it('应该防止SQL注入', async () => {
    const maliciousInput = "'; DROP TABLE visitors; --";
    
    await expect(
      visitorService.searchVisitors(maliciousInput)
    ).rejects.toThrow('Invalid input');
  });
});
```

## 🛠️ 测试框架

### 测试运行器
使用 **Vitest** 作为主要测试运行器：

```bash
# 安装Vitest
npm install -D vitest

# 运行测试
npm test              # 运行所有测试
npm run test:unit     # 只运行单元测试
npm run test:integration # 只运行集成测试
npm run test:e2e      # 只运行E2E测试
```

### 断言库
使用 **Vitest** 内置断言：

```typescript
import { expect, test } from 'vitest';

// 基本断言
expect(value).toBe(expected);
expect(value).toEqual(expected);
expect(value).toBeTruthy();

// 异步断言
await expect(promise).resolves.toBe(expected);
await expect(promise).rejects.toThrow(error);

// 对象断言
expect(object).toHaveProperty('key', value);
expect(array).toContain(item);
```

### 测试双胞胎 (Test Doubles)

#### 使用 Vitest 的 vi 工具
```typescript
import { vi } from 'vitest';

// Mock函数
const mockFn = vi.fn();

// Mock模块
vi.mock('../services/visitorService', () => ({
  getVisitors: vi.fn().mockResolvedValue([])
}));

// Mock时间
vi.useFakeTimers();
vi.advanceTimersByTime(1000);
vi.useRealTimers();
```

### E2E 测试框架
使用 **Playwright** 进行端到端测试：

```bash
# 安装Playwright
npm install -D @playwright/test

# 安装浏览器
npx playwright install

# 运行E2E测试
npm run test:e2e
```

## 📝 测试用例编写

### 测试结构

#### 使用 Describe/It 模式
```typescript
describe('模块名称', () => {
  describe('功能描述', () => {
    it('应该完成某个行为', () => {
      // 测试代码
    });

    it('应该处理错误情况', () => {
      // 测试代码
    });
  });
});
```

### 测试生命周期

#### 生命周期钩子
```typescript
describe('测试套件', () => {
  beforeAll(() => {
    // 在所有测试之前运行
  });

  beforeEach(() => {
    // 在每个测试之前运行
  });

  afterEach(() => {
    // 在每个测试之后运行
  });

  afterAll(() => {
    // 在所有测试之后运行
  });
});
```

### 测试数据

#### 使用工厂函数创建测试数据
```typescript
// 测试数据工厂
const createMockVisitor = (overrides = {}) => ({
  id: 1,
  name: '测试用户',
  phone: '13800138000',
  createdAt: new Date(),
  ...overrides
});

// 在测试中使用
const visitor = createMockVisitor({ name: '特定用户' });
```

#### 使用夹具 (Fixtures)
```typescript
// 定义夹具
const testDatabase = async () => {
  const db = await createTestDatabase();
  await seedTestData(db);
  return db;
};

// 使用夹具
describe('数据库测试', () => {
  it('应该查询数据', async ({ database } = {}) => {
    const result = await database.query('SELECT * FROM visitors');
    expect(result.length).toBeGreaterThan(0);
  });
});
```

## 🚀 测试执行

### 本地开发测试

#### 开发模式测试
```bash
# 监听模式运行测试
npm run test:watch

# 运行特定测试文件
npm run test -- src/services/__tests__/visitorService.test.ts

# 运行特定测试套件
npm run test -- -t "来访者服务"
```

#### 调试测试
```typescript
// 在测试中调试
it('应该调试这个测试', async () => {
  debugger; // 设置断点
  // 测试代码
});
```

```bash
# 使用调试模式运行测试
npm run test:debug
```

### 持续集成测试

#### GitHub Actions 配置
```yaml
name: Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

#### 测试报告
```bash
# 生成测试报告
npm run test:coverage

# 查看覆盖率报告
open coverage/lcov-report/index.html
```

## 🧩 测试特定场景

### 测试 React 组件

#### 使用 Testing Library
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import VisitorForm from './VisitorForm';

describe('VisitorForm', () => {
  it('应该渲染表单', () => {
    render(<VisitorForm onSubmit={vi.fn()} />);
    
    expect(screen.getByLabelText('姓名')).toBeInTheDocument();
    expect(screen.getByLabelText('手机号')).toBeInTheDocument();
  });

  it('应该提交表单', async () => {
    const handleSubmit = vi.fn();
    render(<VisitorForm onSubmit={handleSubmit} />);
    
    fireEvent.change(screen.getByLabelText('姓名'), {
      target: { value: '王五' }
    });
    
    fireEvent.click(screen.getByText('保存'));
    
    expect(handleSubmit).toHaveBeenCalledWith({
      name: '王五',
      phone: ''
    });
  });
});
```

### 测试 Electron 应用

#### 测试主进程
```typescript
// 测试Electron主进程
describe('主进程', () => {
  it('应该创建浏览器窗口', () => {
    const { createMainWindow } = await import('../main/main');
    
    const window = createMainWindow();
    
    expect(window).toBeDefined();
    expect(window.loadURL).toHaveBeenCalled();
  });
});
```

#### 测试预加载脚本
```typescript
// 测试预加载脚本
describe('预加载脚本', () => {
  it('应该暴露API给渲染进程', () => {
    const { exposeApi } = await import('../preload');
    
    const mockIpcRenderer = {
      invoke: vi.fn()
    };
    
    exposeApi(mockIpcRenderer);
    
    expect(mockIpcRenderer.invoke).toHaveBeenCalledWith(
      'api:getVisitors'
    );
  });
});
```

### 测试数据库

#### 使用内存数据库
```typescript
// 测试数据库操作
describe('数据库测试', () => {
  let db: Database;

  beforeEach(async () => {
    // 使用内存数据库进行测试
    db = new sqlite3.Database(':memory:');
    await initTestDatabase(db);
  });

  afterEach(() => {
    db.close();
  });

  it('应该插入和查询数据', async () => {
    await db.run('INSERT INTO visitors (name) VALUES (?)', ['测试用户']);
    
    const result = await db.get('SELECT * FROM visitors');
    expect(result.name).toBe('测试用户');
  });
});
```

## 📊 测试覆盖率

### 覆盖率配置
```javascript
// vitest.config.js
export default {
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        '**/node_modules/**',
        '**/dist/**',
        '**/coverage/**',
        '**/*.d.ts',
        '**/__tests__/**'
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 70,
        statements: 80
      }
    }
  }
};
```

### 覆盖率报告
```bash
# 生成覆盖率报告
npm run test:coverage

# 查看HTML报告
open coverage/index.html

# 检查覆盖率阈值
npm run test:coverage:check
```

## 🔧 测试调试

### 常见问题解决

#### 测试失败调试
```bash
# 详细输出
npm run test -- --verbose

# 调试特定测试
npm run test -- -t "特定测试名称" --verbose

# 查看测试日志
DEBUG=vitest npm run test
```

#### 异步测试问题
```typescript
// 确保正确处理异步操作
it('应该等待异步操作', async () => {
  // 正确：使用await
  await expect(asyncFunction()).resolves.toBe(true);
  
  // 错误：没有处理Promise
  // expect(asyncFunction()).toBe(true);
});
```

#### 时间相关测试
```typescript
// 测试定时器
it('应该处理定时器', () => {
  vi.useFakeTimers();
  
  const callback = vi.fn();
  setTimeout(callback, 1000);
  
  // 快进时间
  vi.advanceTimersByTime(1000);
  
  expect(callback).toHaveBeenCalled();
  
  vi.useRealTimers();
});
```

## 📋 测试检查清单

### 编写测试时
- [ ] 测试名称清晰描述行为
- [ ] 测试独立不依赖其他测试
- [ ] 包含正向和反向测试用例
- [ ] 使用合适的测试双胞胎
- [ ] 清理测试数据

### 提交代码前
- [ ] 所有测试通过
- [ ] 新功能有对应测试
- [ ] 测试覆盖率达标
- [ ] 没有跳过或禁用测试

### 代码审查时
- [ ] 检查测试覆盖所有重要路径
- [ ] 验证测试断言正确
- [ ] 确认测试数据合理
- [ ] 检查异步测试正确处理

## 🆘 获取帮助

### 测试文档
- [Vitest 文档](https://vitest.dev/)
- [Testing Library](https://testing-library.com/)
- [Playwright 文档](https://playwright.dev/)

### 常见问题
- 查看 [测试问题排查指南](../docs/TESTING_TROUBLESHOOTING.md)
- 提交 [测试相关问题](<repository-url>/issues)
- 联系测试团队

---
*本文档基于现代测试最佳实践，确保代码质量和应用可靠性*