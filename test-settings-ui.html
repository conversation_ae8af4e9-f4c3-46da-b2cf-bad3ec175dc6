<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知设置功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>通知设置功能测试</h1>
    
    <div class="test-section">
        <h2>1. 检查通知权限</h2>
        <button onclick="checkNotificationPermission()">检查通知权限</button>
        <div id="permission-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试桌面通知</h2>
        <button onclick="testDesktopNotification()">发送测试通知</button>
        <div id="notification-result"></div>
    </div>

    <div class="test-section">
        <h2>3. 验证设置界面</h2>
        <p>请在应用中打开设置页面，检查以下功能：</p>
        <ul>
            <li>✅ 预约提醒启用/禁用开关</li>
            <li>✅ 提前提醒时间选择器（15分钟到1天前）</li>
            <li>✅ 桌面通知启用/禁用开关</li>
            <li>✅ 系统更新通知复选框</li>
            <li>✅ 错误警告通知复选框</li>
            <li>✅ 维护计划通知复选框</li>
        </ul>
        <p class="warning">注意：请确保设置能够保存并在刷新后保持。</p>
    </div>

    <script>
        function checkNotificationPermission() {
            if (!('Notification' in window)) {
                document.getElementById('permission-result').innerHTML = 
                    '<p class="error">浏览器不支持通知功能</p>';
                return;
            }

            const permission = Notification.permission;
            let message = '';
            
            switch (permission) {
                case 'granted':
                    message = '<p class="success">通知权限已授予 ✓</p>';
                    break;
                case 'denied':
                    message = '<p class="error">通知权限被拒绝 ✗</p>';
                    break;
                case 'default':
                    message = '<p class="warning">通知权限未设置，需要请求权限</p>';
                    break;
            }
            
            document.getElementById('permission-result').innerHTML = message;
        }

        function testDesktopNotification() {
            if (!('Notification' in window)) {
                document.getElementById('notification-result').innerHTML = 
                    '<p class="error">浏览器不支持通知功能</p>';
                return;
            }

            if (Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        sendTestNotification();
                    } else {
                        document.getElementById('notification-result').innerHTML = 
                            '<p class="error">通知权限被拒绝</p>';
                    }
                });
            } else if (Notification.permission === 'granted') {
                sendTestNotification();
            } else {
                document.getElementById('notification-result').innerHTML = 
                    '<p class="error">通知权限被拒绝，请在浏览器设置中启用</p>';
            }
        }

        function sendTestNotification() {
            const notification = new Notification('测试通知', {
                body: '这是一个测试通知，用于验证通知功能是否正常工作。',
                icon: '/logo.png',
                requireInteraction: true
            });

            notification.onclick = () => {
                console.log('通知被点击');
                notification.close();
            };

            document.getElementById('notification-result').innerHTML = 
                '<p class="success">测试通知已发送 ✓</p>';

            // 5秒后自动关闭
            setTimeout(() => {
                notification.close();
            }, 5000);
        }
    </script>
</body>
</html>