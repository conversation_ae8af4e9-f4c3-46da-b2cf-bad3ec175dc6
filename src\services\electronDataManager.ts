// Electron 环境下的 SQLite 数据管理器
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';
import type { SandTool, SandToolUsageRecord, SandToolMaintenanceRecord } from '../types/sandtool';
import type { Appointment, Room, AppointmentStatus, UrgencyLevel, AppointmentType } from '../types/schedule';
import type { AppSettings } from '../types/settings';

declare global {
  interface Window {
    electronAPI?: {
      dbQuery: (sql: string, params?: unknown[]) => Promise<unknown[]>;
      dbRun: (sql: string, params?: unknown[]) => Promise<{ changes: number; lastInsertRowid: number }>;
      getAppVersion: () => Promise<string>;
      getAppPath: (name: string) => Promise<string>;
      // 浏览器功能
      openExternal: (url: string) => Promise<{ success: boolean; error?: string }>;
      showItemInFolder: (path: string) => Promise<{ success: boolean; error?: string }>;
      // 新增：文件/数据相关
      exportData?: (data: unknown, options?: any) => Promise<{ success: boolean; canceled?: boolean; error?: string; filePath?: string }>;
      importData?: (filePath?: string) => Promise<{ success: boolean; canceled?: boolean; error?: string; data?: any; filePath?: string }>;
      performBackup?: (options?: any) => Promise<{ success: boolean; canceled?: boolean; error?: string; filePath?: string }>;
      listBackups?: () => Promise<{ success: boolean; files: Array<{ name: string; path: string; size: number; modified: string }> } | { success: false; error: string }>;
      restoreBackup?: (filePath?: string) => Promise<{ success: boolean; canceled?: boolean; error?: string; filePath?: string }>;
      cleanupData?: (options: any) => Promise<{ success: boolean; error?: string; result?: any }>;
      showNotification?: (title: string, options?: any) => Promise<any>;
      platform: string;
      isElectron: boolean;
    };
  }
}

export class ElectronDataManager {
  private isElectron: boolean;

  constructor() {
    this.isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
  }

  // 检查是否在 Electron 环境中
  isElectronEnvironment(): boolean {
    return this.isElectron;
  }

  // 执行 SQL 查询
  private async query(sql: string, params: unknown[] = []): Promise<unknown[]> {
    if (!this.isElectron) {
      throw new Error('此方法只能在 Electron 环境中使用');
    }
    if (!window.electronAPI) {
      throw new Error('Electron API不可用');
    }
    return await window.electronAPI.dbQuery(sql, params);
  }

  // 执行 SQL 命令
  private async run(sql: string, params: unknown[] = []): Promise<{ changes: number; lastInsertRowid: number }> {
    if (!this.isElectron) {
      throw new Error('此方法只能在 Electron 环境中使用');
    }
    if (!window.electronAPI) {
      throw new Error('Electron API不可用');
    }
    return await window.electronAPI.dbRun(sql, params);
  }

  // 来访者操作
  async getAllVisitors(): Promise<Visitor[]> {
    if (!this.isElectron) {
      // 浏览器环境返回模拟数据
      const { testVisitorGenerator } = await import('../utils/generateTestVisitors');
      const mockVisitors = testVisitorGenerator.generateTestVisitors(10);
      return mockVisitors.map((visitor: any, index: number) => ({
        ...visitor,
        id: `visitor-${index + 1}`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      }));
    }
    const rows = await this.query('SELECT * FROM visitors ORDER BY created_at DESC') as any[];
    return rows.map(r => this.mapVisitorFromDb(r as any));
  }

  async getVisitor(id: string): Promise<Visitor | null> {
    if (!this.isElectron) {
      const visitors = await this.getAllVisitors();
      return visitors.find(v => v.id === id) || null;
    }
    const rows = await this.query('SELECT * FROM visitors WHERE id = ?', [id]) as any[];
    return rows.length > 0 ? this.mapVisitorFromDb(rows[0] as any) : null;
  }

  async saveVisitor(visitor: Visitor): Promise<void> {
    if (!this.isElectron) {
      // 浏览器环境下只是模拟保存
      console.log('浏览器环境：模拟保存来访者数据', visitor);
      return;
    }
    const exists = await this.getVisitor(visitor.id);
    
    if (exists) {
      await this.run(`
        UPDATE visitors SET 
          name = ?, gender = ?, age = ?, phone = ?, email = ?,
          emergency_contact = ?, emergency_phone = ?, occupation = ?,
          education = ?, address = ?, notes = ?, status = ?, updated_at = ?
        WHERE id = ?
      `, [
        visitor.name, visitor.gender, visitor.age, visitor.phone, visitor.email,
        visitor.emergencyContact, visitor.emergencyPhone, visitor.occupation,
        visitor.education, visitor.address, visitor.notes, visitor.status,
        new Date().toISOString(), visitor.id
      ]);
    } else {
      await this.run(`
        INSERT INTO visitors (
          id, name, gender, age, phone, email, emergency_contact,
          emergency_phone, occupation, education, address, notes,
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        visitor.id, visitor.name, visitor.gender, visitor.age, visitor.phone,
        visitor.email, visitor.emergencyContact, visitor.emergencyPhone,
        visitor.occupation, visitor.education, visitor.address, visitor.notes,
        visitor.status, visitor.createdAt, visitor.updatedAt
      ]);
    }
  }

  async deleteVisitor(id: string): Promise<void> {
    await this.run('DELETE FROM visitors WHERE id = ?', [id]);
  }

  // 个案操作
  async getAllCases(): Promise<SimpleCase[]> {
    if (!this.isElectron) {
      // 浏览器环境返回空数组
      return [];
    }
    const rows = await this.query('SELECT * FROM cases ORDER BY created_at DESC') as any[];
    return rows.map(r => this.mapCaseFromDb(r as any));
  }

  async getCase(id: string): Promise<SimpleCase | null> {
    if (!this.isElectron) {
      // 浏览器环境返回null
      return null;
    }
    const rows = await this.query('SELECT * FROM cases WHERE id = ?', [id]) as any[];
    return rows.length > 0 ? this.mapCaseFromDb(rows[0] as any) : null;
  }

  async saveCase(caseData: SimpleCase): Promise<void> {
    if (!this.isElectron) {
      // 浏览器环境下只是模拟保存
      console.log('浏览器环境：模拟保存个案数据', caseData);
      return;
    }
    const exists = await this.getCase(caseData.id);
    
    if (exists) {
      await this.run(`
        UPDATE cases SET 
          visitor_id = ?, name = ?, summary = ?, therapy_method = ?, selected_sand_tools = ?, last_date = ?, next_date = ?,
          total = ?, star = ?, duration = ?, crisis = ?, homework = ?,
          progress = ?, keywords = ?, supervision = ?, updated_at = ?
        WHERE id = ?
      `, [
        caseData.visitorId, caseData.name, caseData.summary, caseData.therapyMethod, 
        JSON.stringify(caseData.selectedSandTools || []), caseData.lastDate, caseData.nextDate,
        caseData.total, caseData.star ? 1 : 0, caseData.duration, caseData.crisis,
        caseData.homework, caseData.progress, JSON.stringify(caseData.keywords || []),
        caseData.supervision, new Date().toISOString(), caseData.id
      ]);
    } else {
      await this.run(`
        INSERT INTO cases (
          id, visitor_id, name, summary, therapy_method, selected_sand_tools, last_date, next_date, total,
          star, duration, crisis, homework, progress, keywords,
          supervision, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        caseData.id, caseData.visitorId, caseData.name, caseData.summary, caseData.therapyMethod,
        JSON.stringify(caseData.selectedSandTools || []), caseData.lastDate,
        caseData.nextDate, caseData.total, caseData.star ? 1 : 0, caseData.duration,
        caseData.crisis, caseData.homework, caseData.progress,
        JSON.stringify(caseData.keywords || []), caseData.supervision,
        caseData.createdAt, caseData.updatedAt
      ]);
    }
  }

  async deleteCase(id: string): Promise<void> {
    await this.run('DELETE FROM cases WHERE id = ?', [id]);
  }

  // 团体会话操作
  async getAllGroupSessions(): Promise<GroupSession[]> {
    const rows = await this.query('SELECT * FROM group_sessions ORDER BY created_at DESC') as any[];
    return rows.map(r => this.mapGroupSessionFromDb(r as any));
  }

  async getGroupSession(id: string): Promise<GroupSession | null> {
    const rows = await this.query('SELECT * FROM group_sessions WHERE id = ?', [id]) as any[];
    return rows.length > 0 ? this.mapGroupSessionFromDb(rows[0] as any) : null;
  }

  async saveGroupSession(session: GroupSession): Promise<void> {
    const exists = await this.getGroupSession(session.id);
    
    if (exists) {
      await this.run(`
        UPDATE group_sessions SET 
          title = ?, description = ?, therapist_id = ?, therapist_name = ?,
          max_participants = ?, current_participants = ?, session_type = ?,
          target_age = ?, duration = ?, frequency = ?, total_sessions = ?,
          current_session = ?, start_date = ?, end_date = ?, meeting_time = ?,
          location = ?, status = ?, requirements = ?, materials = ?,
          notes = ?, updated_at = ?
        WHERE id = ?
      `, [
        session.title, session.description, session.therapistId, session.therapistName,
        session.maxParticipants, session.currentParticipants, session.sessionType,
        session.targetAge, session.duration, session.frequency, session.totalSessions,
        session.currentSession, session.startDate, session.endDate, session.meetingTime,
        session.location, session.status, session.requirements,
        JSON.stringify(session.materials || []), session.notes,
        new Date().toISOString(), session.id
      ]);
    } else {
      await this.run(`
        INSERT INTO group_sessions (
          id, title, description, therapist_id, therapist_name,
          max_participants, current_participants, session_type, target_age,
          duration, frequency, total_sessions, current_session, start_date,
          end_date, meeting_time, location, status, requirements,
          materials, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        session.id, session.title, session.description, session.therapistId,
        session.therapistName, session.maxParticipants, session.currentParticipants,
        session.sessionType, session.targetAge, session.duration, session.frequency,
        session.totalSessions, session.currentSession, session.startDate,
        session.endDate, session.meetingTime, session.location, session.status,
        session.requirements, JSON.stringify(session.materials || []),
        session.notes, session.createdAt, session.updatedAt
      ]);
    }
  }

  async deleteGroupSession(id: string): Promise<void> {
    await this.run('DELETE FROM group_sessions WHERE id = ?', [id]);
  }

  // 数据映射函数
  private mapVisitorFromDb(row: any): Visitor {
    return {
      id: row.id,
      name: row.name,
      gender: (row.gender === '男' || row.gender === '女') ? row.gender : '男',
      age: row.age,
      phone: row.phone,
      email: row.email,
      emergencyContact: row.emergency_contact,
      emergencyPhone: row.emergency_phone,
      occupation: row.occupation,
      education: row.education,
      address: row.address,
      notes: row.notes,
      status: (['活跃','暂停','完成'].includes(row.status) ? row.status : '活跃') as any,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapCaseFromDb(row: any): SimpleCase {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      name: row.name,
      summary: row.summary,
      therapyMethod: (row.therapy_method || '箱庭疗法') as any,
      selectedSandTools: row.selected_sand_tools ? JSON.parse(row.selected_sand_tools) : [],
      lastDate: row.last_date,
      nextDate: row.next_date,
      total: row.total,
      star: Boolean(row.star),
      duration: row.duration,
      crisis: row.crisis as any,
      homework: row.homework as any,
      progress: row.progress as any,
      keywords: row.keywords ? JSON.parse(row.keywords) : [],
      supervision: row.supervision,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapGroupSessionFromDb(row: any): GroupSession {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      therapistId: row.therapist_id || '',
      therapistName: row.therapist_name || '',
      maxParticipants: row.max_participants,
      currentParticipants: row.current_participants,
      participants: [],
      sessionType: (row.session_type || '开放式团体') as any,
      targetAge: (row.target_age || '混合') as any,
      duration: row.duration,
      frequency: (row.frequency || '单次') as any,
      totalSessions: row.total_sessions,
      currentSession: row.current_session,
      startDate: row.start_date,
      endDate: row.end_date,
      meetingTime: row.meeting_time,
      location: row.location,
      status: (row.status || '计划中') as any,
      requirements: row.requirements,
      materials: row.materials ? JSON.parse(row.materials) : [],
      selectedSandTools: row.selected_sand_tools ? JSON.parse(row.selected_sand_tools) : [],
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  // 获取统计信息
  async getStatistics() {
    const [visitors, cases, sessions] = await Promise.all([
      this.query('SELECT COUNT(*) as count FROM visitors') as Promise<any[]>,
      this.query('SELECT COUNT(*) as count FROM cases') as Promise<any[]>,
      this.query('SELECT COUNT(*) as count FROM group_sessions') as Promise<any[]>
    ]);

    return {
      visitors: (visitors as any[])[0]?.count || 0,
      cases: (cases as any[])[0]?.count || 0,
      groupSessions: (sessions as any[])[0]?.count || 0,
      lastBackup: null // TODO: 实现备份时间记录
    };
  }

  // 沙具管理操作
  async getAllSandTools(): Promise<SandTool[]> {
    const rows = await this.query('SELECT * FROM sand_tools ORDER BY created_at DESC') as any[];
    return (rows as any[]).map(r => this.mapSandToolFromDb(r));
  }

  async getSandTool(id: string): Promise<SandTool | null> {
    const rows = await this.query('SELECT * FROM sand_tools WHERE id = ?', [id]) as any[];
    return rows.length > 0 ? this.mapSandToolFromDb(rows[0] as any) : null;
  }

  async saveSandTool(tool: SandTool): Promise<void> {
    const exists = await this.getSandTool(tool.id);
    
    if (exists) {
      await this.run(`
        UPDATE sand_tools SET 
          name = ?, category_id = ?, description = ?, material = ?, size = ?,
          color = ?, quantity = ?, available_quantity = ?, location = ?,
          purchase_date = ?, price = ?, supplier = ?, condition = ?,
          image_url = ?, notes = ?, updated_at = ?
        WHERE id = ?
      `, [
        tool.name, tool.category, tool.description, tool.material, tool.size,
        tool.color, tool.quantity, tool.available, tool.location,
        tool.lastUsed, 0, '', tool.condition, tool.imageData, tool.notes,
        new Date().toISOString(), tool.id
      ]);
    } else {
      await this.run(`
        INSERT INTO sand_tools (
          id, name, category_id, description, material, size, color,
          quantity, available_quantity, location, purchase_date, price,
          supplier, condition, image_url, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        tool.id, tool.name, tool.category, tool.description, tool.material,
        tool.size, tool.color, tool.quantity, tool.available, tool.location,
        tool.lastUsed, 0, '', tool.condition, tool.imageData, tool.notes,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async deleteSandTool(id: string): Promise<void> {
    await this.run('DELETE FROM sand_tools WHERE id = ?', [id]);
  }

  async getAllSandToolUsageRecords(): Promise<SandToolUsageRecord[]> {
    const rows = await this.query('SELECT * FROM sand_tool_usage_records ORDER BY session_date DESC') as any[];
    return (rows as any[]).map(r => this.mapSandToolUsageRecordFromDb(r));
  }

  async saveSandToolUsageRecord(record: SandToolUsageRecord): Promise<void> {
    await this.run(`
      INSERT INTO sand_tool_usage_records (
        id, sand_tool_id, visitor_id, case_id, session_date,
        quantity_used, usage_duration, usage_notes, therapist_notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      record.id, record.toolId, record.clientName, record.sessionId,
      record.usageDate, 1, record.duration, record.notes, '',
      new Date().toISOString()
    ]);
  }

  async getAllSandToolMaintenanceRecords(): Promise<SandToolMaintenanceRecord[]> {
    const rows = await this.query('SELECT * FROM sand_tool_maintenance_records ORDER BY maintenance_date DESC') as any[];
    return (rows as any[]).map(r => this.mapSandToolMaintenanceRecordFromDb(r));
  }

  async saveSandToolMaintenanceRecord(record: SandToolMaintenanceRecord): Promise<void> {
    await this.run(`
      INSERT INTO sand_tool_maintenance_records (
        id, sand_tool_id, maintenance_type, maintenance_date,
        description, cost, performed_by, next_maintenance_date, notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      record.id, record.toolId, record.maintenanceType, record.date,
      record.description, record.cost, record.performedBy, record.nextScheduled,
      '', new Date().toISOString()
    ]);
  }

  // 日程管理操作
  async getAllAppointments(): Promise<Appointment[]> {
    const rows = await this.query('SELECT * FROM appointments ORDER BY appointment_date DESC, start_time DESC') as any[];
    return (rows as any[]).map(r => this.mapAppointmentFromDb(r));
  }

  async getAppointment(id: string): Promise<Appointment | null> {
    const rows = await this.query('SELECT * FROM appointments WHERE id = ?', [id]) as any[];
    return rows.length > 0 ? this.mapAppointmentFromDb(rows[0] as any) : null;
  }

  async saveAppointment(appointment: Appointment): Promise<void> {
    const exists = await this.getAppointment(appointment.id);
    
    if (exists) {
      await this.run(`
        UPDATE appointments SET 
          visitor_id = ?, case_id = ?, therapist_id = ?, therapist_name = ?,
          room_id = ?, title = ?, description = ?, appointment_date = ?,
          start_time = ?, end_time = ?, duration = ?, appointment_type = ?,
          status = ?, urgency_level = ?, recurring_type = ?, recurring_end_date = ?,
          reminder_time = ?, notes = ?, cancellation_reason = ?, updated_at = ?
        WHERE id = ?
      `, [
        appointment.visitorId, appointment.visitorId, appointment.therapistId, appointment.therapistName,
        appointment.room, appointment.subject, appointment.description, appointment.date,
        appointment.startTime, appointment.endTime, appointment.duration, appointment.type,
        appointment.status, appointment.urgency, null, null,
        appointment.reminderTime, appointment.notes, null, new Date().toISOString(), appointment.id
      ]);
    } else {
      await this.run(`
        INSERT INTO appointments (
          id, visitor_id, case_id, therapist_id, therapist_name, room_id,
          title, description, appointment_date, start_time, end_time, duration,
          appointment_type, status, urgency_level, recurring_type, recurring_end_date,
          reminder_time, notes, cancellation_reason, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        appointment.id, appointment.visitorId, appointment.visitorId, appointment.therapistId,
        appointment.therapistName, appointment.room, appointment.subject, appointment.description,
        appointment.date, appointment.startTime, appointment.endTime, appointment.duration,
        appointment.type, appointment.status, appointment.urgency, null, null,
        appointment.reminderTime, appointment.notes, null,
        appointment.createdAt, appointment.updatedAt
      ]);
    }
  }

  async deleteAppointment(id: string): Promise<void> {
    await this.run('DELETE FROM appointments WHERE id = ?', [id]);
  }

  async getAllRooms(): Promise<Room[]> {
    const rows = await this.query('SELECT * FROM rooms ORDER BY name') as any[];
    return (rows as any[]).map(r => this.mapRoomFromDb(r));
  }

  async saveRoom(room: Room): Promise<void> {
    const exists = await this.query('SELECT id FROM rooms WHERE id = ?', [room.id]);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE rooms SET name = ?, capacity = ?, equipment = ?, location = ?,
        description = ?, is_available = ?, updated_at = ? WHERE id = ?
      `, [
        room.name, room.capacity, JSON.stringify(room.equipment || []), room.notes,
        room.notes, room.available ? 1 : 0, new Date().toISOString(), room.id
      ]);
    } else {
      await this.run(`
        INSERT INTO rooms (id, name, capacity, equipment, location, description, is_available, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        room.id, room.name, room.capacity, JSON.stringify(room.equipment || []),
        room.notes, room.notes, room.available ? 1 : 0,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  // 设置管理操作
  async getSettings(): Promise<AppSettings | null> {
    const rows = await this.query('SELECT * FROM settings') as any[];
    if (rows.length === 0) return null;
    const settingsMap: Record<string, unknown> = {};
    (rows as any[]).forEach((row: any) => {
      try {
        const value = row.data ? JSON.parse(row.data) : row.value;
        settingsMap[row.key] = value;
      } catch (e) {
        settingsMap[row.key] = (row as any).value;
      }
    });
    if (settingsMap['app_settings']) {
      return settingsMap['app_settings'] as AppSettings;
    }
    return null;
  }

  async saveSettings(settings: AppSettings): Promise<void> {
    // 删除旧的应用设置
    await this.run('DELETE FROM settings WHERE key = ?', ['app_settings']);
    
    // 保存完整的设置对象
    await this.run(`
      INSERT OR REPLACE INTO settings (key, value, data, updated_at)
      VALUES (?, ?, ?, ?)
    `, [
      'app_settings', 
      JSON.stringify(settings), 
      JSON.stringify(settings), 
      new Date().toISOString()
    ]);
  }

  // 系统数据管理
  async saveSystemData(key: string, value: unknown): Promise<void> {
    const exists = await this.query('SELECT id FROM system_data WHERE key = ?', [key]);
    const stringValue = JSON.stringify(value);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE system_data SET value = ?, updated_at = ? WHERE key = ?
      `, [stringValue, new Date().toISOString(), key]);
    } else {
      await this.run(`
        INSERT INTO system_data (id, key, value, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `, [
        `system_${key}`, key, stringValue,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async getSystemData(key: string): Promise<any | null> {
    const rows = await this.query('SELECT value FROM system_data WHERE key = ?', [key]) as any[];
    if (rows.length === 0) return null;
    try {
      return JSON.parse((rows[0] as any).value);
    } catch (error) {
      return (rows[0] as any).value;
    }
  }

  // 用户偏好设置
  async saveUserPreference(category: string, key: string, value: unknown): Promise<void> {
    const preferenceId = `${category}_${key}`;
    const exists = await this.query('SELECT id FROM user_preferences WHERE id = ?', [preferenceId]);
    const stringValue = JSON.stringify(value);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE user_preferences SET value = ?, updated_at = ? WHERE id = ?
      `, [stringValue, new Date().toISOString(), preferenceId]);
    } else {
      await this.run(`
        INSERT INTO user_preferences (id, category, key, value, data_type, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        preferenceId, category, key, stringValue, typeof value,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async getUserPreference(category: string, key?: string): Promise<any> {
    if (key) {
      const preferenceId = `${category}_${key}`;
      const rows = await this.query('SELECT value FROM user_preferences WHERE id = ?', [preferenceId]) as any[];
      if (rows.length === 0) return null;
      try {
        return JSON.parse((rows[0] as any).value);
      } catch (error) {
        return (rows[0] as any).value;
      }
    } else {
      const rows = await this.query('SELECT key, value FROM user_preferences WHERE category = ?', [category]) as any[];
      const result: Record<string, unknown> = {};
      (rows as any[]).forEach((row: any) => {
        try { result[row.key] = JSON.parse(row.value); } catch { result[row.key] = row.value; }
      });
      return result;
    }
  }

  // 新增数据映射函数
  private mapSandToolFromDb(row: any): SandTool {
    return {
      id: row.id,
      name: row.name,
      category: (row.category_id && ['容大天成原创','人物类','动物类','植物类','建筑类','生活类','交通类','食物类','自然物质类','其它类'].includes(row.category_id) ? row.category_id : '其它类') as any,
      description: row.description,
      material: row.material || '',
      size: (row.size && ['微型','小型','中型','大型','超大型'].includes(row.size) ? row.size : '中型') as any,
      color: row.color,
      quantity: row.quantity || 0,
      available: (row.available_quantity ?? row.available ?? row.quantity ?? 0),
      condition: (row.condition && ['全新','良好','一般','损坏','报废'].includes(row.condition) ? row.condition : '良好') as any,
      location: row.location || '',
      imageData: row.image_url || row.imageData,
      notes: row.notes,
      lastUsed: row.purchase_date || row.last_used
    };
  }

  private mapSandToolUsageRecordFromDb(row: any): SandToolUsageRecord {
    return {
      id: row.id,
      toolId: row.sand_tool_id,
      toolName: '',
      sessionId: row.case_id || row.group_session_id,
      sessionType: row.group_session_id ? '团体' : '个案',
      clientName: row.visitor_id,
      therapistName: '',
      usageDate: row.session_date,
      duration: row.usage_duration,
      notes: row.usage_notes || row.notes,
      damageReport: row.damage_report,
      returnCondition: (row.return_condition && ['全新','良好','一般','损坏','报废'].includes(row.return_condition) ? row.return_condition : '良好') as any
    };
  }

  private mapSandToolMaintenanceRecordFromDb(row: any): SandToolMaintenanceRecord {
    return {
      id: row.id,
      toolId: row.sand_tool_id,
      maintenanceType: (row.maintenance_type && ['清洁','修复','更换','检查'].includes(row.maintenance_type) ? row.maintenance_type : '检查') as any,
      date: row.maintenance_date,
      description: row.description,
      cost: row.cost,
      performedBy: row.performed_by,
      nextScheduled: row.next_maintenance_date
    };
  }

  private mapAppointmentFromDb(row: { id: string; visitor_id?: string; case_id?: string; appointment_date: string; start_time: string; end_time: string; duration: number; appointment_type: string; status: string; urgency: string; room: string; notes?: string; reminder_sent: number; reminder_time?: number; title?: string; description?: string; therapist_id?: string; therapist_name?: string; room_id?: string; urgency_level?: string; created_at: string; updated_at: string }): Appointment {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      visitorName: '',
      visitorPhone: '',
      date: row.appointment_date,
      startTime: row.start_time,
      endTime: row.end_time,
      duration: row.duration,
      type: row.appointment_type as AppointmentType,
      status: row.status as AppointmentStatus,
      urgency: (row.urgency_level || row.urgency) as UrgencyLevel,
      room: row.room_id || row.room,
      therapistId: row.therapist_id || '',
      therapistName: row.therapist_name || '',
      subject: row.title || '',
      description: row.description,
      notes: row.notes,
      reminderEnabled: true,
      reminderTime: row.reminder_time || 30,
      isFirstSession: false,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: ''
    };
  }

  private mapRoomFromDb(row: { id: string; name: string; capacity: number; equipment?: string; is_available: number; description?: string; created_at: string; updated_at: string }): Room {
    return {
      id: row.id,
      name: row.name,
      capacity: row.capacity,
      type: '个体咨询室',
      equipment: row.equipment ? JSON.parse(row.equipment) : [],
      available: Boolean(row.is_available),
      notes: row.description
    };
  }

}

// 单例实例
export const electronDataManager = new ElectronDataManager();
