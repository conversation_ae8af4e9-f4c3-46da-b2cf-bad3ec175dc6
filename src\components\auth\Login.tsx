import React, { useEffect, useRef, useState } from 'react';
import DeviceCodeModal from './DeviceCodeModal';
import FileActivationForm from '../license/FileActivationForm';
import { checkActivated } from '../../utils/auth/licenseValidator';
import WindowControls from '../common/WindowControls';
import '../../styles/auth.css';

// 登录页面（一比一还原 loin/src/App.tsx 布局与交互，样式由 src/styles/auth.css 提供）
const Login: React.FC<{ onActivated?: () => void }> = ({ onActivated }) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const [showDeviceCode, setShowDeviceCode] = useState(false);
  const [showActivation, setShowActivation] = useState(false);
  const [isActivated, setIsActivated] = useState(false);
  const [isCheckingActivation, setIsCheckingActivation] = useState(true);

  useEffect(() => {
    const checkActivationStatus = async () => {
      try {
        const activated = await checkActivated();
        setIsActivated(activated);
      } catch (error) {
        console.error('检查激活状态失败:', error);
        setIsActivated(false);
      } finally {
        setIsCheckingActivation(false);
      }
    };
    checkActivationStatus();
  }, []);

  // 进入登录模式：为 body 添加类，离开时移除，确保布局与底层样式兼容
  useEffect(() => {
    document.body.classList.add('login-mode');
    return () => { document.body.classList.remove('login-mode'); };
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current!;
    const glAny = (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')) as WebGLRenderingContext | null;
    if (!glAny) return;
    const gl = glAny as WebGLRenderingContext;

    function resizeCanvas() {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      gl.viewport(0, 0, canvas.width, canvas.height);
    }
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    const vertexShaderSource = `
      attribute vec2 a_position;
      void main() { gl_Position = vec4(a_position, 0.0, 1.0); }
    `;

    const fragmentShaderSource = `
      precision mediump float;
      uniform float u_time; uniform vec2 u_resolution;
      vec3 aurora(vec2 uv, float time) { vec2 p = uv - 0.5; p.y += 0.3; float w1 = sin(p.x*3.0+time*0.5)*0.08; float w2 = sin(p.x*5.0+time*0.7+sin(time*0.3)*2.0)*0.04; float w3 = sin(p.x*7.0+time*1.1+cos(time*0.4)*1.5)*0.025; float w4 = sin(p.x*2.0+time*0.3+sin(time*0.6)*3.0)*0.06; float y=p.y-w1-w2-w3-w4; float i1=exp(-abs(y)*16.0)*0.375; float i2=exp(-abs(y+0.1)*24.0)*0.3; float i3=exp(-abs(y-0.05)*30.0)*0.225; vec3 c1=vec3(0.2,0.5,1.0)*i1; vec3 c2=vec3(1.0,1.0,1.0)*i2; vec3 c3=vec3(1.0,0.5,0.2)*i3; return c1+c2+c3; }
      vec3 secondary(vec2 uv, float time){ vec2 p=uv-0.5; p.y+=0.1; float w1=sin(p.x*2.0+time*0.3+sin(time*0.2)*2.5)*0.06; float w2=cos(p.x*4.0+time*0.5+cos(time*0.35)*1.8)*0.03; float y=p.y-w1-w2; float i=exp(-abs(y)*12.0)*0.225; return vec3(0.2,0.6,1.0)*i; }
      vec3 tertiary(vec2 uv, float time){ vec2 p=uv-0.5; p.y-=0.2; float w1=sin(p.x*1.5+time*0.4+sin(time*0.25)*3.0)*0.07; float w2=cos(p.x*3.5+time*0.6+cos(time*0.45)*2.2)*0.035; float y=p.y-w1-w2; float i=exp(-abs(y)*18.0)*0.18; return vec3(1.0,0.8,0.4)*i; }
      float noise(vec2 p){ return fract(sin(dot(p, vec2(127.1,311.7))) * 43758.5453); }
      void main(){ vec2 uv=gl_FragCoord.xy/u_resolution.xy; vec3 color=vec3(0.03,0.03,0.075); color+=aurora(uv,u_time); color+=secondary(uv,u_time+3.0); color+=tertiary(uv,u_time+1.5); vec2 su=uv*120.0; vec2 sid=floor(su); vec2 sf=fract(su); float s=noise(sid); if(s>0.985){ float b=(sin(u_time*1.5+s*8.0)*0.3+0.4)*0.75; float d=length(sf-0.5); if(d<0.03){ color+=vec3(0.8,0.9,1.0)*(1.0-d*30.0)*b; } } float glow=1.0 - length(uv - 0.5) * 0.6; color+=vec3(0.075,0.15,0.225)*glow*0.225; color+=vec3(0.05,0.15,0.3)*glow*0.225; gl_FragColor=vec4(color,1.0); }
    `;

    function createShader(gl: WebGLRenderingContext, type: number, source: string){
      const shader = gl.createShader(type)!; gl.shaderSource(shader, source); gl.compileShader(shader);
      if(!gl.getShaderParameter(shader, gl.COMPILE_STATUS)){ console.error('Shader error:', gl.getShaderInfoLog(shader)); gl.deleteShader(shader); return null; }
      return shader;
    }

    const v = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource)!;
    const f = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource)!;
    const program = gl.createProgram()!; gl.attachShader(program, v); gl.attachShader(program, f); gl.linkProgram(program);
    if(!gl.getProgramParameter(program, gl.LINK_STATUS)) console.error('Program link error:', gl.getProgramInfoLog(program));

    const aPos = gl.getAttribLocation(program, 'a_position');
    const uTime = gl.getUniformLocation(program, 'u_time');
    const uRes = gl.getUniformLocation(program, 'u_resolution');

    const positionBuffer = gl.createBuffer(); gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    const positions = new Float32Array([-1,-1, 1,-1, -1,1, -1,1, 1,-1, 1,1]);
    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

    function render(time: number){
      time *= 0.001;
      gl.clearColor(0,0,0,1); gl.clear(gl.COLOR_BUFFER_BIT); gl.useProgram(program);
      gl.enableVertexAttribArray(aPos); gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
      gl.vertexAttribPointer(aPos, 2, gl.FLOAT, false, 0, 0);
      gl.uniform1f(uTime, time); gl.uniform2f(uRes, canvas.width, canvas.height);
      gl.drawArrays(gl.TRIANGLES, 0, 6);
      requestAnimationFrame(render);
    }
    requestAnimationFrame(render);

    return () => { window.removeEventListener('resize', resizeCanvas); };
  }, []);

  return (
    <div className="relative min-h-screen w-screen">
      <canvas id="aurora-canvas" ref={canvasRef} className="fixed inset-0 w-full h-full z-0" />
      <div className="fixed inset-0 flex items-center justify-center p-4 z-10">
        {/* 自定义窗口控件 */}
        <WindowControls />
        <div className="w-full relative max-w-sm">
          <div className="relative card-border overflow-hidden rounded-2xl flex flex-col animate-float">
            <div className="p-6 pb-0 flex justify-center relative">
              <div className="w-full h-32 rounded-xl gradient-border overflow-hidden relative animate-login-pulse">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative">
                    <div className="w-16 h-16 rounded-full glass border-2 border-blue-400/30 flex items-center justify-center mb-4">
                      <img src="/login-logo.png" alt="Logo" className="w-12 h-12" onError={(e)=>{ const t=e.currentTarget; t.onerror=null; t.src='/logo.png'; }} />
                    </div>
                    <div className="absolute -top-2 -right-2"><div className="w-4 h-4 bg-orange-400 rounded-full animate-pulse"/></div>
                    <div className="absolute -bottom-1 -left-2"><div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.6s' }}/></div>
                    <div className="absolute top-1/2 -right-4"><div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" style={{ animationDelay: '1.2s' }}/></div>
                  </div>
                </div>
                <div className="absolute bottom-2 right-2">
                  <div className="flex items-center glass px-2 py-1 rounded-full border border-orange-400/20">
                    <svg className="w-3 h-3 text-orange-400 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/></svg>
                    <span className="text-xs text-green-300">安全</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">登录</h3>
                <p className="text-white/60 text-sm">访问您的专属沙盘管理系统</p>
              </div>
              <form className="space-y-4">
                <div className="relative">
                  <label className="block text-sm font-medium text-white/80 mb-2">账号</label>
                  <div className="relative">
                    <input type="email" className="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0" placeholder="请输入您的账号" onFocus={(e)=>e.currentTarget.classList.add('animate-field-glow')} onBlur={(e)=>e.currentTarget.classList.remove('animate-field-glow')} />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center input-suffix">
                      <svg className="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/></svg>
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <label className="block text-sm font-medium text-white/80 mb-2">密码</label>
                  <div className="relative">
                    <input type="password" className="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0" placeholder="请输入您的密码" onFocus={(e)=>e.currentTarget.classList.add('animate-field-glow')} onBlur={(e)=>e.currentTarget.classList.remove('animate-field-glow')} />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center input-suffix">
                      <svg className="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd"/></svg>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <label className="flex items-center">
                    <input type="checkbox" className="sr-only" />
                    <div className="w-4 h-4 border-2 border-blue-400/50 rounded glass flex items-center justify-center">
                      <svg className="w-3 h-3 text-blue-400 hidden" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/></svg>
                    </div>
                    <span className="ml-2 text-white/60">记住密码</span>
                  </label>
                  <a href="#" className="text-blue-400 hover:text-blue-300 transition">忘记密码？</a>
                </div>
                <button type="submit" className="w-full login-button text-white font-medium py-3 px-4 rounded-lg transition hover:shadow-lg hover:shadow-blue-500/25 focus:outline-none focus:ring-2 focus:ring-blue-500/50">登录</button>
                <div className="text-center my-6">
                  <span className="text-white/60 text-sm">{isCheckingActivation ? '检查激活状态...' : (isActivated ? '软件已激活' : '首次使用请激活软件')}</span>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  <button type="button" className="glass flex items-center justify-center px-3 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50" onClick={(e)=>{ e.preventDefault(); e.stopPropagation(); window.open('https://work.weixin.qq.com/kfid/kfc605d7e78dd00133d', '_blank'); }}>
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd"/></svg>
                    客服
                  </button>
                  <button type="button" className="glass flex items-center justify-center px-3 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50" onClick={(e)=>{ e.preventDefault(); e.stopPropagation(); setShowDeviceCode(true); }}>
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24"><path d="M9 7H7v2h2V7zm0 4H7v2h2v-2zm0-8c-.55 0-1 .45-1 1v1H7c-.55 0-1 .45-1 1v1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V8c0-.55-.45-1-1-1h-2V6c0-.55-.45-1-1-1h-1V4c0-.55-.45-1-1-1H9zm6 0h2v1h-2V3zm4 2h2v1h-2V5zM5 9h14v8H5V9z"/><path d="M11 11h2v2h-2v-2zm4 0h2v2h-2v-2z"/></svg>
                    设备码
                  </button>
                  <button type="button" className={`glass flex items-center justify-center px-3 py-2 border rounded-lg transition text-sm focus:outline-none focus:ring-2 ${isActivated ? 'border-green-400/30 text-green-400 hover:bg-green-500/10 focus:ring-green-500/50' : 'border-orange-400/30 text-orange-400 hover:bg-orange-500/10 focus:ring-orange-500/50'}`} onClick={(e)=>{ e.preventDefault(); e.stopPropagation(); setShowActivation(true); }} disabled={isCheckingActivation}>
                    {isActivated ? (<><svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>已激活</>) : (<><svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" /></svg>激活</>)}
                  </button>
                </div>
                <div className="text-center mt-6">
                  <span className="text-white/60 text-sm">还没有账户？ </span>
                  <a href="#" className="text-blue-400 hover:text-blue-300 transition text-sm font-medium">立即注册</a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* 设备码模态框 */}
      <DeviceCodeModal isOpen={showDeviceCode} onClose={()=>setShowDeviceCode(false)} />

      {/* 激活表单模态框 */}
      {showActivation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/60 backdrop-blur-md" onClick={() => setShowActivation(false)} />
          <div className="relative w-full max-w-lg mx-auto">
            <FileActivationForm onSuccess={()=>{ setShowActivation(false); setIsActivated(true); onActivated?.(); }} onClose={()=>setShowActivation(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;

