// 心情选择功能测试脚本
const testMoodSelection = async () => {
  console.log('=== 心情选择功能测试开始 ===');
  
  try {
    // 动态导入服务和数据
    const { moodSelectionService } = await import('./src/services/moodSelectionService.js');
    const { moodOptions, getRandomMoodResponse } = await import('./src/data/moodSelectionData.js');
    
    // 测试1: 检查心情选项数据
    console.log('测试1: 心情选项数据');
    console.log('心情选项数量:', moodOptions.length);
    moodOptions.forEach(option => {
      console.log(`- ${option.emoji} ${option.name} (${option.id})`);
    });
    
    // 测试2: 测试随机回应
    console.log('\n测试2: 随机回应测试');
    const testMoods = ['joy', 'calm', 'nervous', 'melancholy', 'tired', 'expectant'];
    testMoods.forEach(moodType => {
      const response = getRandomMoodResponse(moodType);
      console.log(`${moodType}: ${response.message}`);
    });
    
    // 测试3: 检查今日记录状态（浏览器环境）
    console.log('\n测试3: 今日记录状态');
    const hasToday = await moodSelectionService.hasTodayMoodRecord();
    console.log('今日已记录心情:', hasToday);
    
    // 测试4: 获取心情统计
    console.log('\n测试4: 心情统计');
    const stats = await moodSelectionService.getMoodStats();
    console.log('总记录数:', stats.totalRecords);
    console.log('上次心情:', stats.lastMood);
    console.log('心情分布:', stats.moodDistribution);
    
    // 测试5: 模拟记录心情（如果今天未记录）
    if (!hasToday) {
      console.log('\n测试5: 记录心情测试');
      const result = await moodSelectionService.recordMoodSelection('joy');
      console.log('记录结果:', result);
      
      // 重新检查统计
      const newStats = await moodSelectionService.getMoodStats();
      console.log('更新后统计:', newStats.totalRecords);
    }
    
    console.log('\n=== 心情选择功能测试完成 ===');
    
  } catch (error) {
    console.error('测试出错:', error);
  }
};

// 检查环境并运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testMoodSelection = testMoodSelection;
  console.log('心情选择测试脚本已加载，可以调用 testMoodSelection() 进行测试');
} else {
  // Node.js 环境
  module.exports = { testMoodSelection };
}

// 如果是直接运行脚本
if (typeof window === 'undefined' && require.main === module) {
  testMoodSelection();
}

console.log('心情选择测试脚本已加载，可以调用 testMoodSelection() 进行测试');
