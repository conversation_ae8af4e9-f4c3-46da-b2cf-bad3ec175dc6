# 每日短语功能说明

## 功能概述

每日短语功能为沙盘管理软件的首页添加了温暖的心灵短语展示，旨在为心理咨询师和来访者提供积极正面的心理支持。

## 功能特点

### 1. 智能短语推荐
- **今日短语**: 自动为每一天选择合适的短语，优先使用较少的短语
- **随机短语**: 支持手动获取随机短语
- **分类管理**: 按照积极向上、励志成长、温暖治愈等分类组织

### 2. 多语言支持
- **中文**: 精选的中文短语
- **拼音**: 提供准确的拼音标注
- **英文**: 对应的英文翻译
- **切换显示**: 支持显示/隐藏翻译

### 3. 交互功能
- **点赞**: 标记喜欢的短语
- **分享**: 支持系统分享功能或复制到剪贴板
- **刷新**: 随时获取新的短语
- **更多**: 可扩展到专门的短语管理页面

### 4. 数据持久化
- **SQLite存储**: 所有短语数据存储在本地数据库
- **使用统计**: 记录短语使用次数和最后使用时间
- **自动迁移**: 数据库结构自动升级

## 界面设计

### 布局比例
- **位置**: 首页轮播图下方
- **高度**: 固定220px，确保与轮播图协调
- **宽度**: 自适应容器宽度
- **响应式**: 支持移动端和桌面端

### 视觉风格
- **现代简洁**: 遵循Material Design原则
- **温暖配色**: 使用舒缓的蓝色调
- **清晰层次**: 明确的信息层级和视觉引导
- **微交互**: 细腻的过渡动画和悬停效果

## 技术实现

### 数据结构
```typescript
interface DailyPhrase {
  id: string;
  chinese: string;        // 中文短语
  pinyin: string;         // 拼音标注
  english: string;        // 英文翻译
  category: string;       // 分类
  description?: string;   // 描述
  source?: string;        // 来源
  tags?: string[];        // 标签
  isActive: boolean;      // 是否启用
  usageCount: number;     // 使用次数
  lastUsed?: string;      // 最后使用时间
  createdAt: string;      // 创建时间
  updatedAt: string;      // 更新时间
}
```

### 数据库表结构
```sql
CREATE TABLE daily_phrases (
  id TEXT PRIMARY KEY,
  chinese TEXT NOT NULL,
  pinyin TEXT NOT NULL,
  english TEXT NOT NULL,
  category TEXT NOT NULL,
  description TEXT,
  source TEXT,
  tags TEXT,
  is_active INTEGER DEFAULT 1,
  usage_count INTEGER DEFAULT 0,
  last_used TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

### 核心服务方法
- `getTodayPhrase()`: 获取今日推荐短语
- `getRandomPhrase()`: 获取随机短语
- `getPhrasesByCategory()`: 按分类获取短语
- `markPhraseAsUsed()`: 标记短语使用
- `getPhraseStats()`: 获取使用统计

## 默认短语内容

系统预置了10条精选短语，涵盖以下分类：

1. **积极向上** (3条)
   - 今天也要元气满满呀！
   - 每一天都是新的开始
   - 保持微笑，拥抱美好

2. **励志成长** (1条)
   - 阳光总在风雨后

3. **生活智慧** (1条)
   - 慢慢来，比较快

4. **自我接纳** (2条)
   - 做自己的太阳，无需凭借谁的光
   - 接纳不完美的自己，也是一种成长

5. **温暖治愈** (2条)
   - 愿你被这个世界温柔以待
   - 平凡的日子也有微光

6. **内心平静** (1条)
   - 平凡的日子也有微光

7. **心理健康** (1条)
   - 心中有光，何惧黑暗

## 文件结构

```
src/
├── types/
│   └── dailyPhrase.ts           # 类型定义
├── services/
│   └── dailyPhraseService.ts    # 业务逻辑
├── components/
│   └── daily-workspace/
│       ├── DailyPhrase.tsx      # 主组件
│       └── DailyPhrase.css      # 样式文件
└── components/
    ├── Dashboard.tsx            # 首页集成
    └── Dashboard.css            # 首页样式
```

## 扩展计划

### 短期优化
- [ ] 添加短语分享统计
- [ ] 支持用户自定义短语
- [ ] 短语收藏功能
- [ ] 每日短语推送通知

### 长期规划
- [ ] 专门的短语管理页面
- [ ] 短语导入/导出功能
- [ ] 多主题短语包
- [ ] AI智能短语推荐
- [ ] 情感分析匹配

## 测试说明

运行测试脚本验证功能：
```bash
node test-daily-phrases.js
```

或在浏览器控制台中：
```javascript
testDailyPhrases()
```

## 维护注意事项

1. **数据库迁移**: 修改短语表结构时要更新迁移脚本
2. **性能优化**: 大量短语时考虑分页加载
3. **内容审核**: 新增短语需要人工审核内容适宜性
4. **备份策略**: 重要短语数据需要定期备份
5. **国际化**: 未来支持多语言时需要重构数据结构

---

*该功能旨在为心理健康服务平台增添温暖元素，提升用户体验和心理支持效果。*
