const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const fs = require('fs');
const http = require('http');

let Database;
try {
  Database = require('better-sqlite3');
  console.log('better-sqlite3 导入成功');
} catch (error) {
  console.warn('better-sqlite3 导入失败:', error.message);
  Database = null;
}

// 数据库路径
const userDataPath = app.getPath('userData');
const dbPath = path.join(userDataPath, 'xlsp.db');

let mainWindow;
let db;
let dbStatus = { available: false, mode: 'none', message: '未初始化' };

// 初始化数据库
function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    console.log('用户数据路径:', userDataPath);
    console.log('数据库路径:', dbPath);

    if (!Database) {
      console.warn('better-sqlite3未可用，应用将在非数据库模式下运行');
      dbStatus = { available: false, mode: 'disabled', message: 'native 模块加载失败' };
      db = null;
      return;
    }

    // 确保用户数据目录存在
    if (!fs.existsSync(userDataPath)) {
      console.log('创建用户数据目录:', userDataPath);
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    // 连接数据库
    console.log('连接数据库...');
    try {
      db = new Database(dbPath);
      console.log('数据库连接成功');
      dbStatus = { available: true, mode: 'file', message: '已连接本地数据库' };

      // 启用外键约束
      db.exec('PRAGMA foreign_keys = ON;');

      // 创建表结构
      console.log('创建数据库表...');
      createTables();

      console.log('数据库初始化成功:', dbPath);
      verifyAndReseed();
    } catch (dbError) {
      console.error('SQLite数据库初始化失败，尝试内存数据库:', dbError.message);

      // 尝试使用内存数据库
      try {
        db = new Database(':memory:');
        console.log('内存数据库连接成功');
        dbStatus = { available: true, mode: 'memory', message: '使用内存数据库(未持久化)' };

        // 启用外键约束
        db.exec('PRAGMA foreign_keys = ON;');

        // 创建表结构
        console.log('创建内存数据库表...');
        createTables();

        console.log('内存数据库初始化成功');
      } catch (memError) {
        console.error('内存数据库也失败:', memError.message);
        dbStatus = { available: false, mode: 'error', message: '数据库初始化失败: ' + memError.message };
        throw memError;
      }
    }

  } catch (error) {
    console.error('数据库初始化失败:', error.message);
    console.warn('应用将在非数据库模式下运行');
    dbStatus = { available: false, mode: 'error', message: '初始化异常: ' + error.message };
    db = null; // 确保数据库实例为null
  }
}

// 创建数据库表
function createTables() {
  const tables = [
    // 新增: 每日短语表
    `CREATE TABLE IF NOT EXISTS daily_phrases (
      id TEXT PRIMARY KEY,
      chinese TEXT NOT NULL,
      pinyin TEXT,
      english TEXT,
      category TEXT,
      description TEXT,
      source TEXT,
      tags TEXT,
      is_active INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    // 新增: 心情回应表(若后续需要记录使用情况)
    `CREATE TABLE IF NOT EXISTS mood_responses (
      id TEXT PRIMARY KEY,
      mood_type TEXT NOT NULL,
      message TEXT NOT NULL,
      category TEXT,
      is_active INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    // 来访者表
    `CREATE TABLE IF NOT EXISTS visitors (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      gender TEXT CHECK(gender IN ('男', '女', '其他')),
      age INTEGER,
      phone TEXT UNIQUE,
      email TEXT,
      emergency_contact TEXT,
      emergency_phone TEXT,
      occupation TEXT,
      education TEXT,
      address TEXT,
      notes TEXT,
      status TEXT DEFAULT '活跃' CHECK(status IN ('活跃', '暂停', '完成')),
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 个案表
    `CREATE TABLE IF NOT EXISTS cases (
      id TEXT PRIMARY KEY,
      visitor_id TEXT,
      name TEXT NOT NULL,
      summary TEXT,
      therapy_method TEXT DEFAULT '箱庭疗法',
      selected_sand_tools TEXT,
      last_date TEXT,
      next_date TEXT,
      total INTEGER DEFAULT 0,
      star BOOLEAN DEFAULT FALSE,
      duration INTEGER DEFAULT 50,
      crisis TEXT CHECK(crisis IN ('⚠️', '⚡', '✅')),
      homework TEXT CHECK(homework IN ('📋', '✅', '❌')),
      progress TEXT CHECK(progress IN ('⬆️', '➡️', '⬇️')),
      keywords TEXT,
      supervision TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (visitor_id) REFERENCES visitors (id)
    )`,

    // 团体会话表
    `CREATE TABLE IF NOT EXISTS group_sessions (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      therapist_id TEXT,
      therapist_name TEXT,
      max_participants INTEGER DEFAULT 8,
      current_participants INTEGER DEFAULT 0,
      participants TEXT,
      session_type TEXT,
      target_age TEXT,
      duration INTEGER DEFAULT 90,
      frequency TEXT,
      total_sessions INTEGER,
      current_session INTEGER DEFAULT 0,
      start_date TEXT,
      end_date TEXT,
      meeting_time TEXT,
      location TEXT,
      status TEXT DEFAULT '计划中' CHECK(status IN ('计划中', '进行中', '已完成', '已取消')),
      requirements TEXT,
      materials TEXT,
      selected_sand_tools TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 沙具表
    `CREATE TABLE IF NOT EXISTS sand_tools (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      category_id TEXT,
      subcategory TEXT,
      description TEXT,
      material TEXT,
      size TEXT,
      color TEXT,
      quantity INTEGER DEFAULT 1,
      available_quantity INTEGER DEFAULT 1,
      condition TEXT DEFAULT '良好',
      location TEXT,
      image_url TEXT,
      notes TEXT,
      tags TEXT,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      purchase_date TEXT,
      price REAL DEFAULT 0,
      supplier TEXT,
      is_fragile BOOLEAN DEFAULT FALSE,
      needs_care BOOLEAN DEFAULT FALSE,
      replacement_needed BOOLEAN DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 设置表
    `CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY,
      key TEXT UNIQUE,
      value TEXT,
      data TEXT,
      updated_at TEXT
    )`,

    // 预约表
    `CREATE TABLE IF NOT EXISTS appointments (
      id TEXT PRIMARY KEY,
      visitor_id TEXT,
      case_id TEXT,
      therapist_id TEXT,
      therapist_name TEXT,
      room_id TEXT,
      title TEXT NOT NULL,
      description TEXT,
      appointment_date TEXT NOT NULL,
      start_time TEXT NOT NULL,
      end_time TEXT NOT NULL,
      duration INTEGER DEFAULT 50,
      appointment_type TEXT DEFAULT '个体咨询',
      status TEXT DEFAULT '已预约' CHECK(status IN ('已预约', '进行中', '已完成', '已取消', '缺席')),
      urgency_level TEXT DEFAULT '正常' CHECK(urgency_level IN ('紧急', '高', '正常', '低')),
      recurring_type TEXT,
      recurring_end_date TEXT,
      reminder_time INTEGER DEFAULT 30,
      notes TEXT,
      cancellation_reason TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (visitor_id) REFERENCES visitors (id),
      FOREIGN KEY (case_id) REFERENCES cases (id)
    )`,

    // 房间表
    `CREATE TABLE IF NOT EXISTS rooms (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      capacity INTEGER DEFAULT 1,
      equipment TEXT,
      location TEXT,
      description TEXT,
      is_available BOOLEAN DEFAULT TRUE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 沙具使用记录表
    `CREATE TABLE IF NOT EXISTS sand_tool_usage_records (
      id TEXT PRIMARY KEY,
      sand_tool_id TEXT NOT NULL,
      visitor_id TEXT,
      case_id TEXT,
      group_session_id TEXT,
      session_date TEXT NOT NULL,
      quantity_used INTEGER DEFAULT 1,
      usage_duration INTEGER,
      usage_notes TEXT,
      damage_report TEXT,
      return_condition TEXT DEFAULT '良好',
      therapist_notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sand_tool_id) REFERENCES sand_tools (id),
      FOREIGN KEY (visitor_id) REFERENCES visitors (id),
      FOREIGN KEY (case_id) REFERENCES cases (id),
      FOREIGN KEY (group_session_id) REFERENCES group_sessions (id)
    )`,

    // 沙具维护记录表
    `CREATE TABLE IF NOT EXISTS sand_tool_maintenance_records (
      id TEXT PRIMARY KEY,
      sand_tool_id TEXT NOT NULL,
      maintenance_type TEXT NOT NULL,
      maintenance_date TEXT NOT NULL,
      description TEXT,
      cost REAL DEFAULT 0,
      performed_by TEXT,
      next_maintenance_date TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sand_tool_id) REFERENCES sand_tools (id)
    )`,

    // 系统数据表
    `CREATE TABLE IF NOT EXISTS system_data (
      id TEXT PRIMARY KEY,
      key TEXT UNIQUE NOT NULL,
      value TEXT,
      data_type TEXT DEFAULT 'string',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,

    // 用户偏好设置表
    `CREATE TABLE IF NOT EXISTS user_preferences (
      id TEXT PRIMARY KEY,
      category TEXT NOT NULL,
      key TEXT NOT NULL,
      value TEXT,
      data_type TEXT DEFAULT 'string',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(category, key)
    )`
  ];

  // 创建所有表
  tables.forEach(sql => {
    try {
      db.exec(sql);
    } catch (err) {
      console.error('创建表失败:', err.message);
    }
  });

  // === 针对旧版本 settings 表缺少 key 列的迁移处理 ===
  try {
    const settingsCols = db.prepare('PRAGMA table_info(settings)').all();
    const settingsColNames = settingsCols.map(c => c.name);
    if (settingsColNames.length > 0 && !settingsColNames.includes('key')) {
      console.log('检测到旧结构 settings 表，开始迁移...');
      db.exec('ALTER TABLE settings RENAME TO settings_old');
      db.exec(`CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY,
        key TEXT UNIQUE,
        value TEXT,
        data TEXT,
        updated_at TEXT
      )`);
      const oldRows = db.prepare('SELECT * FROM settings_old').all();
      // 旧表可能只有 value 列，简单迁移为 app_settings
      if (oldRows.length > 0) {
        try {
          const first = oldRows[0];
            db.prepare(`INSERT OR REPLACE INTO settings (key, value, data, updated_at) VALUES (?, ?, ?, ?)`)
              .run('app_settings', first.value || JSON.stringify(first), first.data || first.value || JSON.stringify(first), new Date().toISOString());
        } catch (mErr) {
          console.warn('迁移旧 settings 数据失败:', mErr.message);
        }
      }
      db.exec('DROP TABLE settings_old');
      console.log('settings 表迁移完成');
    }
  } catch (mErr) {
    console.warn('检查/迁移 settings 表失败:', mErr.message);
  }

  // 数据库升级和迁移
  try {
    const columns = db.prepare("PRAGMA table_info(cases)").all();

    const hasTherapyMethod = columns.some(col => col.name === 'therapy_method');
    if (!hasTherapyMethod) {
      try {
        db.exec('ALTER TABLE cases ADD COLUMN therapy_method TEXT DEFAULT "箱庭疗法"');
        console.log('已添加therapy_method字段到cases表');
      } catch (err) {
        console.warn('添加therapy_method字段失败:', err.message);
      }
    }

    const hasSelectedSandTools = columns.some(col => col.name === 'selected_sand_tools');
    if (!hasSelectedSandTools) {
      try {
        db.exec('ALTER TABLE cases ADD COLUMN selected_sand_tools TEXT');
        console.log('已添加selected_sand_tools字段到cases表');
      } catch (err) {
        console.warn('添加selected_sand_tools字段失败:', err.message);
      }
    }
  } catch (err) {
    console.warn('检查表结构失败:', err.message);
  }
}

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.png'),
    frame: false, // 使用无边框窗口以支持自定义标题栏
    show: false
  });

  // 设置安全的Content Security Policy
  if (!isDev) {
    mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'; object-src 'none'; media-src 'self'; frame-src 'none'; child-src 'none'; form-action 'self'; base-uri 'self';"
          ]
        }
      });
    });
  }

  const loadDist = () => {
    const distIndex = path.join(__dirname, '../dist/index.html');
    if (fs.existsSync(distIndex)) {
      console.log('[Loader] 载入本地构建 dist/index.html');
      mainWindow.loadFile(distIndex);
    } else {
      console.error('[Loader] 未找到 dist/index.html，请先运行: npm run build');
      const html = `<html><body style="font-family:sans-serif;padding:40px;">`+
        `<h2>无法加载开发服务器</h2>`+
        `<p>未检测到 Vite Dev Server 且 dist/index.html 不存在。</p>`+
        `<p>请执行 <code>npm run dev</code> (自动启动) 或先构建: <code>npm run build</code></p>`+
        `<hr/><small>main.cjs fallback loader</small></body></html>`;
      mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(html));
    }
  };

  const tryLoadDev = (port) => {
    return new Promise((resolve) => {
      const url = `http://localhost:${port}`;
      const req = http.get(url, res => {
        if (res.statusCode && res.statusCode < 500) {
          console.log(`[Loader] 检测到开发服务器: ${url}`);
          resolve(true);
        } else {
          resolve(false);
        }
        res.resume();
      });
      req.on('error', () => resolve(false));
      req.setTimeout(1500, () => { req.destroy(); resolve(false); });
    });
  };

  async function loadContent() {
    if (isDev) {
      // 支持多端口尝试，优先 5188（与脚本一致），其次 5174/5173/5175
      const portList = (process.env.DEV_PORT || '5188,5174,5173,5175')
        .split(',')
        .map(p => String(p).trim())
        .filter(Boolean);
      let foundPort = null;
      for (const p of portList) {
        const ok = await tryLoadDev(p);
        if (ok) { foundPort = p; break; }
      }
      if (foundPort) {
        mainWindow.loadURL(`http://localhost:${foundPort}`);
        mainWindow.webContents.once('did-fail-load', () => {
          console.warn('[Loader] dev server 加载失败，回退 dist');
          loadDist();
        });
      } else {
        console.warn('[Loader] 未检测到开发服务器，回退本地 dist');
        loadDist();
      }
    } else {
      loadDist();
    }
  }

  loadContent();

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 设置菜单
  setApplicationMenu();
}

// 设置应用菜单
function setApplicationMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入数据',
          accelerator: 'CmdOrCtrl+I',
          click: () => importData()
        },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: () => exportData()
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '工具',
      submenu: [
        {
          label: '在桌面创建 激活文件生成器(HTML)',
          click: () => {
            try {
              const desktop = app.getPath('desktop');
              const src = path.join(__dirname, '../tools/license-file-signer.html');
              const dst = path.join(desktop, '激活文件生成器.html');
              if (fs.existsSync(src)) {
                fs.copyFileSync(src, dst);
                shell.openPath(dst);
                dialog.showMessageBox(mainWindow, { type: 'info', message: '已在桌面创建: 激活文件生成器.html\n\n⚠️ 重要提醒：\n1. 私钥已预设为您指定的种子生成\n2. 请在离线环境中使用此工具\n3. 生成的.lic文件发送给客户上传激活' });
              } else {
                dialog.showErrorBox('文件缺失', '未找到 tools/license-file-signer.html，请联系开发者或重新构建');
              }
            } catch (e) {
              dialog.showErrorBox('创建失败', e.message);
            }
          }
        },
        {
          label: '在桌面创建 发行说明(README)',
          click: () => {
            try {
              const desktop = app.getPath('desktop');
              const src = path.join(__dirname, '../tools/README.md');
              const dst = path.join(desktop, '激活工具使用说明.md');
              if (fs.existsSync(src)) {
                fs.copyFileSync(src, dst);
                shell.openPath(dst);
                dialog.showMessageBox(mainWindow, { type: 'info', message: '已在桌面创建: 激活工具使用说明.md' });
              } else {
                dialog.showErrorBox('文件缺失', '未找到 tools/README.md，请联系开发者或重新构建');
              }
            } catch (e) {
              dialog.showErrorBox('创建失败', e.message);
            }
          }
        }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于沙盘管理系统',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '沙盘管理系统',
              detail: '专业的心理健康沙盘疗法管理系统\\n版本: 1.0.0\\n\\n© 2024 版权所有'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 导入数据
async function importData() {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: '选择数据文件',
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];
      const data = fs.readFileSync(filePath, 'utf8');

      // 发送数据到渲染进程进行处理
      mainWindow.webContents.send('import-data', data);

      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '导入成功',
        message: '数据文件已加载，请在应用中确认导入'
      });
    }
  } catch (error) {
    dialog.showErrorBox('导入失败', error.message);
  }
}

// 导出数据
async function exportData() {
  try {
    if (!db) {
      dialog.showMessageBox(mainWindow, {
        type: 'warning',
        title: '导出失败',
        message: '数据库未初始化，无法导出数据'
      });
      return;
    }

    const result = await dialog.showSaveDialog(mainWindow, {
      title: '保存数据文件',
      defaultPath: `xlsp-backup-${new Date().toISOString().split('T')[0]}.json`,
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled) {
      try {
        // 使用同步查询获取所有表的数据
        const visitors = db.prepare('SELECT * FROM visitors').all();
        const cases = db.prepare('SELECT * FROM cases').all();
        const groupSessions = db.prepare('SELECT * FROM group_sessions').all();
        const sandTools = db.prepare('SELECT * FROM sand_tools').all();

        const data = {
          version: '1.0.0',
          timestamp: new Date().toISOString(),
          visitors,
          cases,
          groupSessions,
          sandTools
        };

        fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));

        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '导出成功',
          message: '数据导出完成'
        });
      } catch (error) {
        dialog.showErrorBox('导出失败', error.message);
      }
    }
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
  }
}

// IPC 处理程序
ipcMain.handle('db-query', async (event, sql, params = []) => {
  try {
    if (!db) {
      console.warn('数据库未初始化，返回空结果');
      return [];
    }

    const stmt = db.prepare(sql);
    const rows = stmt.all(params);
    return rows || [];
  } catch (error) {
    console.error('Database query error:', error);
    return []; // 返回空数组而不是抛出错误
  }
});

ipcMain.handle('db-run', async (event, sql, params = []) => {
  try {
    if (!db) {
      console.warn('数据库未初始化，返回默认结果');
      return { changes: 0, lastInsertRowid: null };
    }

    const stmt = db.prepare(sql);
    const result = stmt.run(params);
    return {
      changes: result.changes || 0,
      lastInsertRowid: result.lastInsertRowid || null
    };
  } catch (error) {
    console.error('Database run error:', error);
    return { changes: 0, lastInsertRowid: null }; // 返回默认结果
  }
});

ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', (event, name) => {
  return app.getPath(name);
});

// 数据库状态查询
ipcMain.handle('db-status', async () => {
  return dbStatus;
});

// 处理来自渲染进程的导出数据
ipcMain.handle('save-export-data', (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '导出成功',
      message: '数据导出完成'
    });
    return { success: true };
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
    return { success: false, error: error.message };
  }
});

// 处理数据导出请求
ipcMain.handle('export-data', async (event, data, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: '保存数据文件',
      defaultPath: `xlsp-export-${new Date().toISOString().split('T')[0]}.json`,
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled) {
      fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));

      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '导出成功',
        message: '数据导出完成'
      });

      return { success: true, filePath: result.filePath };
    }

    return { success: false, canceled: true };
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
    return { success: false, error: error.message };
  }
});

// 处理数据导入请求
ipcMain.handle('import-data', async (event) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: '选择数据文件',
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const selectedFilePath = result.filePaths[0];
      const data = fs.readFileSync(selectedFilePath, 'utf8');

      return { success: true, data: JSON.parse(data), filePath: selectedFilePath };
    }

    return { success: false, canceled: true };
  } catch (error) {
    dialog.showErrorBox('导入失败', error.message);
    return { success: false, error: error.message };
  }
});

// 生成每日短语种子(独立于前端TS, 避免打包后缺失)
function generateSeedPhrases() {
  const start = [
    { zh: '保持', py: 'Bǎochí', en: 'Keep' },
    { zh: '带着', py: 'Dàizhe', en: 'With' },
    { zh: '允许', py: 'Yǔnxǔ', en: 'Allow' },
    { zh: '相信', py: 'Xiāngxìn', en: 'Believe' },
    { zh: '拥抱', py: 'Yōngbào', en: 'Embrace' },
    { zh: '慢慢体会', py: 'Mànmàn tǐhuì', en: 'Gently feel' },
    { zh: '静静守护', py: 'Jìngjìng shǒuhù', en: 'Quietly guard' },
    { zh: '温柔看见', py: 'Wēnróu kànjiàn', en: 'Gently notice' }
  ];
  const middle = [
    { zh: '内心正在苏醒的微光', py: 'nèixīn zhèngzài sūxǐng de wēiguāng', en: 'the awakening inner glimmer' },
    { zh: '一点点生长的勇气', py: 'yī diǎndiǎn shēngzhǎng de yǒngqì', en: 'the slowly growing courage' },
    { zh: '尚未被说出口的温暖', py: 'shàngwèi bèi shuō chūkǒu de wēnnuǎn', en: 'the unspoken warmth' },
    { zh: '正在自我修复的情绪', py: 'zhèngzài zìwǒ xiūfù de qíngxù', en: 'the emotions healing themselves' },
    { zh: '平凡日子里的细小发现', py: 'píngfán rìzi lǐ de xìxiǎo fāxiàn', en: 'the tiny finds in ordinary days' },
    { zh: '你尚未察觉的力量', py: 'nǐ shàngwèi chájué de lìliàng', en: 'the strength not yet noticed' },
    { zh: '正在被接纳的那份不完美', py: 'zhèngzài bèi jiēnà de nà fèn bù wánměi', en: 'the imperfection being accepted' },
    { zh: '缓缓沉淀下来的安定', py: 'huǎnhuǎn chéndiàn xiàlái de āndìng', en: 'the stability slowly settling' },
    { zh: '在呼吸里循环的节奏', py: 'zài hūxī lǐ xúnhuán de jiézòu', en: 'the rhythm cycling in breaths' }
  ];
  const end = [
    { zh: '它正在悄悄支持你前行。', py: 'tā zhèngzài qiāoqiāo zhīchí nǐ qiánxíng.', en: 'it is quietly supporting you forward.' },
    { zh: '让今天多了一点亮度。', py: 'ràng jīntiān duōle yīdiǎn liàngdù.', en: 'making today a bit brighter.' },
    { zh: '会在需要的时候化成答案。', py: 'huì zài xūyào de shíhòu huà chéng dáàn.', en: 'will become an answer when needed.' },
    { zh: '它无需喧哗也足够有力。', py: 'tā wúxū xuānhuá yě zúgòu yǒulì.', en: 'it needs no noise yet is strong.' },
    { zh: '请耐心与它并肩。', py: 'qǐng nàixīn yǔ tā bìngjiān.', en: 'be patient and walk beside it.' },
    { zh: '它值得被温柔收藏。', py: 'tā zhídé bèi wēnróu shōucáng.', en: 'it deserves gentle keeping.' },
    { zh: '终会汇聚成清晰方向。', py: 'zhōng huì huìjù chéng qīngxī fāngxiàng.', en: 'it will gather into a clear direction.' },
    { zh: '会在平静里继续生根。', py: 'huì zài píngjìng lǐ jìxù shēnggēn.', en: 'it will keep rooting in calm.' },
    { zh: '请给自己一个肯定。', py: 'qǐng gěi zìjǐ yī gè kěndìng.', en: 'please give yourself affirmation.' },
    { zh: '这是成长可靠的证据。', py: 'zhè shì chéngzhǎng kěkào de zhèngjù.', en: 'this is reliable proof of growth.' },
    { zh: '它让明天更值得期待。', py: 'tā ràng míngtiān gèng zhídé qídài.', en: 'it makes tomorrow more expectable.' },
    { zh: '静观其变就是一种力量。', py: 'jìng guān qí biàn jiùshì yī zhǒng lìliàng.', en: 'observing calmly is a strength.' }
  ];
  const categories = ['积极向上','励志成长','温暖治愈','内心平静','生活智慧','心理健康','人际关系','自我接纳'];
  const base = [];
  let idx = 0;
  for (const s of start) {
    for (const m of middle) {
      for (const e of end) {
        base.push({
          chinese: s.zh + m.zh + '，' + e.zh,
          pinyin: s.py + ' ' + m.py + ' ' + e.py,
          english: s.en + ' ' + m.en + ', ' + e.en,
          category: categories[idx % categories.length],
          description: '自动生成短语',
          tags: ''
        });
        idx++;
      }
    }
  }
  // 864 基础, 生成变体补足
  const variantSuffixes = [
    { zh: '今天为自己记录', py: 'jīntiān wèi zìjǐ jìlù', en: 'record it today' },
    { zh: '值得在心里留白', py: 'zhídé zài xīnlǐ liúbái', en: 'leave a quiet blank' },
    { zh: '静候花开那刻', py: 'jìng hòu huākāi nà kè', en: 'await the bloom' },
    { zh: '让善待成为习惯', py: 'ràng shàndài chéngwéi xíguàn', en: 'let kindness be habit' }
  ];
  const variants = [];
  let v = 0;
  for (let i = 0; i < base.length && variants.length < 300; i += 2) {
    const b = base[i];
    const suf = variantSuffixes[v % variantSuffixes.length];
    v++;
    variants.push({
      chinese: b.chinese.replace(/。$/, '，' + suf.zh + '。'),
      pinyin: b.pinyin + ' ' + suf.py,
      english: b.english.replace(/\.$/, ', ' + suf.en + '.'),
      category: categories[(i+3)%categories.length],
      description: '自动生成短语(变体)',
      tags: ''
    });
  }
  let all = base.concat(variants);
  if (all.length < 1095) {
    const need = 1095 - all.length;
    for (let i=0;i<need;i++) {
      const src = all[i];
      all.push({ ...src, chinese: src.chinese.replace(/。$/, '【扩展】。'), description: '自动生成短语(扩展)' });
    }
  }
  return all.slice(0,1095);
}

// 生成心情回应种子(每类60)
function generateMoodResponseSeeds() {
  const moods = {
    joy: { base: [
      { p: '今天的喜悦像', c: '晨光', s: '一样清新, 让它在沙盘里延伸。' },
      { p: '你带来的光像', c: '春芽', s: '一般安静却充满力量。' },
      { p: '这份开心如', c: '微风', s: '掠过心湖, 留下圈圈涟漪。' }
    ], cat: '积极回应' },
    calm: { base: [
      { p: '此刻的平静如', c: '缓慢潮汐', s: '，来回抚平紧绷。' },
      { p: '你的安宁像', c: '一池澄澈的水', s: '，悄悄养护着力量。' },
      { p: '沉着像', c: '午后柔光', s: '，不耀眼却持续在场。' }
    ], cat: '温和鼓励' },
    nervous: { base: [
      { p: '这份紧张像', c: '未调匀的弦', s: '，调整一下会更动听。' },
      { p: '你感到的波动如', c: '浪前的起伏', s: '，是力量蓄积的信号。' },
      { p: '心跳的快节奏像', c: '赶路的轻步', s: '，慢下来会更稳。' }
    ], cat: '安抚支持' },
    melancholy: { base: [
      { p: '此刻的情绪像', c: '薄雾', s: '，会在温度里慢慢散开。' },
      { p: '淡淡忧伤如', c: '低空缓行的云', s: '，只是暂时停驻。' },
      { p: '你的小低落像', c: '暮色未收的余灰', s: '，无需赶走。' }
    ], cat: '温柔陪伴' },
    tired: { base: [
      { p: '这份疲倦像', c: '旅途中磨损的鞋底', s: '，需要被好好护理。' },
      { p: '你的乏力如', c: '熄火后尚温的炉壁', s: '，仍保留核心余热。' },
      { p: '倦意像', c: '长途列车的轻晃', s: '，提示该稍作停靠。' }
    ], cat: '关怀体贴' },
    expectant: { base: [
      { p: '期待像', c: '晨空第一颗褪不掉的星', s: '，指向未写的篇章。' },
      { p: '心中的盼望似', c: '种子埋在温润土里', s: '，静默积累。' },
      { p: '你的好奇像', c: '刚展开的纸卷', s: '，边缘还带着卷曲。' }
    ], cat: '希望激励' }
  };
  const extraA = ['静静流动','轻柔存在','悄悄积累','缓缓展开','默默支撑','持续发亮'];
  const extraB = ['请给它空间','值得被记录','让它自然生长','无需过度控制','它正在成形','相信自己的节奏'];
  const result = [];
  Object.keys(moods).forEach(key => {
    const { base, cat } = moods[key];
    let produced = 0; let id = 1;
    outer: for (const f of base) {
      for (const a of extraA) {
        for (const b of extraB) {
          result.push({ id: `${key}_${id++}`, mood_type: key, message: `${f.p}${f.c}${f.s}${a}，${b}。`, category: cat });
          produced++;
          if (produced >= 60) break outer;
        }
      }
    }
  });
  return result; // 6*60=360
}

function verifyAndReseed() {
  if (!db) return;
  try {
    // 每日短语
    const phraseCount = db.prepare('SELECT COUNT(*) AS c FROM daily_phrases').get().c;
    if (phraseCount < 1095) {
      console.log(`每日短语数量不足(${phraseCount})，开始自动恢复...`);
      db.exec('DELETE FROM daily_phrases');
      const phrases = generateSeedPhrases();
      const insert = db.prepare(`INSERT INTO daily_phrases (id,chinese,pinyin,english,category,description,source,tags,is_active,usage_count,last_used,created_at,updated_at) VALUES (?,?,?,?,?,?,?,?,1,0,NULL,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)`);
      const insertMany = db.transaction(list => { list.forEach((p,i) => insert.run('phrase_'+String(i+1).padStart(4,'0'), p.chinese, p.pinyin, p.english, p.category, p.description, null, p.tags)); });
      insertMany(phrases);
      console.log('每日短语自动恢复完成，总数:', phrases.length);
    } else {
      console.log('每日短语数量充足:', phraseCount);
    }
    // 心情回应
    const moodCount = db.prepare('SELECT COUNT(*) AS c FROM mood_responses').get().c;
    if (moodCount < 300) {
      console.log(`心情回应数量不足(${moodCount})，开始自动恢复...`);
      db.exec('DELETE FROM mood_responses');
      const responses = generateMoodResponseSeeds();
      const stmt = db.prepare(`INSERT INTO mood_responses (id,mood_type,message,category,is_active,usage_count,last_used,created_at,updated_at) VALUES (?,?,?,?,1,0,NULL,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)`);
      const batch = db.transaction(list => { list.forEach(r => stmt.run(r.id, r.mood_type, r.message, r.category)); });
      batch(responses);
      console.log('心情回应自动恢复完成，总数:', responses.length);
    } else {
      console.log('心情回应数量充足:', moodCount);
    }
  } catch (err) {
    console.error('自恢复过程出错:', err.message);
  }
}

// 创建备份目录
const BACKUP_DIR = path.join(userDataPath, 'backups');

function ensureBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
  }
}

function createBackupFilename(prefix = 'backup') {
  const ts = new Date().toISOString().replace(/[:T]/g, '-').split('.')[0];
  return `${prefix}-${ts}.json`;
}

function collectAllData() {
  if (!db) throw new Error('数据库未初始化');
  const tables = [
    'visitors','cases','group_sessions','sand_tools','appointments','rooms',
    'sand_tool_usage_records','sand_tool_maintenance_records','settings','system_data','user_preferences','daily_phrases','mood_responses'
  ];
  const data = {};
  tables.forEach(t => {
    try { data[t] = db.prepare(`SELECT * FROM ${t}`).all(); } catch(e){ data[t] = []; }
  });
  return {
    version: '1.0.0',
    exportedAt: new Date().toISOString(),
    tables: data
  };
}

function restoreDataFromObject(obj, merge = true) {
  if (!db) throw new Error('数据库未初始化');
  if (!obj || !obj.tables) throw new Error('备份文件结构无效');
  const tables = Object.keys(obj.tables);
  const trx = db.transaction(() => {
    tables.forEach(t => {
      const rows = Array.isArray(obj.tables[t]) ? obj.tables[t] : [];
      if (!merge) {
        try { db.exec(`DELETE FROM ${t}`); } catch(e) { /* ignore */ }
      }
      if (rows.length === 0) return;
      // 动态生成插入语句
      rows.forEach(r => {
        const cols = Object.keys(r);
        const placeholders = cols.map(()=>'?').join(',');
        const sql = `INSERT OR REPLACE INTO ${t} (${cols.join(',')}) VALUES (${placeholders})`;
        try { db.prepare(sql).run(cols.map(c=> r[c])); } catch(e) { /* 忽略单行错误 */ }
      });
    });
  });
  trx();
}

function cleanupDataFiles({ tempFiles, logs, cache, oldBackups, backupRetentionDays = 30 }) {
  const results = { removed: [] };
  try {
    if (oldBackups) {
      ensureBackupDir();
      const files = fs.readdirSync(BACKUP_DIR).filter(f => f.endsWith('.json'));
      const threshold = Date.now() - backupRetentionDays * 24 * 60 * 60 * 1000;
      files.forEach(f => {
        const fp = path.join(BACKUP_DIR, f);
        const stat = fs.statSync(fp);
        if (stat.mtime.getTime() < threshold) {
          fs.unlinkSync(fp);
          results.removed.push(fp);
        }
      });
    }
    // 其他类型(tempFiles/logs/cache)实际项目可在此扩展路径清理逻辑
  } catch(e) {
    results.error = e.message;
  }
  return results;
}

// IPC —— 备份
ipcMain.handle('perform-backup', async (event, options = {}) => {
  try {
    ensureBackupDir();
    const payload = collectAllData();
    const fileName = createBackupFilename('xlsp-backup');
    const filePath = path.join(BACKUP_DIR, fileName);
    fs.writeFileSync(filePath, JSON.stringify(payload, null, 2));
    return { success: true, filePath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC —— 列出备份
ipcMain.handle('list-backups', async () => {
  try {
    ensureBackupDir();
    const files = fs.readdirSync(BACKUP_DIR)
      .filter(f => f.endsWith('.json'))
      .map(f => {
        const fp = path.join(BACKUP_DIR, f);
        const stat = fs.statSync(fp);
        return { name: f, path: fp, size: stat.size, modified: stat.mtime };
      })
      .sort((a,b)=> b.modified - a.modified);
    return { success: true, files };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC —— 恢复备份
ipcMain.handle('restore-backup', async (event, filePath) => {
  try {
    if (!filePath) {
      const result = await dialog.showOpenDialog(mainWindow, {
        title: '选择备份文件',
        filters: [{ name: 'JSON 文件', extensions: ['json'] }],
        properties: ['openFile']
      });
      if (result.canceled || result.filePaths.length === 0) {
        return { success: false, canceled: true };
      }
      filePath = result.filePaths[0];
    }
    const raw = fs.readFileSync(filePath, 'utf8');
    const obj = JSON.parse(raw);
    restoreDataFromObject(obj, true); // 合并导入
    return { success: true, filePath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC —— 数据清理
ipcMain.handle('cleanup-data', async (event, options = {}) => {
  try {
    const result = cleanupDataFiles(options || {});
    return { success: true, result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 单实例应用
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// 窗口控制事件（自定义标题栏按钮调用）
ipcMain.on('window-control', (event, action) => {
  if (!mainWindow) return;
  switch (action) {
    case 'minimize':
      mainWindow.minimize();
      break;
    case 'maximize':
      if (mainWindow.isMaximized()) mainWindow.unmaximize();
      else mainWindow.maximize();
      break;
    case 'close':
      mainWindow.close();
      break;
    default:
      break;
  }
});

// 应用事件
app.whenReady().then(() => {
  initDatabase();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (db) {
      try {
        db.close();
        console.log('数据库已关闭');
      } catch (err) {
        console.error('关闭数据库时出错:', err.message);
      }
    }
    app.quit();
  }
});

app.on('before-quit', () => {
  if (db) {
    try {
      db.close();
      console.log('数据库已关闭');
    } catch (err) {
      console.error('关闭数据库时出错:', err.message);
    }
  }
});
