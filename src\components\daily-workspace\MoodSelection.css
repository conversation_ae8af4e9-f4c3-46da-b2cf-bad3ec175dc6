/* 心情选择组件样式 */
.mood-selection-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.mood-selection-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0;
}

.mood-selection-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.last-mood-info {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

.mood-selection-card {
  flex: 1;
  padding: 16px 20px 20px;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.mood-prompt {
  text-align: center;
  margin-bottom: 20px;
}

.prompt-text {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

.mood-options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  flex: 1;
}

.mood-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
}

.mood-option:hover {
  border-color: var(--mood-color);
  background: var(--mood-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mood-option.selected {
  border-color: var(--mood-color);
  background: var(--mood-bg);
  transform: scale(1.05);
}

.mood-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mood-option.disabled:hover {
  transform: none;
  box-shadow: none;
}

.mood-emoji {
  font-size: 24px;
  transition: transform 0.3s ease;
}

.mood-option:hover .mood-emoji {
  transform: scale(1.1);
}

.mood-name {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

.today-completed {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  color: #15803d;
  font-size: 13px;
  margin-top: auto;
}

/* 心情回应区域 */
.mood-response {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}

.response-mood {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.response-emoji {
  font-size: 36px;
  animation: mood-selected 0.6s ease-in-out;
}

.response-mood-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.response-message {
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
}

.response-message p {
  margin: 0;
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
}

.response-actions {
  display: flex;
  justify-content: center;
}

.btn-reset {
  background: white;
  border: 1px solid #e5e7eb;
  color: #374151;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.btn-reset:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.btn-reset:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 心情花园预览 */
.mood-garden-preview {
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 12px 20px;
  flex-shrink: 0;
}

.garden-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.garden-count {
  color: #6b7280;
  font-size: 12px;
}

.garden-emotions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.garden-emotion {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.garden-emoji {
  font-size: 16px;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.garden-emotion:hover .garden-emoji {
  opacity: 1;
  transform: scale(1.1);
}

.garden-count-dot {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: bold;
  min-width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 心情动画效果 */
@keyframes mood-selected {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.mood-animation-joy {
  animation: joy-animation 2s ease-in-out;
}

.mood-animation-calm {
  animation: calm-animation 2s ease-in-out;
}

.mood-animation-nervous {
  animation: nervous-animation 2s ease-in-out;
}

.mood-animation-melancholy {
  animation: melancholy-animation 2s ease-in-out;
}

.mood-animation-tired {
  animation: tired-animation 2s ease-in-out;
}

.mood-animation-expectant {
  animation: expectant-animation 2s ease-in-out;
}

@keyframes joy-animation {
  0%, 100% { transform: scale(1.05); }
  25% { transform: scale(1.1) rotate(5deg); }
  50% { transform: scale(1.05) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(3deg); }
}

@keyframes calm-animation {
  0%, 100% { transform: scale(1.05); }
  50% { transform: scale(1.08); box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
}

@keyframes nervous-animation {
  0%, 100% { transform: scale(1.05); }
  10% { transform: scale(1.02) translateX(-2px); }
  20% { transform: scale(1.08) translateX(2px); }
  30% { transform: scale(1.02) translateX(-1px); }
  40% { transform: scale(1.06) translateX(1px); }
  50% { transform: scale(1.05); }
}

@keyframes melancholy-animation {
  0%, 100% { transform: scale(1.05); }
  50% { transform: scale(1.03); opacity: 0.8; }
}

@keyframes tired-animation {
  0%, 100% { transform: scale(1.05); }
  25% { transform: scale(1.02); opacity: 0.7; }
  75% { transform: scale(1.03); opacity: 0.9; }
}

@keyframes expectant-animation {
  0%, 100% { transform: scale(1.05); }
  25% { transform: scale(1.1); box-shadow: 0 0 15px rgba(139, 92, 246, 0.4); }
  50% { transform: scale(1.05); }
  75% { transform: scale(1.08); box-shadow: 0 0 10px rgba(139, 92, 246, 0.2); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mood-selection-container {
    margin: 0 -4px;
  }
  
  .mood-selection-header {
    padding: 14px 16px 10px;
  }
  
  .mood-selection-card {
    padding: 14px 16px 16px;
    min-height: 180px;
  }
  
  .mood-options-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
  
  .mood-option {
    padding: 10px 6px;
  }
  
  .mood-emoji {
    font-size: 20px;
  }
  
  .mood-name {
    font-size: 11px;
  }
  
  .response-emoji {
    font-size: 32px;
  }
  
  .response-mood-name {
    font-size: 16px;
  }
  
  .mood-garden-preview {
    padding: 10px 16px;
  }
}

/* 额外的心情选择状态 */
.mood-option:active {
  transform: scale(0.95);
}

.mood-option.selected:active {
  transform: scale(1.02);
}
