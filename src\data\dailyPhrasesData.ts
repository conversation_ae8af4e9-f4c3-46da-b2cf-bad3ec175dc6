import type { DailyPhrase } from '../types/dailyPhrase';

// 通过片段组合生成三年(>=1095天)不重复每日短语
// 保证: 数量 >= 1095, 中文唯一, 分类均衡分配
// 设计: 选择有限可控的片段库(带拼音/英文)进行笛卡尔积并打乱后截取

interface PhraseFragment {
  zh: string;
  py: string;
  en: string;
  tags: string[];
}

interface FragmentSet {
  start: PhraseFragment[];
  middle: PhraseFragment[];
  end: PhraseFragment[];
}

// 片段库(避免版权风险: 自行撰写的短通用励志/心理关怀片段)
const fragments: FragmentSet = {
  start: [
    { zh: '保持', py: 'Bǎochí', en: 'Keep', tags: ['坚持'] },
    { zh: '带着', py: 'Dàizhe', en: 'With', tags: ['携带'] },
    { zh: '允许', py: 'Yǔnxǔ', en: 'Allow', tags: ['接纳'] },
    { zh: '相信', py: 'Xiāngxìn', en: 'Believe', tags: ['信念'] },
    { zh: '拥抱', py: 'Yōngbào', en: 'Embrace', tags: ['接纳'] },
    { zh: '慢慢体会', py: 'Mànmàn tǐhuì', en: 'Gently feel', tags: ['觉察'] },
    { zh: '静静守护', py: 'Jìngjìng shǒuhù', en: 'Quietly guard', tags: ['守护'] },
    { zh: '温柔看见', py: 'Wēnróu kànjiàn', en: 'Gently notice', tags: ['温柔'] }
  ],
  middle: [
    { zh: '内心正在苏醒的微光', py: 'nèixīn zhèngzài sūxǐng de wēiguāng', en: 'the awakening inner glimmer', tags: ['内心','光'] },
    { zh: '一点点生长的勇气', py: 'yī diǎndiǎn shēngzhǎng de yǒngqì', en: 'the slowly growing courage', tags: ['勇气'] },
    { zh: '尚未被说出口的温暖', py: 'shàngwèi bèi shuō chūkǒu de wēnnuǎn', en: 'the unspoken warmth', tags: ['温暖'] },
    { zh: '正在自我修复的情绪', py: 'zhèngzài zìwǒ xiūfù de qíngxù', en: 'the emotions healing themselves', tags: ['情绪','修复'] },
    { zh: '平凡日子里的细小发现', py: 'píngfán rìzi lǐ de xìxiǎo fāxiàn', en: 'the tiny finds in ordinary days', tags: ['发现','平凡'] },
    { zh: '你尚未察觉的力量', py: 'nǐ shàngwèi chájué de lìliàng', en: 'the strength not yet noticed', tags: ['力量'] },
    { zh: '正在被接纳的那份不完美', py: 'zhèngzài bèi jiēnà de nà fèn bù wánměi', en: 'the imperfection being accepted', tags: ['接纳','不完美'] },
    { zh: '缓缓沉淀下来的安定', py: 'huǎnhuǎn chéndiàn xiàlái de āndìng', en: 'the stability slowly settling', tags: ['安定'] },
    { zh: '在呼吸里循环的节奏', py: 'zài hūxī lǐ xúnhuán de jiézòu', en: 'the rhythm cycling in breaths', tags: ['呼吸','节奏'] }
  ],
  end: [
    { zh: '它正在悄悄支持你前行。', py: 'tā zhèngzài qiāoqiāo zhīchí nǐ qiánxíng.', en: 'it is quietly supporting you forward.', tags: ['支持','前行'] },
    { zh: '让今天多了一点亮度。', py: 'ràng jīntiān duōle yīdiǎn liàngdù.', en: 'making today a bit brighter.', tags: ['今日','光'] },
    { zh: '会在需要的时候化成答案。', py: 'huì zài xūyào de shíhòu huà chéng dáàn.', en: 'will become an answer when needed.', tags: ['答案'] },
    { zh: '它无需喧哗也足够有力。', py: 'tā wúxū xuānhuá yě zúgòu yǒulì.', en: 'it needs no noise yet is strong.', tags: ['安静','力量'] },
    { zh: '请耐心与它并肩。', py: 'qǐng nàixīn yǔ tā bìngjiān.', en: 'be patient and walk beside it.', tags: ['耐心','陪伴'] },
    { zh: '它值得被温柔收藏。', py: 'tā zhídé bèi wēnróu shōucáng.', en: 'it deserves gentle keeping.', tags: ['收藏'] },
    { zh: '终会汇聚成清晰方向。', py: 'zhōng huì huìjù chéng qīngxī fāngxiàng.', en: 'it will gather into a clear direction.', tags: ['方向'] },
    { zh: '会在平静里继续生根。', py: 'huì zài píngjìng lǐ jìxù shēnggēn.', en: 'it will keep rooting in calm.', tags: ['生根','平静'] },
    { zh: '请给自己一个肯定。', py: 'qǐng gěi zìjǐ yī gè kěndìng.', en: 'please give yourself affirmation.', tags: ['肯定'] },
    { zh: '这是成长可靠的证据。', py: 'zhè shì chéngzhǎng kěkào de zhèngjù.', en: 'this is reliable proof of growth.', tags: ['成长'] },
    { zh: '它让明天更值得期待。', py: 'tā ràng míngtiān gèng zhídé qídài.', en: 'it makes tomorrow more expectable.', tags: ['期待'] },
    { zh: '静观其变就是一种力量。', py: 'jìng guān qí biàn jiùshì yī zhǒng lìliàng.', en: 'observing calmly is a strength.', tags: ['观察'] }
  ]
};

// 分类列表(与 types 中一致)
const categories = [
  '积极向上',
  '励志成长',
  '温暖治愈',
  '内心平静',
  '生活智慧',
  '心理健康',
  '人际关系',
  '自我接纳'
];

// 生成所有组合
function buildAllCombinations(): Omit<DailyPhrase, 'id' | 'createdAt' | 'updatedAt'>[] {
  const list: Omit<DailyPhrase, 'id' | 'createdAt' | 'updatedAt'>[] = [];
  let syntheticId = 0; // 用于稳定排序后再随机
  for (const s of fragments.start) {
    for (const m of fragments.middle) {
      for (const e of fragments.end) {
        const zh = `${s.zh}${m.zh}，${e.zh}`;
        const pinyin = `${s.py} ${m.py} ${e.py}`;
        const en = `${s.en} ${m.en}, ${e.en}`;
        const allTags = Array.from(new Set([...s.tags, ...m.tags, ...e.tags]));
        list.push({
          chinese: zh,
          pinyin,
            english: en,
          category: categories[syntheticId % categories.length],
          description: '自动生成的三年每日短语',
          isActive: true,
          usageCount: 0,
          tags: allTags
        });
        syntheticId++;
      }
    }
  }
  return list;
}

function shuffle<T>(arr: T[]): T[] {
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}

// 构建 & 截取 >=1095 条(当前组合数 start*middle*end = 8*9*12 = 864 < 1095)
// 为满足 1095, 增广: 再复制一轮并追加少量变体(尾部追加简单后缀)
const baseCombinations = buildAllCombinations(); // 864
const variants: Omit<DailyPhrase, 'id' | 'createdAt' | 'updatedAt'>[] = [];

const variantSuffixes: PhraseFragment[] = [
  { zh: '今天为自己记录', py: 'jīntiān wèi zìjǐ jìlù', en: 'record it for yourself today', tags: ['记录'] },
  { zh: '值得在心里留白', py: 'zhídé zài xīnlǐ liúbái', en: 'worth a quiet blank in heart', tags: ['留白'] },
  { zh: '静候花开那刻', py: 'jìng hòu huākāi nà kè', en: 'waiting for blooming', tags: ['花开'] },
  { zh: '让善待成为习惯', py: 'ràng shàndài chéngwéi xíguàn', en: 'let kindness be habit', tags: ['善待'] }
];

let variantIndex = 0;
for (let i = 0; i < baseCombinations.length && variants.length < 300; i += 2) { // 挑选一半生成变体
  const base = baseCombinations[i];
  const suf = variantSuffixes[variantIndex % variantSuffixes.length];
  variantIndex++;
  const zh = base.chinese.replace(/。$/, `，${suf.zh}。`);
  variants.push({
    chinese: zh,
    pinyin: base.pinyin + ' ' + suf.py,
    english: base.english.replace(/\.$/, ', ' + suf.en + '.'),
    category: categories[(i + 3) % categories.length],
    description: '自动生成的三年每日短语(变体)',
    isActive: true,
    usageCount: 0,
    tags: Array.from(new Set([...(base.tags || []), ...suf.tags]))
  });
}

let combined = shuffle([...baseCombinations, ...variants]);
if (combined.length < 1095) {
  // 若仍不足, 重复选取前若干并打上“循环扩展”标记
  const needed = 1095 - combined.length;
  for (let i = 0; i < needed; i++) {
    const src = combined[i];
    combined.push({
      ...src,
      chinese: src.chinese.replace(/。$/, '【扩展】。'),
      description: '自动生成的三年每日短语(扩展补足)',
      tags: Array.from(new Set([...(src.tags || []), '扩展']))
    });
  }
}
// 截取前 1095 条
combined = combined.slice(0, 1095);

// 导出静态数组(保持旧命名兼容)
export const threeYearPhrases: Omit<DailyPhrase, 'id' | 'createdAt' | 'updatedAt'>[] = combined;

// 生成函数(现在返回已生成的静态内容)
export const generateThreeYearPhrases = (): Omit<DailyPhrase, 'id' | 'createdAt' | 'updatedAt'>[] => {
  return threeYearPhrases;
};

// 数量充足校验
export const checkPhraseSufficiency = (phrases: DailyPhrase[]): boolean => {
  const threeYearsDays = 3 * 365; // 1095天
  return phrases.length >= threeYearsDays;
};

// 简单统计工具
export const getPhraseStats = () => ({
  total: threeYearPhrases.length,
  categories: categories.reduce<Record<string, number>>((acc, c) => {
    acc[c] = threeYearPhrases.filter(p => p.category === c).length;
    return acc;
  }, {})
});