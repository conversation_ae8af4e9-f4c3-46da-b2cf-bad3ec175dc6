/* Layout.css - 统一的布局组件样式 */
@import '../../styles/variables.css';

/* EmptyState 组件样式 - 柔和统一的空状态设计 */
.empty-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 20px;
  background: transparent;
  border-radius: 0;
  border: none;
  margin: 0;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  animation: fadeInUp 0.6s ease-out;
}

.empty-state-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 50%;
  border: 2px solid #e0f2fe;
  color: #64b5f6;
  transition: all 0.3s ease;
}

.empty-state-icon svg {
  width: 40px;
  height: 40px;
  opacity: 0.8;
}

.empty-state-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.empty-state-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 32px 0;
  max-width: 320px;
}

.empty-state-action {
  margin-top: 8px;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .empty-state-container {
    min-height: 250px;
    padding: 40px 16px;
    margin: 16px 0;
  }
  
  .empty-state-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }
  
  .empty-state-icon svg {
    width: 32px;
    height: 32px;
  }
  
  .empty-state-title {
    font-size: 16px;
  }
  
  .empty-state-description {
    font-size: 13px;
    margin-bottom: 24px;
  }
}

/* 支持暗色主题 */
@media (prefers-color-scheme: dark) {
  .empty-state-container {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
  }
  
  .empty-state-icon {
    background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
    border-color: #3b82f6;
    color: #93c5fd;
  }
  
  .empty-state-title {
    color: #f1f5f9;
  }
  
  .empty-state-description {
    color: #cbd5e1;
  }
}

/* 减少动画运动 */
@media (prefers-reduced-motion: reduce) {
  .empty-state-content {
    animation: none;
  }
  
  .empty-state-icon {
    transition: none;
  }
}
