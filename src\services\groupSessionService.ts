import type { 
  GroupSession, 
  GroupParticipant, 
  GroupSessionRecord, 
  GroupParticipantSandToolUsage,
  GroupSessionStats 
} from '../types/groupSession';
import { electronDataManager } from './electronDataManager';

export class GroupSessionService {
  
  // 获取所有团体活动
  async getAllGroupSessions(): Promise<GroupSession[]> {
    return await electronDataManager.getAllGroupSessions();
  }

  // 获取单个团体活动
  async getGroupSession(id: string): Promise<GroupSession | null> {
    return await electronDataManager.getGroupSession(id);
  }

  // 创建团体活动
  async createGroupSession(sessionData: Omit<GroupSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<GroupSession> {
    const newSession: GroupSession = {
      ...sessionData,
      id: `group-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await electronDataManager.saveGroupSession(newSession);
    return newSession;
  }

  // 更新团体活动
  async updateGroupSession(id: string, updates: Partial<GroupSession>): Promise<GroupSession | null> {
    const existingSession = await this.getGroupSession(id);
    if (!existingSession) return null;

    const updatedSession: GroupSession = {
      ...existingSession,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await electronDataManager.saveGroupSession(updatedSession);
    return updatedSession;
  }

  // 删除团体活动
  async deleteGroupSession(id: string): Promise<boolean> {
    try {
      await electronDataManager.deleteGroupSession(id);
      return true;
    } catch (error) {
      console.error('删除团体活动失败:', error);
      return false;
    }
  }

  // 搜索团体活动
  async searchGroupSessions(query: string): Promise<GroupSession[]> {
    const allSessions = await this.getAllGroupSessions();
    const searchTerm = query.toLowerCase();
    
    return allSessions.filter(session =>
      session.title.toLowerCase().includes(searchTerm) ||
      (session.description && session.description.toLowerCase().includes(searchTerm)) ||
      session.therapistName.toLowerCase().includes(searchTerm) ||
      session.location.toLowerCase().includes(searchTerm)
    );
  }

  // 筛选团体活动
  async filterGroupSessions(filters: {
    status?: string;
    therapistId?: string;
    sessionType?: string;
    targetAge?: string;
  }): Promise<GroupSession[]> {
    const allSessions = await this.getAllGroupSessions();
    let filtered = [...allSessions];

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(session => session.status === filters.status);
    }

    if (filters.therapistId && filters.therapistId !== 'all') {
      filtered = filtered.filter(session => session.therapistId === filters.therapistId);
    }

    if (filters.sessionType && filters.sessionType !== 'all') {
      filtered = filtered.filter(session => session.sessionType === filters.sessionType);
    }

    if (filters.targetAge && filters.targetAge !== 'all') {
      filtered = filtered.filter(session => session.targetAge === filters.targetAge);
    }

    return filtered;
  }

  // 获取团体活动统计信息
  async getGroupSessionStats(): Promise<GroupSessionStats> {
    const sessions = await this.getAllGroupSessions();
    
    const activeSessions = sessions.filter(s => s.status === '进行中').length;
    sessions.filter(s => s.status === '已完成').length;
    sessions.filter(s => s.status === '计划中').length;

    // 统计总参与者数
    const totalParticipants = sessions.reduce((sum, session) => sum + session.currentParticipants, 0);

    // 计算平均出勤率
    let totalAttendanceRate = 0;
    let participantCount = 0;
    sessions.forEach(session => {
      session.participants.forEach(participant => {
        if (participant.attendanceRate !== undefined) {
          totalAttendanceRate += participant.attendanceRate;
          participantCount++;
        }
      });
    });
    const averageAttendance = participantCount > 0 ? totalAttendanceRate / participantCount : 0;

    return {
      totalGroups: sessions.length,
      activeGroups: activeSessions,
      totalParticipants,
      totalSessions: sessions.reduce((sum, session) => sum + (session.currentSession || 0), 0),
      averageAttendance,
      mostUsedSandTools: [] // 这个需要从沙具使用记录中统计
    };
  }

  // 获取即将进行的团体活动
  async getUpcomingGroupSessions(days: number = 7): Promise<GroupSession[]> {
    try {
      const sessions = await this.getAllGroupSessions();
      const now = new Date();
      const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

      return sessions.filter(session => {
        if (session.status !== '计划中' && session.status !== '进行中') return false;
        
        const sessionDate = new Date(session.startDate);
        return sessionDate >= now && sessionDate <= futureDate;
      });
    } catch (error) {
      console.error('获取即将进行的团体活动失败:', error);
      return [];
    }
  }

  // 获取活跃的团体活动
  async getActiveGroupSessions(): Promise<GroupSession[]> {
    try {
      const sessions = await this.getAllGroupSessions();
      return sessions.filter(session => session.status === '进行中');
    } catch (error) {
      console.error('获取活跃团体活动失败:', error);
      return [];
    }
  }

  // 更新团体状态
  async updateSessionStatus(id: string, status: GroupSession['status']): Promise<boolean> {
    try {
      const updated = await this.updateGroupSession(id, { status });
      return updated !== null;
    } catch (error) {
      console.error('更新团体状态失败:', error);
      return false;
    }
  }

  // 添加参与者到团体
  async addParticipantToSession(sessionId: string, participant: Omit<GroupParticipant, 'id' | 'joinDate' | 'status'>): Promise<boolean> {
    try {
      const session = await this.getGroupSession(sessionId);
      if (!session) return false;

      // 检查是否已存在
      const existingParticipant = session.participants.find(p => p.visitorId === participant.visitorId);
      if (existingParticipant) {
        return false; // 已存在
      }

      if (session.participants.length >= session.maxParticipants) {
        return false; // 人数已满
      }

      const newParticipant: GroupParticipant = {
        ...participant,
        id: `participant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        joinDate: new Date().toISOString(),
        status: '进行中'
      };

      const updatedParticipants = [...session.participants, newParticipant];
      const updated = await this.updateGroupSession(sessionId, {
        participants: updatedParticipants,
        currentParticipants: updatedParticipants.length
      });

      return updated !== null;
    } catch (error) {
      console.error('添加参与者失败:', error);
      return false;
    }
  }

  // 从团体中移除参与者
  async removeParticipantFromSession(sessionId: string, participantId: string): Promise<boolean> {
    try {
      const session = await this.getGroupSession(sessionId);
      if (!session) return false;

      const updatedParticipants = session.participants.filter(p => p.id !== participantId);
      const updated = await this.updateGroupSession(sessionId, {
        participants: updatedParticipants,
        currentParticipants: updatedParticipants.length
      });

      return updated !== null;
    } catch (error) {
      console.error('移除参与者失败:', error);
      return false;
    }
  }

  // ==================== 活动记录相关方法 ====================

  // 创建活动记录
  async createSessionRecord(recordData: Omit<GroupSessionRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<GroupSessionRecord> {
    const newRecord: GroupSessionRecord = {
      ...recordData,
      id: `session-record-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 这里需要调用数据管理器保存记录
    // await electronDataManager.saveGroupSessionRecord(newRecord);
    return newRecord;
  }

  // 获取团体的所有活动记录
  async getSessionRecords(): Promise<GroupSessionRecord[]> {
    // 这里需要从数据库获取记录
    // return await electronDataManager.getGroupSessionRecords(groupSessionId);
    return [];
  }

  // 更新活动记录
  async updateSessionRecord(): Promise<GroupSessionRecord | null> {
    // 这里需要实现更新逻辑
    return null;
  }

  // 删除活动记录
  async deleteSessionRecord(): Promise<boolean> {
    try {
      // 这里需要实现删除逻辑
      return true;
    } catch (error) {
      console.error('删除活动记录失败:', error);
      return false;
    }
  }

  // ==================== 参与者沙具使用记录相关方法 ====================

  // 记录参与者沙具使用
  async recordParticipantSandToolUsage(usageData: Omit<GroupParticipantSandToolUsage, 'id' | 'createdAt'>): Promise<GroupParticipantSandToolUsage> {
    const newUsage: GroupParticipantSandToolUsage = {
      ...usageData,
      id: `sand-tool-usage-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    };

    // 这里需要调用数据管理器保存记录
    // await electronDataManager.saveGroupParticipantSandToolUsage(newUsage);
    return newUsage;
  }

  // 获取活动记录的所有沙具使用记录
  async getSessionSandToolUsage(): Promise<GroupParticipantSandToolUsage[]> {
    // 这里需要从数据库获取记录
    // return await electronDataManager.getGroupParticipantSandToolUsage(sessionRecordId);
    return [];
  }

  // 获取参与者的沙具使用记录
  async getParticipantSandToolUsage(): Promise<GroupParticipantSandToolUsage[]> {
    // 这里需要从数据库获取记录
    return [];
  }

  // 批量记录多个参与者的沙具使用
  async batchRecordSandToolUsage(usageList: Omit<GroupParticipantSandToolUsage, 'id' | 'createdAt'>[]): Promise<GroupParticipantSandToolUsage[]> {
    const results: GroupParticipantSandToolUsage[] = [];

    for (const usage of usageList) {
      const recorded = await this.recordParticipantSandToolUsage(usage);
      results.push(recorded);
    }

    return results;
  }

  // 更新沙具使用记录
  async updateSandToolUsage(): Promise<GroupParticipantSandToolUsage | null> {
    // 这里需要实现更新逻辑
    return null;
  }

  // 删除沙具使用记录
  async deleteSandToolUsage(): Promise<boolean> {
    try {
      // 这里需要实现删除逻辑
      return true;
    } catch (error) {
      console.error('删除沙具使用记录失败:', error);
      return false;
    }
  }

  // ==================== 统计分析相关方法 ====================

  // 获取最常用的沙具统计
  async getMostUsedSandTools(): Promise<Array<{toolId: string; toolName: string; usageCount: number}>> {
    // 这里需要统计沙具使用频率
    return [];
  }

  // 获取参与者活跃度统计
  async getParticipantActivityStats(): Promise<Array<{participantId: string; participantName: string; activityScore: number}>> {
    // 这里需要计算参与者活跃度
    return [];
  }
}

// 导出实例
export const groupSessionService = new GroupSessionService();
