import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  PageHeader,
  Card,
  CardContent,
  Button,
  Badge,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  LoadingSpinner,
  EmptyState,
  Pagination,
  FilterBar
} from '../ui/index';
import {
  Plus,
  Search,
  Users,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  Clock,
  Filter
} from 'lucide-react';
import { groupSessionService } from '../../services/groupSessionService';
import type { GroupSession } from '../../types/groupSession';
import CreateGroupSessionModal from './CreateGroupSessionModal';
import GroupSessionDetailModal from './GroupSessionDetailModal';
import './GroupSessions.css';

// 团沙管理页面
const GroupSessions: React.FC = () => {
  // 状态管理
  const [groupSessions, setGroupSessions] = useState<GroupSession[]>([]);
  const [filteredSessions, setFilteredSessions] = useState<GroupSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [ageFilter, setAgeFilter] = useState('all');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<GroupSession | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');

  // 加载数据
  useEffect(() => {
    loadGroupSessions();
  }, []);

  const applyFilters = useCallback(() => {
    let filtered = [...groupSessions];

    // 搜索筛选
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(session =>
        session.title.toLowerCase().includes(query) ||
        session.therapistName.toLowerCase().includes(query) ||
        session.location.toLowerCase().includes(query) ||
        (session.description && session.description.toLowerCase().includes(query))
      );
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(session => session.status === statusFilter);
    }

    // 类型筛选
    if (typeFilter !== 'all') {
      filtered = filtered.filter(session => session.sessionType === typeFilter);
    }

    // 年龄群体筛选
    if (ageFilter !== 'all') {
      filtered = filtered.filter(session => session.targetAge === ageFilter);
    }

    setFilteredSessions(filtered);
    setCurrentPage(1);
  }, [groupSessions, searchQuery, statusFilter, typeFilter, ageFilter]);

  // 应用筛选
  useEffect(() => {
    applyFilters();
  }, [groupSessions, searchQuery, statusFilter, typeFilter, ageFilter, applyFilters]);

  const loadGroupSessions = async () => {
    try {
      setLoading(true);
      const sessions = await groupSessionService.getAllGroupSessions();
      setGroupSessions(sessions);
    } catch (error) {
      console.error('加载团体活动失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 分页计算
  const totalPages = Math.ceil(filteredSessions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentPageSessions = filteredSessions.slice(startIndex, startIndex + itemsPerPage);

  // 事件处理函数
  const handleCreateSession = () => {
    setSelectedSession(null);
    setModalMode('create');
    setShowCreateModal(true);
  };

  const handleViewSession = (session: GroupSession) => {
    setSelectedSession(session);
    setModalMode('view');
    setShowDetailModal(true);
  };

  const handleEditSession = (session: GroupSession) => {
    setSelectedSession(session);
    setModalMode('edit');
    setShowCreateModal(true);
  };

  const handleDeleteSession = async (session: GroupSession) => {
    if (confirm(`确定要删除团体活动"${session.title}"吗？此操作不可恢复。`)) {
      try {
        const success = await groupSessionService.deleteGroupSession(session.id);
        if (success) {
          await loadGroupSessions();
          alert('删除成功');
        } else {
          alert('删除失败');
        }
      } catch (error) {
        console.error('删除团体活动失败:', error);
        alert('删除失败，请稍后重试');
      }
    }
  };

  const handleUpdateStatus = async (session: GroupSession, newStatus: GroupSession['status']) => {
    try {
      const success = await groupSessionService.updateSessionStatus(session.id, newStatus);
      if (success) {
        await loadGroupSessions();
      } else {
        alert('状态更新失败');
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      alert('状态更新失败，请稍后重试');
    }
  };

  // 状态徽章
  const getStatusBadge = (status: GroupSession['status']) => {
    const statusConfig = {
      '计划中': { variant: 'primary' as const, icon: <Clock size={12} /> },
      '进行中': { variant: 'success' as const, icon: <Play size={12} /> },
      '已完成': { variant: 'gray' as const, icon: <CheckCircle size={12} /> },
      '已取消': { variant: 'danger' as const, icon: <Pause size={12} /> },
      '暂停': { variant: 'warning' as const, icon: <Pause size={12} /> }
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        {config.icon}
        {status}
      </Badge>
    );
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN');
  };

  return (
    <PageContainer>
      <PageHeader
        title="团沙管理"
        subtitle="管理团体沙盘治疗活动和参与者"
        actions={
          <div className="flex gap-sm">
            <Button
              variant="secondary"
              leftIcon={<Filter size={16} />}
              onClick={() => console.log('高级筛选')}
            >
              高级筛选
            </Button>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={handleCreateSession}
            >
              创建团沙
            </Button>
          </div>
        }
      />

      {/* 搜索和筛选区域 */}
      <Card className="mb-xl">
        <CardContent>
          <FilterBar
            searchProps={{
              value: searchQuery,
              onChange: (e) => setSearchQuery(e.target.value),
              placeholder: "搜索团体活动（名称、治疗师、地点）",
              leftIcon: <Search size={16} />
            }}
            filters={[
              {
                value: statusFilter,
                onChange: (e) => setStatusFilter(e.target.value),
                options: [
                  { value: 'all', label: '全部状态' },
                  { value: '计划中', label: '计划中' },
                  { value: '进行中', label: '进行中' },
                  { value: '已完成', label: '已完成' },
                  { value: '已取消', label: '已取消' },
                  { value: '暂停', label: '暂停' }
                ]
              },
              {
                value: typeFilter,
                onChange: (e) => setTypeFilter(e.target.value),
                options: [
                  { value: 'all', label: '全部类型' },
                  { value: '开放式团体', label: '开放式团体' },
                  { value: '封闭式团体', label: '封闭式团体' },
                  { value: '主题团体', label: '主题团体' },
                  { value: '发展性团体', label: '发展性团体' }
                ]
              },
              {
                value: ageFilter,
                onChange: (e) => setAgeFilter(e.target.value),
                options: [
                  { value: 'all', label: '全部年龄' },
                  { value: '儿童', label: '儿童' },
                  { value: '青少年', label: '青少年' },
                  { value: '成人', label: '成人' },
                  { value: '老年', label: '老年' },
                  { value: '混合', label: '混合' }
                ]
              }
            ]}
          />
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="stats-row mb-xl">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{filteredSessions.length}</div>
              <div className="stat-label">总团体数</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{filteredSessions.filter(s => s.status === '进行中').length}</div>
              <div className="stat-label">进行中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{filteredSessions.filter(s => s.status === '计划中').length}</div>
              <div className="stat-label">计划中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{filteredSessions.reduce((sum, s) => sum + s.currentParticipants, 0)}</div>
              <div className="stat-label">总参与者</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner />
            </div>
          ) : filteredSessions.length === 0 ? (
            <EmptyState
              icon={<Users size={64} className="opacity-30" />}
              title={searchQuery || statusFilter !== 'all' || typeFilter !== 'all' || ageFilter !== 'all' ? '没有找到匹配的团体活动' : '暂无团体活动'}
              description={searchQuery || statusFilter !== 'all' || typeFilter !== 'all' || ageFilter !== 'all' ? '请尝试调整筛选条件' : '点击上方按钮创建第一个团体活动'}
              action={
                <Button
                  leftIcon={<Plus size={16} />}
                  onClick={handleCreateSession}
                >
                  创建团体活动
                </Button>
              }
            />
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell isHeader>团体名称</TableCell>
                    <TableCell isHeader>类型</TableCell>
                    <TableCell isHeader>治疗师</TableCell>
                    <TableCell isHeader>参与者</TableCell>
                    <TableCell isHeader>地点</TableCell>
                    <TableCell isHeader>状态</TableCell>
                    <TableCell isHeader>开始日期</TableCell>
                    <TableCell isHeader>操作</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPageSessions.map(session => (
                    <TableRow key={session.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{session.title}</div>
                          {session.description && (
                            <div className="text-sm text-gray-500 truncate max-w-48">
                              {session.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="primary">{session.sessionType}</Badge>
                      </TableCell>
                      <TableCell>{session.therapistName}</TableCell>
                      <TableCell>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                          <Users size={14} />
                          {session.currentParticipants}/{session.maxParticipants}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                          <MapPin size={14} />
                          {session.location}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(session.status)}</TableCell>
                      <TableCell>{formatDate(session.startDate)}</TableCell>
                      <TableCell>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewSession(session)}
                            title="查看详情"
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditSession(session)}
                            title="编辑"
                          >
                            <Edit size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSession(session)}
                            title="删除"
                            style={{ color: '#ef4444' }}
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页和底部信息 */}
              {totalPages > 1 && (
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center', 
                  marginTop: '16px',
                  paddingTop: '16px',
                  borderTop: '1px solid #e5e7eb'
                }}>
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>
                    共 {filteredSessions.length} 个团体活动
                  </span>
                  
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                    showTotal={true}
                    total={filteredSessions.length}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* 创建/编辑模态框 */}
      {showCreateModal && (
        <CreateGroupSessionModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={async (sessionData: Omit<GroupSession, 'id' | 'createdAt' | 'updatedAt'>) => {
            if (modalMode === 'create') {
              await groupSessionService.createGroupSession(sessionData);
            } else {
              await groupSessionService.updateGroupSession(selectedSession!.id, sessionData);
            }
            await loadGroupSessions();
            setShowCreateModal(false);
          }}
          session={modalMode === 'edit' ? selectedSession : null}
          mode={modalMode}
        />
      )}

      {/* 详情模态框 */}
      {showDetailModal && selectedSession && (
        <GroupSessionDetailModal
          isOpen={showDetailModal}
          session={selectedSession}
          onClose={() => setShowDetailModal(false)}
          onEdit={() => {
            setShowDetailModal(false);
            handleEditSession(selectedSession);
          }}
          onDelete={() => {
            setShowDetailModal(false);
            handleDeleteSession(selectedSession);
          }}
          onUpdateStatus={(status: GroupSession['status']) => handleUpdateStatus(selectedSession, status)}
        />
      )}
    </PageContainer>
  );
};

export default GroupSessions;
