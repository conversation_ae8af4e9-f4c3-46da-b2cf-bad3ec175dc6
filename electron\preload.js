const { contextBridge, ipcRenderer } = require('electron');

// 安全地暴露 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 数据库操作
  dbQuery: (sql, params) => ipcRenderer.invoke('db-query', sql, params),
  dbRun: (sql, params) => ipcRenderer.invoke('db-run', sql, params),
  dbStatus: () => ipcRenderer.invoke('db-status'), // 新增的 dbStatus API
  
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: (name) => ipcRenderer.invoke('get-app-path', name),
  
  // 浏览器功能
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),
  
  // 系统通知
  showNotification: (title, options) => ipcRenderer.invoke('show-notification', title, options),
  
  // 窗口控制（自定义标题栏）
  windowAction: (action) => ipcRenderer.send('window-control', action),
  
  // 文件操作
  exportData: (data, options) => ipcRenderer.invoke('export-data', data, options),
  importData: (filePath) => ipcRenderer.invoke('import-data', filePath),
  
  // 新增：备份 / 恢复 / 清理 / 列出备份
  performBackup: (options) => ipcRenderer.invoke('perform-backup', options),
  listBackups: () => ipcRenderer.invoke('list-backups'),
  restoreBackup: (filePath) => ipcRenderer.invoke('restore-backup', filePath),
  cleanupData: (options) => ipcRenderer.invoke('cleanup-data', options),
  
  // 平台信息
  platform: process.platform,
  isElectron: true
});

// 窗口加载完成后通知主进程
window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron preload script loaded');
});
