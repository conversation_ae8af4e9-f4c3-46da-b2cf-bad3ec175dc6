// 测试每日短语功能
const testDailyPhrases = async () => {
  console.log('开始测试每日短语功能...');
  
  try {
    // 模拟环境检查
    console.log('1. 检查环境支持...');
    if (typeof window === 'undefined') {
      console.log('❌ 非浏览器环境');
      return;
    }
    
    // 检查是否有电子API
    if (!window.electronAPI) {
      console.log('⚠️ 缺少 Electron API，模拟数据库操作');
      // 创建模拟 API
      window.electronAPI = {
        isElectron: false,
        dbQuery: async (sql, params) => {
          console.log('模拟查询:', sql, params);
          if (sql.includes('daily_phrases')) {
            return []; // 模拟空结果
          }
          return [];
        },
        dbRun: async (sql, params) => {
          console.log('模拟执行:', sql, params);
          return { changes: 1, lastInsertRowid: Date.now() };
        }
      };
    }
    
    console.log('✅ 环境检查完成');
    
    // 动态导入服务
    console.log('2. 加载每日短语服务...');
    const { dailyPhraseService } = await import('../src/services/dailyPhraseService.js');
    console.log('✅ 服务加载完成');
    
    // 测试获取短语
    console.log('3. 测试获取短语...');
    try {
      const phrases = await dailyPhraseService.getAllPhrases();
      console.log('✅ 获取到短语数量:', phrases.length);
      
      if (phrases.length === 0) {
        console.log('4. 初始化默认短语...');
        await dailyPhraseService.initializeDefaultPhrases();
        console.log('✅ 默认短语初始化完成');
        
        // 重新获取
        const newPhrases = await dailyPhraseService.getAllPhrases();
        console.log('✅ 重新获取短语数量:', newPhrases.length);
      }
      
      // 测试获取今日短语
      console.log('5. 测试获取今日短语...');
      const todayPhrase = await dailyPhraseService.getTodayPhrase();
      if (todayPhrase) {
        console.log('✅ 今日短语:', todayPhrase.chinese);
      } else {
        console.log('⚠️ 没有找到今日短语');
      }
      
      // 测试获取随机短语
      console.log('6. 测试获取随机短语...');
      const randomPhrase = await dailyPhraseService.getRandomPhrase();
      if (randomPhrase) {
        console.log('✅ 随机短语:', randomPhrase.chinese);
      } else {
        console.log('⚠️ 没有找到随机短语');
      }
      
      // 测试统计
      console.log('7. 测试短语统计...');
      const stats = await dailyPhraseService.getPhraseStats();
      console.log('✅ 短语统计:', stats);
      
    } catch (serviceError) {
      console.error('❌ 服务测试失败:', serviceError);
    }
    
    console.log('✅ 每日短语功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
};

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testDailyPhrases };
} else if (typeof window !== 'undefined') {
  window.testDailyPhrases = testDailyPhrases;
}

// 如果直接运行，则执行测试
if (typeof require !== 'undefined' && require.main === module) {
  testDailyPhrases();
}

console.log('每日短语测试脚本已加载，可以调用 testDailyPhrases() 进行测试');
