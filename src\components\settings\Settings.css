/* 系统设置页面样式 */
@import '../../styles/variables.css';

.settings-container {
  max-width: 1400px;
  margin: 0 auto;
}

.settings-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--spacing-2xl);
  align-items: start;
}

/* 左侧导航栏 */
.settings-sidebar {
  position: sticky;
  top: var(--spacing-3xl);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.settings-nav {
  display: flex;
  flex-direction: column;
}

.settings-nav-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border: none;
  background: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.settings-nav-item:last-child {
  border-bottom: none;
}

.settings-nav-item:hover {
  background: var(--bg-secondary);
}

.settings-nav-item.active {
  background: rgb(79 156 249 / 0.1);
}

.nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-top: 2px;
}

.settings-nav-item.active .nav-item-icon {
  color: var(--primary-blue);
}

.nav-item-content {
  flex: 1;
  min-width: 0;
}

.nav-item-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.nav-item-description {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: 1.4;
}

/* 右侧内容区域 */
.settings-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.settings-content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.content-header-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.content-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-blue);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
}

.content-header-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.content-header-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
}

.content-header-status {
  display: flex;
  align-items: center;
}

.saving-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.saving-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.settings-content-body {
  padding: var(--spacing-2xl);
}

/* 加载状态 */
.settings-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

/* 占位符内容 */
.settings-placeholder {
  text-align: center;
  padding: var(--spacing-6xl) var(--spacing-3xl);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.settings-placeholder h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.settings-placeholder p {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-md) 0;
  max-width: 400px;
}

.settings-placeholder p:last-child {
  color: var(--text-tertiary);
  font-style: italic;
}

/* 设置表单通用样式 */
.settings-form {
  max-width: 800px;
}

.settings-form .card {
  margin-bottom: var(--spacing-xl);
}

.settings-form .card:last-child {
  margin-bottom: 0;
}

.settings-section {
  margin-bottom: var(--spacing-4xl);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.settings-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.settings-form-row {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-end;
}

.settings-form-row .form-group {
  flex: 1;
}

/* 复选框容器增加间距 */
.checkbox-container {
  margin-bottom: var(--spacing-md);
}

.checkbox-container:last-child {
  margin-bottom: 0;
}

.settings-actions {
  display: flex;
  gap: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-xl);
}

/* 主题预览样式 */
.theme-preview {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.preview-card {
  max-width: 200px;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.preview-header {
  padding: var(--spacing-md);
  color: white;
  font-weight: var(--font-medium);
}

.preview-title {
  font-size: var(--text-sm);
}

.preview-content {
  padding: var(--spacing-md);
}

.preview-content p {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-md) 0;
}

.preview-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  color: white;
  text-align: center;
}

/* 复选框组样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.checkbox-group .checkbox-container {
  margin-bottom: 0;
}

/* 文件输入包装器 */
.file-input-wrapper {
  position: relative;
  display: inline-block;
}

/* 文本样式 */
.text-sm {
  font-size: var(--text-sm);
}

.text-secondary {
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
  
  .settings-sidebar {
    position: static;
    order: 2;
  }
  
  .settings-content {
    order: 1;
  }
  
  .settings-nav {
    flex-direction: row;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .settings-nav::-webkit-scrollbar {
    display: none;
  }
  
  .settings-nav-item {
    flex-shrink: 0;
    min-width: 200px;
    border-bottom: none;
    border-right: 1px solid var(--border-light);
  }
  
  .settings-nav-item:last-child {
    border-right: none;
  }
  
  .settings-nav-item.active {
    border-right: 1px solid var(--border-light);
    border-bottom: 3px solid var(--primary-blue);
  }
}

@media (max-width: 768px) {
  .settings-content-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: flex-start;
  }
  
  .content-header-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .settings-content-body {
    padding: var(--spacing-xl);
  }
  
  .settings-form-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-form-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .settings-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .settings-nav-item {
    min-width: 160px;
    padding: var(--spacing-lg);
  }
  
  .nav-item-description {
    display: none;
  }
  
  .settings-placeholder {
    padding: var(--spacing-4xl) var(--spacing-lg);
  }
  
  .settings-actions {
    flex-direction: column;
  }
  
  .checkbox-group {
    gap: var(--spacing-lg);
  }
  
  .theme-preview {
    text-align: center;
  }
  
  .preview-card {
    margin: 0 auto;
  }
}
