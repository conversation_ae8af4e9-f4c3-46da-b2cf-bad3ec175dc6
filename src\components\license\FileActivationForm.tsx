import React, { useState, useCallback, useEffect } from 'react';
import { Upload, X, FileText, CheckCircle, AlertCircle, Shield } from 'lucide-react';
import { activateWithFile, isDeviceBlacklisted, getRemainingAttempts } from '../../utils/auth/licenseValidator';
import { useToast } from '../ui/Toast';
import '../ui/BaseModal.css';

interface FileActivationFormProps {
  onSuccess: () => void;
  onClose: () => void;
}

const FileActivationForm: React.FC<FileActivationFormProps> = ({ onSuccess, onClose }) => {
  const [dragOver, setDragOver] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isBlacklisted, setIsBlacklisted] = useState(false);
  const [remainingAttempts, setRemainingAttempts] = useState(5);
  const toast = useToast();

  // 检查安全状态
  useEffect(() => {
    const checkSecurityState = async () => {
      try {
        const blacklisted = await isDeviceBlacklisted();
        const attempts = await getRemainingAttempts();
        setIsBlacklisted(blacklisted);
        setRemainingAttempts(attempts);

        if (blacklisted) {
          setError('设备已被拉黑，无法进行激活操作');
        }
      } catch (error) {
        console.error('检查安全状态失败:', error);
      }
    };

    checkSecurityState();
  }, []);

  const handleFileSelect = useCallback((file: File) => {
    // 检查文件类型
    if (!file.name.endsWith('.lic') && !file.name.endsWith('.license')) {
      setError('请选择有效的激活文件（.lic 或 .license 格式）');
      return;
    }

    // 检查文件大小（最大1MB）
    if (file.size > 1024 * 1024) {
      setError('激活文件过大，请确认文件是否正确');
      return;
    }

    setSelectedFile(file);
    setError('');
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleClick = useCallback(() => {
    if (isValidating) return;
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.lic,.license';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    };
    input.click();
  }, [isValidating, handleFileSelect]);

  const handleRemoveFile = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFile(null);
    setError('');
  }, []);

  const handleActivate = async () => {
    if (!selectedFile) {
      setError('请先选择激活文件');
      toast.warning('请先选择激活文件');
      return;
    }

    // 检查是否被拉黑
    if (isBlacklisted) {
      setError('设备已被拉黑，无法进行激活操作');
      toast.error('设备已被拉黑，无法激活');
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      const success = await activateWithFile(selectedFile);

      if (success) {
        toast.success('激活成功');
        onSuccess();
      } else {
        // 激活失败，更新剩余尝试次数
        const newAttempts = await getRemainingAttempts();
        setRemainingAttempts(newAttempts);

        if (newAttempts === 0) {
          setIsBlacklisted(true);
          setError('激活失败次数过多，设备已被永久拉黑');
          toast.error('设备已被拉黑，无法继续使用');
        } else {
          setError(`激活失败：激活文件无效或与当前设备不匹配。剩余尝试次数：${newAttempts}`);
          toast.error(`激活失败，剩余尝试次数：${newAttempts}`);
        }
      }
    } catch (err) {
      console.error('激活过程出错:', err);
      setError('激活过程出错，请稍后重试');
      toast.error('激活出错，请稍后重试');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="base-modal-container base-modal-md">
      {/* 头部 */}
      <div className="base-modal-header">
        <div className="base-modal-header-content">
          <div className="base-modal-title-section">
            <h3 className="base-modal-title">软件激活</h3>
            <p className="base-modal-subtitle">请按步骤完成激活</p>
          </div>
        </div>
        <button
          className="base-modal-close"
          onClick={onClose}
          aria-label="关闭激活窗口"
        >
          <X size={20} />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="base-modal-content">
        <div className="p-6">
          {/* 安全状态提示 */}
          {isBlacklisted ? (
            <div className="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 flex items-center">
              <Shield className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <span className="text-red-700 text-sm">设备已被拉黑，无法进行激活操作</span>
            </div>
          ) : remainingAttempts < 5 && (
            <div className="mb-4 p-4 rounded-lg bg-orange-50 border border-orange-200 flex items-center">
              <Shield className="w-5 h-5 text-orange-500 mr-3 flex-shrink-0" />
              <span className="text-orange-700 text-sm">剩余激活尝试次数：{remainingAttempts}</span>
            </div>
          )}

          {/* 步骤指示器 */}
          <div className="mb-6 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 ${!selectedFile ? 'text-blue-600' : 'text-gray-400'}`}>
                <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${!selectedFile ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'}`}>1</span>
                <span className="text-sm font-medium">选择文件</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className={`flex items-center space-x-2 ${selectedFile ? 'text-green-600' : 'text-gray-400'}`}>
                <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${selectedFile ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>2</span>
                <span className="text-sm font-medium">激活软件</span>
              </div>
            </div>
          </div>

          {/* 文件上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 transition-all duration-200 cursor-pointer text-center ${
              dragOver
                ? 'border-blue-400 bg-blue-50'
                : selectedFile
                  ? 'border-green-400 bg-green-50'
                  : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={handleClick}
          >
            {selectedFile ? (
              <div>
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 rounded-lg bg-green-100 border border-green-200 flex items-center justify-center">
                    <FileText className="w-8 h-8 text-green-600" />
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{selectedFile.name}</h4>
                <p className="text-sm text-gray-600 mb-4">
                  文件大小: {(selectedFile.size / 1024).toFixed(1)} KB
                </p>
                <button
                  onClick={handleRemoveFile}
                  className="btn btn-ghost btn-sm text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4 mr-1" />
                  移除文件
                </button>
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 rounded-lg bg-blue-100 border border-blue-200 flex items-center justify-center">
                    <Upload className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">选择激活文件</h4>
                <p className="text-sm text-gray-600 mb-4">
                  点击选择或拖拽激活文件到此处
                </p>
                <p className="text-xs text-gray-500">
                  支持 .lic 和 .license 格式文件
                </p>
              </div>
            )}
          </div>
          {selectedFile && (
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={onClose}
                className="btn btn-secondary"
              >
                取消
              </button>
              <button
                onClick={handleActivate}
                disabled={!selectedFile || isValidating || isBlacklisted}
                className={`btn ${isBlacklisted ? 'btn-secondary' : 'btn-primary'}`}
              >
                {isValidating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                    验证中...
                  </>
                ) : isBlacklisted ? (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    设备已拉黑
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    激活软件
                  </>
                )}
              </button>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="mt-4 p-4 rounded-lg bg-red-50 border border-red-200">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-6 space-y-3">
            <h4 className="text-sm font-medium text-gray-700">激活文件使用说明：</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>激活文件由软件提供商基于您的设备码生成</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>激活文件与您的设备硬件绑定，仅限当前设备使用</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>请确保激活文件完整且未被修改</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>如激活失败，请联系软件提供商重新生成</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileActivationForm;
