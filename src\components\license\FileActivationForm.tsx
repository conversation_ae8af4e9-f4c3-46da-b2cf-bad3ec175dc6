import React, { useState, useCallback, useEffect } from 'react';
import { Upload, X, FileText, CheckCircle, AlertCircle, Shield } from 'lucide-react';
import { activateWithFile, isDeviceBlacklisted, getRemainingAttempts } from '../../utils/auth/licenseValidator';
import { useToast } from '../ui/Toast';
import '../ui/BaseModal.css';

interface FileActivationFormProps {
  onSuccess: () => void;
  onClose: () => void;
}

const FileActivationForm: React.FC<FileActivationFormProps> = ({ onSuccess, onClose }) => {
  const [dragOver, setDragOver] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isBlacklisted, setIsBlacklisted] = useState(false);
  const [remainingAttempts, setRemainingAttempts] = useState(5);
  const toast = useToast();

  // 检查安全状态
  useEffect(() => {
    const checkSecurityState = async () => {
      try {
        const blacklisted = await isDeviceBlacklisted();
        const attempts = await getRemainingAttempts();
        setIsBlacklisted(blacklisted);
        setRemainingAttempts(attempts);

        if (blacklisted) {
          setError('设备已被拉黑，无法进行激活操作');
        }
      } catch (error) {
        console.error('检查安全状态失败:', error);
      }
    };

    checkSecurityState();
  }, []);

  const handleFileSelect = useCallback((file: File) => {
    // 检查文件类型
    if (!file.name.endsWith('.lic') && !file.name.endsWith('.license')) {
      setError('请选择有效的激活文件（.lic 或 .license 格式）');
      return;
    }

    // 检查文件大小（最大1MB）
    if (file.size > 1024 * 1024) {
      setError('激活文件过大，请确认文件是否正确');
      return;
    }

    setSelectedFile(file);
    setError('');
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleClick = useCallback(() => {
    if (isValidating) return;
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.lic,.license';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    };
    input.click();
  }, [isValidating, handleFileSelect]);

  const handleRemoveFile = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFile(null);
    setError('');
  }, []);

  const handleActivate = async () => {
    if (!selectedFile) {
      setError('请先选择激活文件');
      toast.warning('请先选择激活文件');
      return;
    }

    // 检查是否被拉黑
    if (isBlacklisted) {
      setError('设备已被拉黑，无法进行激活操作');
      toast.error('设备已被拉黑，无法激活');
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      const success = await activateWithFile(selectedFile);

      if (success) {
        toast.success('激活成功');
        onSuccess();
      } else {
        // 激活失败，更新剩余尝试次数
        const newAttempts = await getRemainingAttempts();
        setRemainingAttempts(newAttempts);

        if (newAttempts === 0) {
          setIsBlacklisted(true);
          setError('激活失败次数过多，设备已被永久拉黑');
          toast.error('设备已被拉黑，无法继续使用');
        } else {
          setError(`激活失败：激活文件无效或与当前设备不匹配。剩余尝试次数：${newAttempts}`);
          toast.error(`激活失败，剩余尝试次数：${newAttempts}`);
        }
      }
    } catch (err) {
      console.error('激活过程出错:', err);
      setError('激活过程出错，请稍后重试');
      toast.error('激活出错，请稍后重试');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="base-modal-container base-modal-md base-modal--glass">
      {/* 头部 */}
      <div className="base-modal-header">
        <div className="base-modal-header-content">
          <div className="base-modal-title-section">
            <h3 className="base-modal-title">软件激活</h3>
            <p className="base-modal-subtitle">请按步骤完成激活</p>
          </div>
        </div>
        <button
          className="base-modal-close"
          onClick={onClose}
          aria-label="关闭激活窗口"
        >
          <X size={20} />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="base-modal-content">
        <div className="p-6 text-white">
          {/* 安全状态提示 */}
          {isBlacklisted ? (
            <div className="mb-4 p-4 rounded-lg glass border border-red-400/30 flex items-center">
              <Shield className="w-5 h-5 text-red-300 mr-3 flex-shrink-0" />
              <span className="text-red-200 text-sm">设备已被拉黑，无法进行激活操作</span>
            </div>
          ) : remainingAttempts < 5 && (
            <div className="mb-4 p-4 rounded-lg glass border border-orange-400/30 flex items-center">
              <Shield className="w-5 h-5 text-orange-300 mr-3 flex-shrink-0" />
              <span className="text-orange-200 text-sm">剩余激活尝试次数：{remainingAttempts}</span>
            </div>
          )}

          {/* 步骤指示器（玻璃风格） */}
          <div className="mb-6 flex items-center justify-center">
            <div className="flex items-center space-x-4 text-white/80">
              <div className={`flex items-center space-x-2 ${!selectedFile ? 'text-blue-300' : 'text-white/40'}`}>
                <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${!selectedFile ? 'glass border border-blue-300/40 text-blue-200' : 'glass border border-white/20 text-white/50'}`}>1</span>
                <span className="text-sm font-medium">选择文件</span>
              </div>
              <div className="w-8 h-px bg-white/20"></div>
              <div className={`flex items-center space-x-2 ${selectedFile ? 'text-green-300' : 'text-white/40'}`}>
                <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${selectedFile ? 'glass border border-green-300/40 text-green-200' : 'glass border border-white/20 text-white/50'}`}>2</span>
                <span className="text-sm font-medium">激活软件</span>
              </div>
            </div>
          </div>

          {/* 文件上传区域（玻璃风格） */}
          <div
            className={`rounded-xl p-8 transition-all duration-200 cursor-pointer text-center glass border ${
              dragOver
                ? 'border-blue-300/40'
                : selectedFile
                  ? 'border-green-300/40'
                  : 'border-white/20 hover:bg-white/10'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={handleClick}
          >
            {selectedFile ? (
              <div>
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 rounded-lg glass border border-green-300/40 flex items-center justify-center">
                    <FileText className="w-8 h-8 text-green-300" />
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">{selectedFile.name}</h4>
                <p className="text-sm text-white/70 mb-4">
                  文件大小: {(selectedFile.size / 1024).toFixed(1)} KB
                </p>
                <button
                  onClick={handleRemoveFile}
                  className="glass inline-flex items-center px-3 py-2 rounded-lg border border-white/20 hover:bg-white/10 text-white text-sm"
                >
                  <X className="w-4 h-4 mr-1" />
                  移除文件
                </button>
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 rounded-lg glass border border-blue-300/40 flex items-center justify-center">
                    <Upload className="w-8 h-8 text-blue-300" />
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">选择激活文件</h4>
                <p className="text-sm text-white/70 mb-4">
                  点击选择或拖拽激活文件到此处
                </p>
                <p className="text-xs text-white/60">
                  支持 .lic 和 .license 格式文件
                </p>
              </div>
            )}
          </div>
          {selectedFile && (
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={onClose}
                className="glass inline-flex items-center px-4 py-2 rounded-lg border border-white/20 hover:bg-white/10 text-white text-sm"
              >
                取消
              </button>
              <button
                onClick={handleActivate}
                disabled={!selectedFile || isValidating || isBlacklisted}
                className={`glass inline-flex items-center px-5 py-2 rounded-lg border text-white text-sm ${isBlacklisted ? 'border-white/20 opacity-60 cursor-not-allowed' : 'border-green-300/40 hover:bg-white/10'}`}
              >
                {isValidating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                    验证中...
                  </>
                ) : isBlacklisted ? (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    设备已拉黑
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    激活软件
                  </>
                )}
              </button>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="mt-4 p-4 rounded-lg bg-red-50 border border-red-200">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-6 space-y-3">
            <h4 className="text-sm font-medium text-gray-700">激活文件使用说明：</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>激活文件由软件提供商基于您的设备码生成</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>激活文件与您的设备硬件绑定，仅限当前设备使用</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>请确保激活文件完整且未被修改</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>如激活失败，请联系软件提供商重新生成</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileActivationForm;
