import React, { useState, useCallback } from 'react';
import { Upload, X, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { activateWithFile } from '../../utils/auth/licenseValidator';
import { useToast } from '../ui/Toast';
import CollapsibleHelp from '../ui/CollapsibleHelp';

interface FileActivationFormProps {
  onSuccess: () => void;
  onClose: () => void;
}

const FileActivationForm: React.FC<FileActivationFormProps> = ({ onSuccess, onClose }) => {
  const [dragOver, setDragOver] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const toast = useToast();

  const handleFileSelect = useCallback((file: File) => {
    // 检查文件类型
    if (!file.name.endsWith('.lic') && !file.name.endsWith('.license')) {
      setError('请选择有效的激活文件（.lic 或 .license 格式）');
      return;
    }

    // 检查文件大小（最大1MB）
    if (file.size > 1024 * 1024) {
      setError('激活文件过大，请确认文件是否正确');
      return;
    }

    setSelectedFile(file);
    setError('');
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleClick = useCallback(() => {
    if (isValidating) return;
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.lic,.license';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    };
    input.click();
  }, [isValidating, handleFileSelect]);

  const handleRemoveFile = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFile(null);
    setError('');
  }, []);

  const handleActivate = async () => {
    if (!selectedFile) {
      setError('请先选择激活文件');
      toast.warning('请先选择激活文件');
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      const success = await activateWithFile(selectedFile);
      
      if (success) {
        toast.success('激活成功');
        onSuccess();
      } else {
        setError('激活失败：激活文件无效或与当前设备不匹配');
        toast.error('激活失败：文件无效或设备不匹配');
      }
    } catch (err) {
      console.error('激活过程出错:', err);
      setError('激活过程出错，请稍后重试');
      toast.error('激活出错，请稍后重试');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="device-code-modal card-border overflow-hidden rounded-2xl animate-modal-enter shadow-2xl">
      {/* 头部 */}
      <div className="px-8 py-6 border-b border-white/10">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-xl glass border border-green-400/30 flex items-center justify-center mr-4 relative overflow-hidden">
              <Upload className="w-6 h-6 text-green-400" />
              <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-xl"></div>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white mb-1">软件激活</h3>
              <p className="text-sm text-white/60">License Activation</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="group relative flex items-center justify-center w-8 h-8 rounded-lg bg-red-500/10 border border-red-400/30 hover:bg-red-500/20 hover:border-red-400/50 transition-all duration-200 text-red-300 hover:text-red-200"
            title="关闭激活窗口"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <p className="text-white/70 text-sm leading-relaxed mt-4">请按步骤完成激活</p>
        {/* 步骤条 */}
        <div className="mt-4 flex items-center text-xs font-medium tracking-wide select-none">
          <div className={`flex items-center ${!selectedFile ? 'text-blue-300' : 'text-white/50'}`}>
            <span className={`w-5 h-5 mr-2 rounded-full flex items-center justify-center text-[10px] font-bold ${!selectedFile ? 'bg-blue-500 text-white' : 'bg-white/10'}`}>1</span>
            选择文件
          </div>
          <div className="flex-1 h-px mx-4 bg-gradient-to-r from-white/20 to-white/5" />
          <div className={`flex items-center ${selectedFile ? 'text-green-300' : 'text-white/40'}`}>
            <span className={`w-5 h-5 mr-2 rounded-full flex items-center justify-center text-[10px] font-bold ${selectedFile ? 'bg-green-500 text-white' : 'bg-white/10'}`}>2</span>
            激活软件
          </div>
        </div>
      </div>

      {/* 文件上传 + 操作区域 */}
      <div className="px-8 py-6">
        <div 
          className={`upload-area border-2 border-dashed rounded-xl p-6 transition-all duration-200 cursor-pointer relative ${
            dragOver 
              ? 'border-blue-400/50 bg-blue-400/10' 
              : selectedFile 
                ? 'border-green-400/50 bg-green-400/5' 
                : 'border-white/20 bg-white/5 hover:border-white/30 hover:bg-white/10'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={handleClick}
        >
          {selectedFile ? (
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 rounded-xl glass border border-green-400/30 flex items-center justify-center relative overflow-hidden">
                  <FileText className="w-8 h-8 text-green-400" />
                  <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-xl"></div>
                </div>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">{selectedFile.name}</h4>
              <p className="text-sm text-white/60 mb-4">
                文件大小: {(selectedFile.size / 1024).toFixed(1)} KB
              </p>
              <button
                onClick={handleRemoveFile}
                className="inline-flex items-center px-4 py-2 text-sm text-red-300 hover:text-red-200 transition-colors"
              >
                <X className="w-4 h-4 mr-2" />
                移除文件
              </button>
            </div>
          ) : (
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 rounded-xl glass border border-blue-400/30 flex items-center justify-center relative overflow-hidden">
                  <Upload className="w-8 h-8 text-blue-400" />
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-xl"></div>
                </div>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">选择激活文件</h4>
              <p className="text-sm text-white/60 mb-4">
                点击选择或拖拽激活文件到此处
              </p>
              <p className="text-xs text-white/40">
                支持 .lic 和 .license 格式文件
              </p>
            </div>
          )}
        </div>
        {selectedFile && (
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleActivate}
              disabled={!selectedFile || isValidating}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500/50"
            >
              {isValidating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  验证中...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  激活软件
                </>
              )}
            </button>
          </div>
        )}
        {/* 错误信息 */}
        {error && (
          <div className="mt-4 p-4 glass rounded-xl border border-red-400/20 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-red-600/5"></div>
            <div className="flex items-start space-x-3 relative z-10">
              <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-300">
                {error}
              </div>
            </div>
          </div>
        )}
        <div className="mt-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 glass border border-white/20 rounded-lg hover:bg-white/10 transition-all duration-200 text-sm text-white/70 hover:text-white"
          >取消</button>
        </div>
        {/* 折叠说明 */}
        <div className="mt-4">
          <CollapsibleHelp
            title="激活文件使用说明"
            defaultOpen={false}
            items={[
              '激活文件由软件提供商基于您的设备码生成',
              '激活文件与您的设备硬件绑定，仅限当前设备使用',
              '请确保激活文件完整且未被修改',
              '如激活失败，请联系软件提供商重新生成'
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default FileActivationForm;
