.app-toast-container {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: var(--z-toast, 1080);
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
  max-width: 360px;
}

.app-toast {
  position: relative;
  backdrop-filter: blur(12px);
  background: linear-gradient(135deg, rgba(30,41,59,0.85), rgba(51,65,85,0.85));
  border: 1px solid rgba(255,255,255,0.12);
  border-radius: 14px;
  padding: 10px 14px 10px 16px;
  color: #fff;
  font-size: 13px;
  line-height: 1.4;
  box-shadow: 0 4px 18px -4px rgba(0,0,0,0.35);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  animation: toast-enter 220ms cubic-bezier(.4,0,.2,1);
}

.app-toast + .app-toast { margin-top: 4px; }

.app-toast-bar {
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 4px;
  background: var(--info);
  border-top-left-radius: 14px;
  border-bottom-left-radius: 14px;
}

.app-toast-success .app-toast-bar { background: var(--success); }
.app-toast-error .app-toast-bar { background: var(--error); }
.app-toast-warning .app-toast-bar { background: var(--warning); }
.app-toast-info .app-toast-bar { background: var(--info); }

.app-toast-success { border-color: rgba(16,185,129,0.35); }
.app-toast-error { border-color: rgba(239,68,68,0.35); }
.app-toast-warning { border-color: rgba(245,158,11,0.35); }
.app-toast-info { border-color: rgba(59,130,246,0.35); }

.app-toast-msg { flex: 1; font-weight: 500; letter-spacing: .3px; }

.app-toast-icon { display:inline-flex; width:16px; height:16px; opacity:.9; }

.app-toast-close {
  background: transparent;
  border: 0;
  color: #fff;
  opacity: .5;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 4px;
  transition: var(--transition-fast);
}

.app-toast-close:hover { opacity: .95; background: rgba(255,255,255,0.12); }

.app-toast-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 3px;
  background: linear-gradient(90deg, rgba(255,255,255,.4), rgba(255,255,255,.9));
  width: 100%;
  animation: toast-progress linear forwards;
}

@keyframes toast-enter {
  from { opacity: 0; transform: translateY(-6px) scale(.96); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes toast-progress {
  from { transform: scaleX(1); transform-origin: left; }
  to { transform: scaleX(0); transform-origin: left; }
}
