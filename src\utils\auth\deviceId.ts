/**
 * 设备码生成工具类 - 与 loin 同步
 */

interface HardwareInfo {
  c: string; // CPU信息
  b: string; // 主板信息（使用屏幕信息替代）
  v: string; // 系统卷信息（使用时区+语言替代）
  g: string; // 安装GUID
}

class DeviceIdGenerator {
  private static STORAGE_KEY = 'device_install_guid';

  private static getCpu(): string {
    try {
      const cores = navigator.hardwareConcurrency || 4;
      const platform = navigator.platform || 'unknown';
      const memory = (navigator as any).deviceMemory || 'unknown';
      return `${platform}-${cores}cores-${memory}GB`;
    } catch {
      return 'NA';
    }
  }

  private static getBoard(): string {
    try {
      const screen = window.screen;
      return `${screen.width}x${screen.height}-${screen.colorDepth}bit`;
    } catch {
      return 'NA';
    }
  }

  private static getSysVol(): string {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown';
      const language = navigator.language || 'unknown';
      return `${timezone}-${language}`;
    } catch {
      return 'NA';
    }
  }

  private static loadOrCreateInstallGuid(): string {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) return stored;
    const guid = this.generateUUID();
    localStorage.setItem(this.STORAGE_KEY, guid);
    return guid;
  }

  private static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  private static collectHardwareInfo(): HardwareInfo {
    return {
      c: this.getCpu(),
      b: this.getBoard(),
      v: this.getSysVol(),
      g: this.loadOrCreateInstallGuid(),
    };
  }

  private static async sha256(data: string): Promise<ArrayBuffer> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    return await crypto.subtle.digest('SHA-256', dataBuffer);
  }

  private static toBase32Crockford(buffer: ArrayBuffer): string {
    const ALPH = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    const bytes = new Uint8Array(buffer);
    let bits = 0,
      value = 0,
      out = '';
    for (const b of bytes) {
      value = (value << 8) | b;
      bits += 8;
      while (bits >= 5) {
        out += ALPH[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }
    if (bits > 0) out += ALPH[(value << (5 - bits)) & 31];
    return out;
  }

  public static async computeDeviceCode(): Promise<{ deviceCode: string; canonical: string }> {
    try {
      const payload = this.collectHardwareInfo();
      const jsonString = JSON.stringify(payload);
      const hash = await this.sha256(jsonString);
      const first17 = hash.slice(0, 17);
      const b32 = this.toBase32Crockford(first17);
      const canonical = b32.substring(0, 25);
      const deviceCode = canonical.match(/.{1,5}/g)!.join('-');
      return { deviceCode, canonical };
    } catch (error) {
      console.error('DeviceIdGenerator: Error computing device code:', error);
      const fallbackId = this.generateFallbackId();
      return { deviceCode: fallbackId, canonical: fallbackId.replace(/-/g, '') };
    }
  }

  public static generateDeviceId(): string {
    return this.generateFallbackId();
  }

  private static formatDeviceId(base32: string): string {
    const cleaned = base32.substring(0, 25).padEnd(25, '0');
    return cleaned.match(/.{1,5}/g)?.join('-') || cleaned;
  }

  private static generateFallbackId(): string {
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substring(2, 7).toUpperCase();
    const fallback = `${timestamp}${random}`.substring(0, 25).padEnd(25, '0');
    return this.formatDeviceId(fallback);
  }

  public static validateDeviceId(deviceId: string): boolean {
    const pattern = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
    return pattern.test(deviceId);
  }

  public static getDeviceInfo(): HardwareInfo {
    return this.collectHardwareInfo();
  }

  public static async getDeviceCode(): Promise<{ deviceCode: string; canonical: string }> {
    return await this.computeDeviceCode();
  }
}

export default DeviceIdGenerator;
export type { HardwareInfo };

