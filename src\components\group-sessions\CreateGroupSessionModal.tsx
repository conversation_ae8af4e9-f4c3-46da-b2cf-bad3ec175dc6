import React, { useState, useEffect } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea, Button } from '../ui';
import { Users, Calendar, MapPin, Clock, User } from 'lucide-react';
import { visitorService } from '../../services/visitorService';
import type { GroupSession, CreateGroupSessionForm } from '../../types/groupSession';
import type { Visitor } from '../../types/visitor';

interface CreateGroupSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (sessionData: Omit<GroupSession, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  session?: GroupSession | null;
  mode: 'create' | 'edit';
}

const CreateGroupSessionModal: React.FC<CreateGroupSessionModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  session,
  mode
}) => {
  const [formData, setFormData] = useState<CreateGroupSessionForm>({
    title: '',
    description: '',
    therapistName: '',
    maxParticipants: 8,
    sessionType: '',
    targetAge: '',
    duration: 90,
    frequency: '',
    totalSessions: 1,
    startDate: '',
    endDate: '',
    meetingTime: '',
    location: '',
    requirements: '',
    materials: [],
    selectedParticipants: [],
    notes: ''
  });

  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 加载来访者数据
  useEffect(() => {
    if (isOpen) {
      loadVisitors();
      if (mode === 'edit' && session) {
        populateFormData(session);
      } else {
        resetForm();
      }
    }
  }, [isOpen, mode, session]);

  const loadVisitors = async () => {
    try {
      const allVisitors = await visitorService.getAllVisitors();
      setVisitors(allVisitors.filter(v => v.status === '活跃'));
    } catch (error) {
      console.error('加载来访者失败:', error);
    }
  };

  const populateFormData = (sessionData: GroupSession) => {
    setFormData({
      title: sessionData.title,
      description: sessionData.description || '',
      therapistName: sessionData.therapistName,
      maxParticipants: sessionData.maxParticipants,
      sessionType: sessionData.sessionType,
      targetAge: sessionData.targetAge,
      duration: sessionData.duration,
      frequency: sessionData.frequency,
      totalSessions: sessionData.totalSessions || 1,
      startDate: sessionData.startDate,
      endDate: sessionData.endDate || '',
      meetingTime: sessionData.meetingTime,
      location: sessionData.location,
      requirements: sessionData.requirements || '',
      materials: sessionData.materials || [],
      selectedParticipants: sessionData.participants.map(p => p.visitorId),
      notes: sessionData.notes || ''
    });
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      therapistName: '',
      maxParticipants: 8,
      sessionType: '',
      targetAge: '',
      duration: 90,
      frequency: '',
      totalSessions: 1,
      startDate: '',
      endDate: '',
      meetingTime: '',
      location: '',
      requirements: '',
      materials: [],
      selectedParticipants: [],
      notes: ''
    });
    setErrors({});
  };

  const handleInputChange = (field: keyof CreateGroupSessionForm, value: string | number | string[] | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入团体名称';
    }

    if (!formData.therapistName.trim()) {
      newErrors.therapistName = '请输入治疗师姓名';
    }

    if (!formData.sessionType) {
      newErrors.sessionType = '请选择团体类型';
    }

    if (!formData.targetAge) {
      newErrors.targetAge = '请选择目标年龄群体';
    }

    if (!formData.frequency) {
      newErrors.frequency = '请选择活动频率';
    }

    if (!formData.startDate) {
      newErrors.startDate = '请选择开始日期';
    }

    if (!formData.meetingTime) {
      newErrors.meetingTime = '请输入会议时间';
    }

    if (!formData.location.trim()) {
      newErrors.location = '请输入活动地点';
    }

    if (formData.maxParticipants < 1 || formData.maxParticipants > 20) {
      newErrors.maxParticipants = '参与人数应在1-20之间';
    }

    if (formData.duration < 30 || formData.duration > 300) {
      newErrors.duration = '活动时长应在30-300分钟之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 构建参与者列表
      const participants = formData.selectedParticipants.map(visitorId => {
        const visitor = visitors.find(v => v.id === visitorId);
        if (!visitor) throw new Error(`找不到来访者: ${visitorId}`);
        
        return {
          id: `participant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          visitorId: visitor.id,
          visitorName: visitor.name,
          age: visitor.age,
          gender: visitor.gender,
          joinDate: new Date().toISOString(),
          status: '进行中' as const,
          attendanceRate: 0
        };
      });

      const sessionData: Omit<GroupSession, 'id' | 'createdAt' | 'updatedAt'> = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        therapistId: `therapist-${Date.now()}`, // 临时ID，后续可以改为实际的治疗师ID系统
        therapistName: formData.therapistName.trim(),
        maxParticipants: formData.maxParticipants,
        currentParticipants: participants.length,
        participants,
        sessionType: formData.sessionType,
        targetAge: formData.targetAge,
        duration: formData.duration,
        frequency: formData.frequency,
        totalSessions: formData.totalSessions,
        currentSession: 0,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        meetingTime: formData.meetingTime,
        location: formData.location.trim(),
        status: '计划中',
        requirements: formData.requirements.trim() || undefined,
        materials: formData.materials.length > 0 ? formData.materials : undefined,
        notes: formData.notes.trim() || undefined
      };

      await onSubmit(sessionData);
      onClose();
    } catch (error) {
      console.error('提交失败:', error);
      alert('提交失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleParticipantToggle = (visitorId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedParticipants: prev.selectedParticipants.includes(visitorId)
        ? prev.selectedParticipants.filter(id => id !== visitorId)
        : [...prev.selectedParticipants, visitorId]
    }));
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? '创建团沙' : '编辑团体活动'}
      subtitle={mode === 'create' ? '创建新的团体沙盘治疗活动' : '修改团体活动信息'}
      onSubmit={handleSubmit}
      submitText={mode === 'create' ? '创建' : '保存'}
      isSubmitting={isSubmitting}
      size="xl"
    >
      <div className="form-modal-body">
        {/* 基本信息 */}
        <div className="form-section">
          <h4 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1f2937', 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '16px'
          }}>
            <Users size={16} />
            基本信息
          </h4>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Input
              label="团体名称"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="请输入团体名称"
              error={errors.title}
              required
            />
            
            <Input
              label="治疗师姓名"
              value={formData.therapistName}
              onChange={(e) => handleInputChange('therapistName', e.target.value)}
              placeholder="请输入治疗师姓名"
              error={errors.therapistName}
              required
              leftIcon={<User size={16} />}
            />
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <Textarea
              label="团体描述"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="请输入团体活动的详细描述（可选）"
              rows={3}
            />
          </div>
        </div>

        {/* 团体设置 */}
        <div className="form-section">
          <h4 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1f2937', 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '16px'
          }}>
            <Users size={16} />
            团体设置
          </h4>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))', 
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Select
              label="团体类型"
              value={formData.sessionType}
              onChange={(e) => handleInputChange('sessionType', e.target.value)}
              options={[
                { value: '', label: '请选择团体类型' },
                { value: '开放式团体', label: '开放式团体' },
                { value: '封闭式团体', label: '封闭式团体' },
                { value: '主题团体', label: '主题团体' },
                { value: '发展性团体', label: '发展性团体' }
              ]}
              error={errors.sessionType}
              required
            />
            
            <Select
              label="目标年龄群体"
              value={formData.targetAge}
              onChange={(e) => handleInputChange('targetAge', e.target.value)}
              options={[
                { value: '', label: '请选择年龄群体' },
                { value: '儿童', label: '儿童' },
                { value: '青少年', label: '青少年' },
                { value: '成人', label: '成人' },
                { value: '老年', label: '老年' },
                { value: '混合', label: '混合' }
              ]}
              error={errors.targetAge}
              required
            />
            
            <Input
              label="最大参与人数"
              type="number"
              value={formData.maxParticipants}
              onChange={(e) => handleInputChange('maxParticipants', parseInt(e.target.value) || 0)}
              placeholder="8"
              min={1}
              max={20}
              error={errors.maxParticipants}
              required
            />
          </div>
        </div>

        {/* 时间安排 */}
        <div className="form-section">
          <h4 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1f2937', 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '16px'
          }}>
            <Calendar size={16} />
            时间安排
          </h4>

          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Input
              label="活动时长（分钟）"
              type="number"
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 0)}
              placeholder="90"
              min={30}
              max={300}
              error={errors.duration}
              required
              leftIcon={<Clock size={16} />}
            />

            <Select
              label="活动频率"
              value={formData.frequency}
              onChange={(e) => handleInputChange('frequency', e.target.value)}
              options={[
                { value: '', label: '请选择频率' },
                { value: '单次', label: '单次活动' },
                { value: '每周', label: '每周一次' },
                { value: '每两周', label: '每两周一次' },
                { value: '每月', label: '每月一次' }
              ]}
              error={errors.frequency}
              required
            />

            <Input
              label="总次数"
              type="number"
              value={formData.totalSessions}
              onChange={(e) => handleInputChange('totalSessions', parseInt(e.target.value) || 1)}
              placeholder="1"
              min={1}
              max={50}
            />
          </div>

          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Input
              label="开始日期"
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              error={errors.startDate}
              required
            />

            <Input
              label="结束日期"
              type="date"
              value={formData.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
              placeholder="可选"
            />

            <Input
              label="会议时间"
              type="time"
              value={formData.meetingTime}
              onChange={(e) => handleInputChange('meetingTime', e.target.value)}
              error={errors.meetingTime}
              required
            />
          </div>
        </div>

        {/* 地点和要求 */}
        <div className="form-section">
          <h4 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1f2937', 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '16px'
          }}>
            <MapPin size={16} />
            地点和要求
          </h4>

          <div style={{ marginBottom: '16px' }}>
            <Input
              label="活动地点"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="请输入活动地点"
              error={errors.location}
              required
              leftIcon={<MapPin size={16} />}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <Textarea
              label="参与要求"
              value={formData.requirements}
              onChange={(e) => handleInputChange('requirements', e.target.value)}
              placeholder="请输入参与者的要求和条件（可选）"
              rows={2}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <Textarea
              label="备注"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="请输入其他备注信息（可选）"
              rows={2}
            />
          </div>
        </div>

        {/* 参与者选择 */}
        <div className="form-section">
          <h4 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1f2937', 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '16px'
          }}>
            <Users size={16} />
            选择参与者 ({formData.selectedParticipants.length}/{formData.maxParticipants})
          </h4>

          <div style={{ 
            maxHeight: '256px', 
            overflowY: 'auto', 
            border: '1px solid #e5e7eb', 
            borderRadius: '8px', 
            padding: '16px' 
          }}>
            {visitors.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                color: '#6b7280', 
                padding: '16px 0' 
              }}>
                暂无可选择的来访者
              </div>
            ) : (
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
                gap: '12px' 
              }}>
                {visitors.map(visitor => (
                  <div
                    key={visitor.id}
                    style={{
                      padding: '12px',
                      border: '1px solid',
                      borderColor: formData.selectedParticipants.includes(visitor.id) ? '#3b82f6' : '#e5e7eb',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      background: formData.selectedParticipants.includes(visitor.id) ? '#dbeafe' : 'white',
                      transition: 'all 0.2s'
                    }}
                    onClick={() => handleParticipantToggle(visitor.id)}
                  >
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between' 
                    }}>
                      <div>
                        <div style={{ fontWeight: '500' }}>{visitor.name}</div>
                        <div style={{ 
                          fontSize: '14px', 
                          color: '#6b7280' 
                        }}>
                          {visitor.gender} · {visitor.age}岁
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={formData.selectedParticipants.includes(visitor.id)}
                        onChange={() => handleParticipantToggle(visitor.id)}
                        style={{ width: '16px', height: '16px' }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {formData.selectedParticipants.length > formData.maxParticipants && (
            <div style={{ 
              color: '#dc2626', 
              fontSize: '14px', 
              marginTop: '8px' 
            }}>
              选择的参与者数量超过了最大限制
            </div>
          )}
        </div>
      </div>
    </FormModal>
  );
};

export default CreateGroupSessionModal;
