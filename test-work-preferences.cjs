// 测试工作偏好设置功能
const { settingsService } = require('./dist-electron/services/databaseSettingsService');
const { scheduleService } = require('./dist-electron/services/scheduleService');

async function testWorkPreferences() {
  console.log('=== 测试工作偏好设置功能 ===');
  
  try {
    // 1. 测试获取设置
    console.log('1. 获取工作偏好设置...');
    const settings = await settingsService.getSettings();
    console.log('默认咨询时长:', settings.user.preferences.defaultSessionDuration);
    console.log('工作时间段:', settings.user.preferences.workingHours);
    
    // 2. 测试修改设置
    console.log('\n2. 修改工作偏好设置...');
    await settingsService.updateSetting('user.preferences.defaultSessionDuration', 45);
    await settingsService.updateSetting('user.preferences.workingHours', { start: '08:30', end: '17:30' });
    
    // 3. 验证修改后的设置
    console.log('\n3. 验证修改后的设置...');
    const updatedSettings = await settingsService.getSettings();
    console.log('修改后的咨询时长:', updatedSettings.user.preferences.defaultSessionDuration);
    console.log('修改后的工作时间段:', updatedSettings.user.preferences.workingHours);
    
    // 4. 测试日程服务使用工作偏好
    console.log('\n4. 测试日程服务使用工作偏好...');
    const availableSlots = await scheduleService.getAvailableTimeSlots('2024-01-01', 'therapist-1', 60);
    console.log('可用时间段数量:', availableSlots.length);
    if (availableSlots.length > 0) {
      console.log('第一个时间段:', availableSlots[0]);
    }
    
    console.log('\n✅ 工作偏好设置功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testWorkPreferences();