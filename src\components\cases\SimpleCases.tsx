import React, { useState, useEffect } from 'react';
import { User, Search, Plus, Eye, Edit, Trash2 } from 'lucide-react';
import { caseService } from '../../services';
import type { SimpleCase } from '../../types/case';
import { EmptyState } from '../ui';
import SimpleCreateCaseModal from './SimpleCreateCaseModal';
import SimpleEditCaseModal from './SimpleEditCaseModal';
import SimpleCaseDetailModal from './SimpleCaseDetailModal';
import './SimpleCases.css';

const SimpleCases: React.FC = () => {
  const [cases, setCases] = useState<Case[]>([]);
  const [filteredCases, setFilteredCases] = useState<Case[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [loading, setLoading] = useState(true);
  
  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedCase, setSelectedCase] = useState<Case | null>(null);

  useEffect(() => {
    loadCases();
  }, []);

  useEffect(() => {
    filterCases();
  }, [cases, searchTerm, statusFilter, filterCases]);

  const loadCases = async () => {
    try {
      setLoading(true);
      const casesData = await caseService.getAllCases();
      setCases(casesData);
    } catch (error) {
      console.error('加载个案失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterCases = () => {
    let filtered = cases;

    if (searchTerm) {
      filtered = filtered.filter(c => 
        c.visitorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.problem.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(c => c.status === statusFilter);
    }

    setFilteredCases(filtered);
  };

  const handleCreateCase = () => {
    setShowCreateModal(true);
  };

  const handleViewCase = (caseItem: Case) => {
    setSelectedCase(caseItem);
    setShowDetailModal(true);
  };

  const handleEditCase = (caseItem: Case) => {
    setSelectedCase(caseItem);
    setShowEditModal(true);
  };

  const handleDeleteCase = async (caseId: string) => {
    if (confirm('确定要删除这个个案吗？')) {
      try {
        await caseService.deleteCase(caseId);
        await loadCases();
      } catch (error) {
        console.error('删除个案失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  const handleCaseCreated = () => {
    setShowCreateModal(false);
    loadCases();
  };

  const handleCaseUpdated = () => {
    setShowEditModal(false);
    loadCases();
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      'active': { text: '进行中', class: 'status-attention' },
      'completed': { text: '已结束', class: 'status-safe' },
      'paused': { text: '暂停', class: 'status-danger' }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, class: 'status-safe' };
    
    return (
      <span className={`status-badge ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="simple-case-list">
        <div style={{ textAlign: 'center', padding: '40px', color: '#6b7280' }}>
          加载中...
        </div>
      </div>
    );
  }

  return (
    <div className="simple-case-list">
      <div className="simple-case-header">
        <h1>个案管理</h1>
        <button 
          className="simple-btn primary"
          onClick={handleCreateCase}
        >
          <Plus size={16} />
          新建个案
        </button>
      </div>

      <div className="simple-search-bar">
        <div style={{ position: 'relative', flex: 1 }}>
          <Search size={16} style={{ 
            position: 'absolute', 
            left: '12px', 
            top: '50%', 
            transform: 'translateY(-50%)', 
            color: '#9ca3af' 
          }} />
          <input
            type="text"
            placeholder="搜索来访者姓名或问题描述..."
            className="simple-search-input"
            style={{ paddingLeft: '36px' }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          className="simple-filter-select"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="">全部状态</option>
          <option value="active">进行中</option>
          <option value="completed">已结束</option>
          <option value="paused">暂停</option>
        </select>
      </div>

      {filteredCases.length === 0 ? (
        <EmptyState
          icon={<User size={48} />}
          title="暂无个案数据"
          description="还没有任何个案信息，点击上方按钮创建您的第一个个案吧。"
          action={
            <button 
              className="simple-btn primary"
              onClick={handleCreateCase}
            >
              创建第一个个案
            </button>
          }
        />
      ) : (
        <div className="simple-case-grid">
          {filteredCases.map((caseItem) => (
            <div key={caseItem.id} className="simple-case-card">
              <div className="case-name">
                <User size={16} />
                {caseItem.visitorName}
              </div>
              
              <div className="case-summary">
                {caseItem.problem}
              </div>
              
              <div className="case-meta">
                <div className="case-status">
                  {getStatusBadge(caseItem.status)}
                </div>
                <div>
                  {new Date(caseItem.createdAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="case-actions">
                <button 
                  className="simple-btn"
                  onClick={() => handleViewCase(caseItem)}
                >
                  <Eye size={14} />
                  查看
                </button>
                <button 
                  className="simple-btn"
                  onClick={() => handleEditCase(caseItem)}
                >
                  <Edit size={14} />
                  编辑
                </button>
                <button 
                  className="simple-btn"
                  onClick={() => handleDeleteCase(caseItem.id)}
                  style={{ color: '#dc2626' }}
                >
                  <Trash2 size={14} />
                  删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 模态框 */}
      {showCreateModal && (
        <SimpleCreateCaseModal
          onClose={() => setShowCreateModal(false)}
          onCaseCreated={handleCaseCreated}
        />
      )}

      {showEditModal && selectedCase && (
        <SimpleEditCaseModal
          case={selectedCase}
          onClose={() => setShowEditModal(false)}
          onCaseUpdated={handleCaseUpdated}
        />
      )}

      {showDetailModal && selectedCase && (
        <SimpleCaseDetailModal
          case={selectedCase}
          onClose={() => setShowDetailModal(false)}
          onEdit={() => {
            setShowDetailModal(false);
            setShowEditModal(true);
          }}
        />
      )}
    </div>
  );
};

export default SimpleCases;
