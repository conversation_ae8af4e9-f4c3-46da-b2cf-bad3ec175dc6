import React, { useState, useEffect } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea, ImageUpload } from '../ui';
import { Package, Tag, Palette } from 'lucide-react';
import { sandToolCategories } from '../../data/mockSandTools';
import { electronDataManager } from '../../services/electronDataManager';
import type { SandTool, SandToolCategory, SandToolSize, SandToolCondition } from '../../types/sandtool';
import './EditSandToolModal.css';

interface EditSandToolModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (tool: SandTool) => void;
  tool: SandTool | null;
}

interface FormData {
  name: string;
  category: SandToolCategory;
  subcategory: string;
  description: string;
  material: string;
  size: SandToolSize;
  color: string;
  quantity: number;
  condition: SandToolCondition;
  location: string;
  imageData: string | null;
  tags: string[];
  notes: string;
  isFragile: boolean;
  needsCare: boolean;
}

export const EditSandToolModal: React.FC<EditSandToolModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  tool
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    category: '人物类',
    subcategory: '',
    description: '',
    material: '',
    size: '中型',
    color: '',
    quantity: 1,
    condition: '全新',
    location: '',
    imageData: null,
    tags: [],
    notes: '',
    isFragile: false,
    needsCare: false
  });

  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tagInput, setTagInput] = useState('');

  // 当工具数据变化时，初始化表单
  useEffect(() => {
    if (tool) {
      setFormData({
        name: tool.name,
        category: tool.category,
        subcategory: tool.subcategory || '',
        description: tool.description || '',
        material: tool.material,
        size: tool.size,
        color: tool.color || '',
        quantity: tool.quantity,
        condition: tool.condition,
        location: tool.location,
        imageData: tool.imageData || null,
        tags: tool.tags || [],
        notes: tool.notes || '',
        isFragile: tool.isFragile || false,
        needsCare: tool.needsCare || false
      });
    }
  }, [tool]);

  const handleInputChange = (field: keyof FormData, value: string | number | string[] | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleTagAdd = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({ 
        ...prev, 
        tags: [...prev.tags, tag] 
      }));
    }
    setTagInput('');
  };

  const handleTagRemove = (tag: string) => {
    setFormData(prev => ({ 
      ...prev, 
      tags: prev.tags.filter(t => t !== tag) 
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入沙具名称';
    }

    if (!formData.material.trim()) {
      newErrors.material = '请输入材质';
    }

    if (formData.quantity < 1) {
      newErrors.quantity = '数量必须大于0';
    }

    if (!formData.location.trim()) {
      newErrors.location = '请输入存放位置';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !tool) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const updatedTool: SandTool = {
        ...tool,
        name: formData.name.trim(),
        category: formData.category,
        subcategory: formData.subcategory.trim() || undefined,
        description: formData.description.trim() || undefined,
        material: formData.material.trim(),
        size: formData.size,
        color: formData.color.trim() || undefined,
        quantity: formData.quantity,
        // 调整可用数量：如果总数量减少，可用数量不能超过新的总数量
        available: Math.min(tool.available, formData.quantity),
        condition: formData.condition,
        location: formData.location.trim(),
        imageData: formData.imageData || undefined,
        tags: formData.tags.length > 0 ? formData.tags : undefined,
        notes: formData.notes.trim() || undefined,
        isFragile: formData.isFragile,
        needsCare: formData.needsCare,
        updatedAt: new Date().toISOString()
      };

      // 保存到数据库
      await electronDataManager.saveSandTool(updatedTool);
      onSubmit(updatedTool);
    } catch (error) {
      console.error('更新沙具失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentCategory = sandToolCategories.find(cat => cat.id === formData.category);
  const currentSubcategories = currentCategory?.subcategories || [];

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="编辑沙具"
      subtitle="修改沙具的详细信息"
      size="xl"
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      submitText="保存修改"
      cancelText="取消"
    >
      {/* 基本信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Package size={16} />
          基本信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <Input
            label="沙具名称"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="请输入沙具名称"
            required
            error={errors.name}
          />
          
          <Select
            label="类别"
            value={formData.category}
            onChange={(e) => {
              handleInputChange('category', e.target.value);
              handleInputChange('subcategory', ''); // 重置子类别
            }}
            required
            options={[
              { value: '人物类', label: '人物类' },
              { value: '动物类', label: '动物类' },
              { value: '植物类', label: '植物类' },
              { value: '建筑类', label: '建筑类' },
              { value: '交通类', label: '交通类' },
              { value: '生活类', label: '生活类' },
              { value: '食物类', label: '食物类' },
              { value: '自然物质类', label: '自然物质类' },
              { value: '其它类', label: '其它类' },
              { value: '容大天成原创', label: '容大天成原创' }
            ]}
          />
        </div>

        <div className="form-grid form-grid-2">
          <Select
            label="子类别"
            value={formData.subcategory}
            onChange={(e) => handleInputChange('subcategory', e.target.value)}
            options={[
              { value: '', label: '请选择子类别（可选）' },
              ...currentSubcategories.map((sub: string) => ({ value: sub, label: sub }))
            ]}
          />
          
          <Input
            label="材质"
            value={formData.material}
            onChange={(e) => handleInputChange('material', e.target.value)}
            placeholder="如：塑料、木质、金属等"
            required
            error={errors.material}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="描述"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请描述沙具的外观、特征等"
            rows={3}
          />
        </div>
      </div>

      {/* 规格信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Palette size={16} />
          规格信息
        </h4>
        
        <div className="form-grid form-grid-3">
          <Select
            label="尺寸"
            value={formData.size}
            onChange={(e) => handleInputChange('size', e.target.value)}
            options={[
              { value: '微型', label: '微型 (≤3cm)' },
              { value: '小型', label: '小型 (3-8cm)' },
              { value: '中型', label: '中型 (8-15cm)' },
              { value: '大型', label: '大型 (15-30cm)' },
              { value: '超大型', label: '超大型 (≥30cm)' }
            ]}
          />
          
          <Input
            label="颜色"
            value={formData.color}
            onChange={(e) => handleInputChange('color', e.target.value)}
            placeholder="如：红色、蓝色、彩色等"
          />
          
          <Input
            label="数量"
            type="number"
            value={formData.quantity}
            onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}
            min="1"
            required
            error={errors.quantity}
          />
        </div>

        <div className="form-grid form-grid-2">
          <Select
            label="状况"
            value={formData.condition}
            onChange={(e) => handleInputChange('condition', e.target.value)}
            options={[
              { value: '全新', label: '全新' },
              { value: '良好', label: '良好' },
              { value: '一般', label: '一般' },
              { value: '损坏', label: '损坏' },
              { value: '报废', label: '报废' }
            ]}
          />
          
          <Input
            label="存放位置"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="如：A区第1层、B柜第3格等"
            required
            error={errors.location}
          />
        </div>
      </div>

      {/* 标签和备注 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Tag size={16} />
          标签和备注
        </h4>
        
        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="form-label">标签</label>
            <div className="tag-input-container">
              <div className="tag-input-row">
                <Input
                  className="tag-input-field"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="输入标签后按回车添加"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleTagAdd(tagInput);
                    }
                  }}
                />
                <button
                  type="button"
                  className="tag-add-button"
                  onClick={() => handleTagAdd(tagInput)}
                >
                  添加
                </button>
              </div>
              
              {/* 已选择的标签 */}
              {formData.tags.length > 0 && (
                <div className="selected-tags">
                  {formData.tags.map((tag, index) => (
                    <span key={index} className="tag-item">
                      {tag}
                      <button
                        type="button"
                        className="tag-remove-btn"
                        onClick={() => handleTagRemove(tag)}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="form-label">沙具图片</label>
            <ImageUpload
              value={formData.imageData || undefined}
              onChange={(imageData) => handleInputChange('imageData', imageData)}
              placeholder="点击选择或拖拽沙具图片"
              maxSize={5}
            />
          </div>
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="备注"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="其他需要说明的信息"
            rows={3}
          />
        </div>
      </div>

      {/* 特殊标记 */}
      <div className="form-section">
        <h4 className="form-section-title">特殊标记</h4>
        
        <div className="form-grid form-grid-2">
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.isFragile}
                onChange={(e) => handleInputChange('isFragile', e.target.checked)}
              />
              <span className="checkmark"></span>
              易碎物品
            </label>
          </div>
          
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.needsCare}
                onChange={(e) => handleInputChange('needsCare', e.target.checked)}
              />
              <span className="checkmark"></span>
              需要特殊保养
            </label>
          </div>
        </div>

        {/* 库存信息显示 */}
        {tool && (
          <div className="form-grid form-grid-1">
            <div className="form-group">
              <div style={{ 
                padding: '12px', 
                backgroundColor: '#f8fafc', 
                borderRadius: '6px', 
                fontSize: '14px',
                color: '#64748b'
              }}>
                <strong>库存信息：</strong>
                总数量 {tool.quantity} 件，当前可用 {tool.available} 件
                {tool.usageCount && ` • 累计使用 ${tool.usageCount} 次`}
                {tool.lastUsed && ` • 最后使用: ${tool.lastUsed}`}
              </div>
            </div>
          </div>
        )}
      </div>
    </FormModal>
  );
};

export default EditSandToolModal;
