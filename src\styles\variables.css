/* 设计系统变量 */
:root {
  /* 主色调 - 更柔和的蓝色系 */
  --primary-blue: #3B82F6;
  --primary-blue-light: #60A5FA;
  --primary-blue-dark: #2563EB;
  --primary-blue-darker: #1D4ED8;
  
  --secondary-teal: #2DD4BF;
  --secondary-teal-light: #5EEAD4;
  --secondary-teal-dark: #0D9488;
  
  --accent-amber: #F59E0B;
  --accent-amber-light: #FCD34D;
  --accent-amber-dark: #D97706;
  
  --accent-purple: #8B5CF6;
  --accent-purple-light: #A78BFA;
  --accent-purple-dark: #7C3AED;
  
  /* 中性色 */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 语义化颜色 */
  --success: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;
  --success-green: #10b981;
  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;
  --warning-orange: #f59e0b;
  --error: #ef4444;
  --error-light: #f87171;
  --error-dark: #dc2626;
  --info: #3b82f6;
  --info-light: #60a5fa;
  --info-dark: #2563eb;
  --info-blue: #3b82f6;
  
  /* 背景色 - 更温和的背景 */
  --bg-primary: var(--white);
  --bg-secondary: #FAFBFC;
  --bg-tertiary: var(--gray-100);
  --bg-overlay: rgba(0, 0, 0, 0.4);
  
  /* 文字颜色 */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  --text-inverse: var(--white);
  
  /* 边框颜色 */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);
  
  /* 阴影 - 更轻微的阴影效果 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.03);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.04);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06), 0 1px 2px -1px rgb(0 0 0 / 0.06);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.06), 0 2px 4px -2px rgb(0 0 0 / 0.06);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.08), 0 4px 6px -4px rgb(0 0 0 / 0.08);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.08), 0 8px 10px -6px rgb(0 0 0 / 0.08);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.15);
  
  /* 圆角 */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.625rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-2xl: 2rem;
  --radius-full: 9999px;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.25rem;
  --spacing-2xl: 1.5rem;
  --spacing-3xl: 2rem;
  --spacing-4xl: 2.5rem;
  --spacing-5xl: 3rem;
  --spacing-6xl: 4rem;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  
  /* 字体粗细 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
