import React, { Suspense, useEffect, useState } from 'react'
const Layout = React.lazy(() => import('./components/Layout'))
import Login from './components/auth/Login'
import { LoadingScreen } from './components/ui'
import { runDatabaseMigrations } from './services/migrationService'
import { dailyPhraseService } from './services/dailyPhraseService'
import { systemNotificationService } from './services/systemNotificationService'
import { settingsService } from './services/databaseSettingsService'
import { checkActivated } from './utils/auth/licenseValidator'
import { ToastProvider } from './components/ui/Toast'
import './App.css'

function App() {
  const [isInitializing, setIsInitializing] = useState(true)
  const [initError, setInitError] = useState<string | null>(null)
  const [isActivated, setIsActivated] = useState<boolean | null>(null)

  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('正在初始化应用...')
        
        const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
        const isDevelopment = import.meta.env.DEV;
        
        if (isElectron) {
          console.log('桌面环境检查: 通过');
          
          try {
            console.log('正在运行数据库迁移...');
            await runDatabaseMigrations();
            console.log('数据库迁移完成');
            
            try {
              console.log('正在检查每日短语数据...');
              const phrases = await dailyPhraseService.getAllPhrases();
              if (phrases.length === 0) {
                console.log('正在初始化默认短语数据...');
                await dailyPhraseService.initializeDefaultPhrases();
                console.log('默认短语数据初始化完成');
              } else {
                console.log(`已有 ${phrases.length} 条短语数据`);
              }
            } catch (phraseError) {
              console.error('短语数据初始化失败:', phraseError);
            }
          } catch (migrationError) {
            console.error('数据库迁移失败:', migrationError);
          }
        } else if (isDevelopment) {
          console.log('浏览器开发环境: 通过');
          if (!window.electronAPI) {
            window.electronAPI = {
              dbQuery: async () => [],
              dbRun: async () => ({ changes: 0, lastInsertRowid: 0 }),
              getAppVersion: async () => '1.0.0-browser',
              getAppPath: async () => '/browser-mode',
              openExternal: async (url: string) => { 
                window.open(url, '_blank'); 
                return { success: true }; 
              },
              showItemInFolder: async () => ({ success: false, error: '浏览器环境不支持' }),
              platform: 'browser',
              isElectron: false
            };
          }
        } else {
          setInitError('此应用只支持桌面环境运行，请使用桌面版本');
          return;
        }
        
        console.log('应用初始化完成')
        
        try {
          const settings = await settingsService.getSettings()
          systemNotificationService.initialize(settings)
          console.log('系统通知服务初始化完成')
        } catch (notificationError) {
          console.error('系统通知服务初始化失败:', notificationError)
        };
      } catch (error) {
        console.error('桌面应用初始化失败:', error)
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        setInitError(`应用初始化失败: ${errorMessage}`)
      } finally {
        setTimeout(() => {
          setIsInitializing(false)
        }, 1500)
      }
    }

    initializeApp()
  }, [])

  // 初始化后检查激活状态
  useEffect(() => {
    const check = async () => {
      try {
        const ok = await checkActivated()
        setIsActivated(ok)
      } catch {
        setIsActivated(false)
      }
    }
    if (!isInitializing && !initError) {
      check()
    }
  }, [isInitializing, initError])

  if (isInitializing) {
    return (
      <LoadingScreen 
        title="正在初始化应用..."
      />
    )
  }

  if (initError) {
    return (
      <div className="app-error" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div className="error-container" style={{
          background: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          textAlign: 'center',
          maxWidth: '500px'
        }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>应用启动失败</h2>
          <p style={{ marginBottom: '30px', color: '#666' }}>{initError}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  // 若激活状态未判定，先不渲染主界面，避免闪烁
  if (isActivated === null) {
    return (
      <LoadingScreen title="加载登录界面..." />
    )
  }

  // 未激活时，先显示一比一还原的登录界面
  if (!isActivated) {
    return (
      <ToastProvider>
        <Login onActivated={() => setIsActivated(true)} />
      </ToastProvider>
    )
  }

  return (
    <div className="app">
      <ToastProvider>
        <Suspense fallback={<LoadingScreen title="加载主界面..." /> }>
          <Layout />
        </Suspense>
      </ToastProvider>
    </div>
  )
}

export default App
