import React, { useState } from 'react';
import {
  BaseModal,
  Button,
  Badge
} from '../ui/index';
import {
  Users,
  MapPin,
  Clock,
  User,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  FileText,
  Activity,
  Plus
} from 'lucide-react';
import type { GroupSession } from '../../types/groupSession';

interface GroupSessionDetailModalProps {
  isOpen: boolean;
  session: GroupSession;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateStatus: (status: GroupSession['status']) => void;
}

const GroupSessionDetailModal: React.FC<GroupSessionDetailModalProps> = ({
  isOpen,
  session,
  onClose,
  onEdit,
  onDelete,
  onUpdateStatus
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // 状态徽章
  const getStatusBadge = (status: GroupSession['status']) => {
    const statusConfig = {
      '计划中': { variant: 'primary' as const, icon: <Clock size={12} /> },
      '进行中': { variant: 'success' as const, icon: <Play size={12} /> },
      '已完成': { variant: 'gray' as const, icon: <CheckCircle size={12} /> },
      '已取消': { variant: 'danger' as const, icon: <Pause size={12} /> },
      '暂停': { variant: 'warning' as const, icon: <Pause size={12} /> }
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        {config.icon}
        {status}
      </Badge>
    );
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    return timeStr;
  };

  // 状态操作按钮
  const getStatusActions = () => {
    const actions = [];
    
    if (session.status === '计划中') {
      actions.push(
        <Button
          key="start"
          variant="success"
          size="sm"
          leftIcon={<Play size={14} />}
          onClick={() => onUpdateStatus('进行中')}
        >
          开始活动
        </Button>
      );
    }
    
    if (session.status === '进行中') {
      actions.push(
        <Button
          key="pause"
          variant="warning"
          size="sm"
          leftIcon={<Pause size={14} />}
          onClick={() => onUpdateStatus('暂停')}
        >
          暂停活动
        </Button>
      );
      actions.push(
        <Button
          key="complete"
          variant="success"
          size="sm"
          leftIcon={<CheckCircle size={14} />}
          onClick={() => onUpdateStatus('已完成')}
        >
          完成活动
        </Button>
      );
    }
    
    if (session.status === '暂停') {
      actions.push(
        <Button
          key="resume"
          variant="success"
          size="sm"
          leftIcon={<Play size={14} />}
          onClick={() => onUpdateStatus('进行中')}
        >
          恢复活动
        </Button>
      );
    }
    
    return actions;
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={session.title}
      subtitle={`团体活动详情 · ID: ${session.id}`}
      size="xl"
    >
      <div className="modal-body">
        {/* 状态和操作栏 */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between', 
          marginBottom: '24px', 
          padding: '16px', 
          background: '#f8fafc', 
          borderRadius: '8px' 
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {getStatusBadge(session.status)}
            <span style={{ fontSize: '14px', color: '#6b7280' }}>
              创建于 {formatDate(session.createdAt)}
            </span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {getStatusActions()}
          </div>
        </div>

        {/* 标签页导航 */}
        <div style={{ 
          display: 'flex', 
          borderBottom: '1px solid #e5e7eb', 
          marginBottom: '24px' 
        }}>
          <button
            style={{
              padding: '8px 16px',
              fontWeight: '500',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              borderBottom: activeTab === 'overview' ? '2px solid #3b82f6' : '2px solid transparent',
              color: activeTab === 'overview' ? '#3b82f6' : '#6b7280'
            }}
            onClick={() => setActiveTab('overview')}
          >
            概览
          </button>
          <button
            style={{
              padding: '8px 16px',
              fontWeight: '500',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              borderBottom: activeTab === 'participants' ? '2px solid #3b82f6' : '2px solid transparent',
              color: activeTab === 'participants' ? '#3b82f6' : '#6b7280'
            }}
            onClick={() => setActiveTab('participants')}
          >
            参与者
          </button>
          <button
            style={{
              padding: '8px 16px',
              fontWeight: '500',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              borderBottom: activeTab === 'records' ? '2px solid #3b82f6' : '2px solid transparent',
              color: activeTab === 'records' ? '#3b82f6' : '#6b7280'
            }}
            onClick={() => setActiveTab('records')}
          >
            活动记录
          </button>
          <button
            style={{
              padding: '8px 16px',
              fontWeight: '500',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              borderBottom: activeTab === 'statistics' ? '2px solid #3b82f6' : '2px solid transparent',
              color: activeTab === 'statistics' ? '#3b82f6' : '#6b7280'
            }}
            onClick={() => setActiveTab('statistics')}
          >
            统计
          </button>
        </div>

        {/* 概览内容 */}
        {activeTab === 'overview' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            {/* 基本信息 */}
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
              gap: '24px' 
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <h4 style={{ 
                  fontWeight: '500', 
                  color: '#1f2937', 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px',
                  margin: '0 0 8px 0'
                }}>
                  <Users size={16} />
                  基本信息
                </h4>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>团体类型:</span>
                    <Badge variant="primary">{session.sessionType}</Badge>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>目标年龄:</span>
                    <span>{session.targetAge}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>治疗师:</span>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <User size={14} />
                      {session.therapistName}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>参与人数:</span>
                    <span>{session.currentParticipants}/{session.maxParticipants}</span>
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <h4 style={{ 
                  fontWeight: '500', 
                  color: '#1f2937', 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px',
                  margin: '0 0 8px 0'
                }}>
                  <Clock size={16} />
                  时间安排
                </h4>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>活动时长:</span>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <Clock size={14} />
                      {session.duration}分钟
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>活动频率:</span>
                    <span>{session.frequency}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>开始日期:</span>
                    <span>{formatDate(session.startDate)}</span>
                  </div>
                  {session.endDate && (
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#6b7280' }}>结束日期:</span>
                      <span>{formatDate(session.endDate)}</span>
                    </div>
                  )}
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>会议时间:</span>
                    <span>{formatTime(session.meetingTime)}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>活动地点:</span>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <MapPin size={14} />
                      {session.location}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 描述和备注 */}
            {(session.description || session.notes || session.requirements) && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {session.description && (
                  <div>
                    <h4 style={{ fontWeight: '500', color: '#1f2937', marginBottom: '8px' }}>团体描述</h4>
                    <p style={{ 
                      color: '#374151', 
                      background: '#f8fafc', 
                      padding: '12px', 
                      borderRadius: '8px',
                      margin: 0,
                      lineHeight: '1.5'
                    }}>
                      {session.description}
                    </p>
                  </div>
                )}

                {session.requirements && (
                  <div>
                    <h4 style={{ fontWeight: '500', color: '#1f2937', marginBottom: '8px' }}>参与要求</h4>
                    <p style={{ 
                      color: '#374151', 
                      background: '#f8fafc', 
                      padding: '12px', 
                      borderRadius: '8px',
                      margin: 0,
                      lineHeight: '1.5'
                    }}>
                      {session.requirements}
                    </p>
                  </div>
                )}

                {session.notes && (
                  <div>
                    <h4 style={{ fontWeight: '500', color: '#1f2937', marginBottom: '8px' }}>备注</h4>
                    <p style={{ 
                      color: '#374151', 
                      background: '#f8fafc', 
                      padding: '12px', 
                      borderRadius: '8px',
                      margin: 0,
                      lineHeight: '1.5'
                    }}>
                      {session.notes}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* 材料清单 */}
            {session.materials && session.materials.length > 0 && (
              <div>
                <h4 style={{ fontWeight: '500', color: '#1f2937', marginBottom: '8px' }}>所需材料</h4>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                  {session.materials.map((material, index) => (
                    <Badge key={index} variant="primary">
                      {material}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 参与者内容 */}
        {activeTab === 'participants' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <h4 style={{ fontWeight: '500', color: '#1f2937', margin: 0 }}>
                参与者列表 ({session.participants.length})
              </h4>
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Users size={14} />}
                onClick={() => console.log('管理参与者')}
              >
                管理参与者
              </Button>
            </div>

            {session.participants.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '32px 0', color: '#6b7280' }}>
                暂无参与者
              </div>
            ) : (
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
                gap: '16px' 
              }}>
                {session.participants.map(participant => (
                  <div key={participant.id} style={{ 
                    border: '1px solid #e5e7eb', 
                    borderRadius: '8px', 
                    padding: '16px' 
                  }}>
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between', 
                      marginBottom: '8px' 
                    }}>
                      <div style={{ fontWeight: '500' }}>{participant.visitorName}</div>
                      <Badge
                        variant={participant.status === '进行中' ? 'success' : 'gray'}
                      >
                        {participant.status}
                      </Badge>
                    </div>
                    <div style={{ 
                      fontSize: '14px', 
                      color: '#6b7280', 
                      display: 'flex', 
                      flexDirection: 'column', 
                      gap: '4px' 
                    }}>
                      <div>{participant.gender} · {participant.age}岁</div>
                      <div>加入日期: {formatDate(participant.joinDate)}</div>
                      {participant.attendanceRate !== undefined && (
                        <div>出勤率: {participant.attendanceRate}%</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 活动记录内容 */}
        {activeTab === 'records' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <h4 style={{ 
                fontWeight: '500', 
                color: '#1f2937', 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px',
                margin: 0
              }}>
                <FileText size={16} />
                活动记录
              </h4>
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Plus size={14} />}
                onClick={() => console.log('添加活动记录')}
              >
                添加记录
              </Button>
            </div>

            <div style={{ textAlign: 'center', padding: '32px 0', color: '#6b7280' }}>
              暂无活动记录
            </div>
          </div>
        )}

        {/* 统计内容 */}
        {activeTab === 'statistics' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <h4 style={{ 
              fontWeight: '500', 
              color: '#1f2937', 
              display: 'flex', 
              alignItems: 'center', 
              gap: '8px',
              margin: 0
            }}>
              <Activity size={16} />
              统计信息
            </h4>

            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
              gap: '16px' 
            }}>
              <div style={{ 
                background: '#dbeafe', 
                padding: '16px', 
                borderRadius: '8px' 
              }}>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: '700', 
                  color: '#2563eb' 
                }}>
                  {session.currentSession || 0}
                </div>
                <div style={{ fontSize: '14px', color: '#2563eb' }}>已进行次数</div>
              </div>

              <div style={{ 
                background: '#dcfce7', 
                padding: '16px', 
                borderRadius: '8px' 
              }}>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: '700', 
                  color: '#16a34a' 
                }}>
                  {session.participants.length}
                </div>
                <div style={{ fontSize: '14px', color: '#16a34a' }}>参与者数量</div>
              </div>

              <div style={{ 
                background: '#f3e8ff', 
                padding: '16px', 
                borderRadius: '8px' 
              }}>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: '700', 
                  color: '#9333ea' 
                }}>
                  {Math.round(session.participants.reduce((sum, p) => sum + (p.attendanceRate || 0), 0) / (session.participants.length || 1))}%
                </div>
                <div style={{ fontSize: '14px', color: '#9333ea' }}>平均出勤率</div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 底部操作按钮 */}
      <div className="modal-footer">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              variant="danger"
              leftIcon={<Trash2 size={16} />}
              onClick={onDelete}
            >
              删除团体
            </Button>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              variant="secondary"
              onClick={onClose}
            >
              关闭
            </Button>
            <Button
              leftIcon={<Edit size={16} />}
              onClick={onEdit}
            >
              编辑团体
            </Button>
          </div>
        </div>
      </div>
    </BaseModal>
  );
};

// 同时提供命名导出与默认导出，兼容不同导入写法
export { GroupSessionDetailModal };
export default GroupSessionDetailModal;
