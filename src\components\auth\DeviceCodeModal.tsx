import React, { useState, useEffect, useRef } from 'react';
import DeviceIdGenerator from '../../utils/auth/deviceId';
import { useToast } from '../ui/Toast';
import CollapsibleHelp from '../ui/CollapsibleHelp';

interface DeviceCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DeviceCodeModal: React.FC<DeviceCodeModalProps> = ({ isOpen, onClose }) => {
  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const isMountedRef = useRef(true);
  const toast = useToast();

  useEffect(() => {
    isMountedRef.current = true;
    if (isOpen) {
      generateDeviceCode();
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') onClose();
      };
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    } else {
      // reset
      setDeviceId('');
      setCopied(false);
      setIsGenerating(false);
    }
    return () => { isMountedRef.current = false; };
  }, [isOpen, onClose]);

  const generateDeviceCode = async () => {
    if (!isMountedRef.current) return;
    setIsGenerating(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      if (!isMountedRef.current) return;
      const result = await DeviceIdGenerator.getDeviceCode();
      if (isMountedRef.current) {
        setDeviceId(result.deviceCode);
      }
    } catch (error) {
      console.error('生成设备码失败:', error);
      if (isMountedRef.current) setDeviceId('生成失败，请重试');
    } finally {
      if (isMountedRef.current) setIsGenerating(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(deviceId);
      setCopied(true);
      toast.success('设备码已复制');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      const textArea = document.createElement('textarea');
      textArea.value = deviceId;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        toast.success('设备码已复制');
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('降级复制也失败:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  const handleRefresh = () => {
    if (!isMountedRef.current) return;
    setCopied(false);
    toast.info('正在重新生成设备码');
    generateDeviceCode();
  };

  const handleBackgroundClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) onClose();
  };

  const renderSegmented = (code: string) => {
    if (!code) return code;
    return code.split('-').map((seg, i) => (
      <span key={i} className="px-2 py-1 rounded-md bg-white/5 font-semibold tracking-wider">
        {seg}
      </span>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-md"
        onClick={handleBackgroundClick}
      />
      {/* 模态框内容 */}
      <div className="relative w-full max-w-lg mx-auto" onClick={(e) => e.stopPropagation()}>
        <div className="device-code-modal card-border overflow-hidden rounded-2xl animate-modal-enter shadow-2xl">
          {/* 头部 */}
          <div className="px-8 py-6 border-b border-white/10">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-xl glass border border-blue-400/30 flex items-center justify-center mr-4 relative overflow-hidden">
                  <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 7H7v2h2V7zm0 4H7v2h2v-2zm0-8c-.55 0-1 .45-1 1v1H7c-.55 0-1 .45-1 1v1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V8c0-.55-.45-1-1-1h-2V6c0-.55-.45-1-1-1h-1V4c0-.55-.45-1-1-1H9zm6 0h2v1h-2V3zm4 2h2v1h-2V5zM5 9h14v8H5V9z"/>
                    <path d="M11 11h2v2h-2v-2zm4 0h2v2h-2v-2z"/>
                  </svg>
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-orange-400/20 rounded-xl"></div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-1">设备唯一码</h3>
                  <p className="text-sm text-white/60">Hardware Device ID</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="group relative flex items-center justify-center w-8 h-8 rounded-lg bg-red-500/10 border border-red-400/30 hover:bg-red-500/20 hover:border-red-400/50 transition-all duration-200 text-red-300 hover:text-red-200"
                title="关闭设备码窗口"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            <p className="text-white/70 text-sm leading-relaxed mt-4">基于硬件信息生成的唯一设备标识码，用于软件授权激活</p>
          </div>
          {/* 设备码显示区域 */}
          <div className="px-8 py-6">
            <div className="rounded-xl p-5 mb-4 bg-white/5 border border-white/15 flex flex-col items-stretch">
              {isGenerating ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex items-center space-x-4">
                    <div className="device-code-spinner"></div>
                    <span className="text-white/80 text-base">正在生成设备唯一码...</span>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex flex-wrap gap-2 justify-center mb-4 font-mono text-lg text-white">
                    {renderSegmented(deviceId)}
                  </div>
                  <div className="flex justify-center gap-3">
                    <button
                      onClick={handleCopy}
                      disabled={isGenerating || !deviceId}
                      className="inline-flex items-center px-4 py-2 rounded-lg bg-blue-500/80 hover:bg-blue-500 text-white text-sm font-medium transition disabled:opacity-50"
                    >
                      {copied ? '已复制' : '复制设备码'}
                    </button>
                    <button
                      onClick={handleRefresh}
                      disabled={isGenerating}
                      className="inline-flex items-center px-4 py-2 rounded-lg bg-orange-500/80 hover:bg-orange-500 text-white text-sm font-medium transition disabled:opacity-50"
                    >
                      {isGenerating ? '生成中...' : '重新生成'}
                    </button>
                  </div>
                </div>
              )}
            </div>
            {/* 折叠说明 */}
            <div className="mt-2">
              <CollapsibleHelp
                title="设备码使用说明"
                items={[
                  '设备码基于硬件信息生成，每台设备唯一且固定',
                  '请将完整设备码发送给软件提供商获取激活文件',
                  '激活文件与设备码绑定，仅限当前设备使用',
                  '更换硬件后需要重新获取设备码和激活文件'
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceCodeModal;

