import React, { useState, useEffect, useRef } from 'react';
import DeviceIdGenerator from '../../utils/auth/deviceId';
import { useToast } from '../ui/Toast';
import '../ui/BaseModal.css';

interface DeviceCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DeviceCodeModal: React.FC<DeviceCodeModalProps> = ({ isOpen, onClose }) => {
  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const isMountedRef = useRef(true);
  const toast = useToast();

  useEffect(() => {
    isMountedRef.current = true;
    if (isOpen) {
      generateDeviceCode();
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') onClose();
      };
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    } else {
      // reset
      setDeviceId('');
      setCopied(false);
      setIsGenerating(false);
    }
    return () => { isMountedRef.current = false; };
  }, [isOpen, onClose]);

  const generateDeviceCode = async () => {
    if (!isMountedRef.current) return;
    setIsGenerating(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      if (!isMountedRef.current) return;
      const result = await DeviceIdGenerator.getDeviceCode();
      if (isMountedRef.current) {
        setDeviceId(result.deviceCode);
      }
    } catch (error) {
      console.error('生成设备码失败:', error);
      if (isMountedRef.current) setDeviceId('生成失败，请重试');
    } finally {
      if (isMountedRef.current) setIsGenerating(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(deviceId);
      setCopied(true);
      toast.success('设备码已复制');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      const textArea = document.createElement('textarea');
      textArea.value = deviceId;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        toast.success('设备码已复制');
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('降级复制也失败:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  const handleRefresh = () => {
    if (!isMountedRef.current) return;
    setCopied(false);
    toast.info('正在重新生成设备码');
    generateDeviceCode();
  };

  const handleBackgroundClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) onClose();
  };

  const renderSegmented = (code: string) => {
    if (!code) return code;
    return code.split('-').map((seg, i) => (
      <span key={i} className="px-3 py-2 rounded-md glass border border-white/20 font-mono font-semibold tracking-wider text-white">
        {seg}
      </span>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="base-modal-overlay" onClick={handleBackgroundClick}>
      {/* 模态框内容 - 使用主应用设计风格 */}
      <div className="base-modal-container base-modal-md base-modal--glass" onClick={(e) => e.stopPropagation()}>
        {/* 头部 */}
        <div className="base-modal-header">
          <div className="base-modal-header-content">
            <div className="base-modal-title-section">
              <h3 className="base-modal-title">设备唯一码</h3>
              <p className="base-modal-subtitle">基于硬件信息生成的唯一设备标识码，用于软件授权激活</p>
            </div>
          </div>
          <button
            className="base-modal-close"
            onClick={onClose}
            aria-label="关闭设备码窗口"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="base-modal-content">
          <div className="p-6">
            {/* 设备码显示区域（玻璃卡片）*/}
            <div className="glass rounded-xl p-5 mb-6 border border-white/15">
              {isGenerating ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-white/80 text-sm">正在生成设备唯一码...</span>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex flex-wrap gap-2 justify-center mb-4 font-mono text-lg text-white">
                    {renderSegmented(deviceId)}
                  </div>
                  <div className="flex justify-center gap-3">
                    <button
                      onClick={handleCopy}
                      disabled={isGenerating || !deviceId}
                      className="glass inline-flex items-center px-4 py-2 rounded-lg border border-white/20 hover:bg-white/10 text-white text-sm font-medium transition disabled:opacity-50"
                    >
                      {copied ? '已复制' : '复制设备码'}
                    </button>
                    <button
                      onClick={handleRefresh}
                      disabled={isGenerating}
                      className="glass inline-flex items-center px-4 py-2 rounded-lg border border-white/20 hover:bg-white/10 text-white text-sm font-medium transition disabled:opacity-50"
                    >
                      {isGenerating ? '生成中...' : '重新生成'}
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* 使用说明 */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-white mb-3">设备码使用说明：</h4>
              <div className="space-y-2 text-sm text-white/70">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>设备码基于硬件信息生成，每台设备唯一且固定</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>请将完整设备码发送给软件提供商获取激活文件</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>激活文件与设备码绑定，仅限当前设备使用</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>更换硬件后需要重新获取设备码和激活文件</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceCodeModal;

