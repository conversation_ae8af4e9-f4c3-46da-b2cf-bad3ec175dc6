#!/usr/bin/env node

/**
 * 激活码签名器CLI工具
 */

import { Command } from 'commander';
import { writeFileSync } from 'fs';
import { KeyManager } from './keyManager.js';
import { KeyRegistry } from './keyRegistry.js';
import { buildActivationCode, formatActivationCode } from './signer.js';

const program = new Command();

program
  .name('activation-signer')
  .description('离线激活码签名工具 - 重构版')
  .version('1.1.0');

// 生成密钥对命令
program
  .command('keygen')
  .description('生成RSA-2048密钥对')
  .option('-v, --version <number>', '密钥版本号', '1')
  .action((options) => {
    try {
      console.log('正在生成RSA-2048密钥对...');
      const keyPair = KeyManager.generateKeyPair();
      KeyManager.saveKeyPair(keyPair);
      
      const version = parseInt(options.version);
      KeyRegistry.setActiveKeyVersion(version);
      
      console.log('密钥对生成完成！');
      console.log('\n⚠️  重要提醒:');
      console.log('1. 请妥善保管私钥文件 keys/private.pem');
      console.log('2. 公钥文件 keys/public.pem 需要嵌入到客户端软件中');
      console.log('3. 建议将私钥备份到离线存储设备');
      console.log(`4. 当前密钥版本: ${version}`);
    } catch (error: any) {
      console.error('生成密钥对失败:', error.message);
      process.exit(1);
    }
  });

// 生成激活文件命令
program
  .command('sign <deviceCode>')
  .description('为指定设备码生成激活文件')
  .option('--json', '以JSON格式输出到控制台')
  .option('--output <path>', '输出文件路径（默认：激活文件_设备码.lic）')
  .action((deviceCode: string, options) => {
    try {
      console.log(`正在为设备码 ${deviceCode} 生成激活文件...`);

      const { activationCode, payload } = buildActivationCode(deviceCode);

      // 创建激活文件内容
      const activationFileContent = {
        deviceCode,
        activationCode,
        keyVersion: payload.kv,
        issuedAt: payload.ia,
        timestamp: new Date(payload.ia).toISOString(),
        version: '1.0',
        description: '沙盘管理系统激活文件'
      };

      // 确定输出文件路径
      const outputPath = options.output || `激活文件_${deviceCode.replace(/-/g, '')}.lic`;

      // 保存激活文件
      writeFileSync(outputPath, JSON.stringify(activationFileContent, null, 2), 'utf-8');

      if (options.json) {
        console.log(JSON.stringify(activationFileContent, null, 2));
      } else {
        console.log('\n✅ 激活文件生成成功!');
        console.log('='.repeat(80));
        console.log(`📁 文件路径: ${outputPath}`);
        console.log(`🔑 设备码: ${deviceCode}`);
        console.log(`📅 生成时间: ${activationFileContent.timestamp}`);
        console.log(`🔢 密钥版本: ${payload.kv}`);
        console.log('='.repeat(80));
        console.log('\n📤 请将此激活文件发送给客户进行激活');
      }
    } catch (error: any) {
      console.error('生成激活文件失败:', error.message);
      process.exit(1);
    }
  });

// 设置密钥版本命令
program
  .command('set-key-version <version>')
  .description('设置活跃的密钥版本')
  .action((version: string) => {
    try {
      const versionNum = parseInt(version);
      if (isNaN(versionNum) || versionNum < 1) {
        throw new Error('密钥版本必须是大于0的整数');
      }
      
      KeyRegistry.setActiveKeyVersion(versionNum);
      console.log(`✅ 活跃密钥版本已设置为: ${versionNum}`);
    } catch (error: any) {
      console.error('设置密钥版本失败:', error.message);
      process.exit(1);
    }
  });

// 查看当前密钥版本命令
program
  .command('key-version')
  .description('查看当前活跃的密钥版本')
  .action(() => {
    try {
      const version = KeyRegistry.getActiveKeyVersion();
      console.log(`当前活跃密钥版本: ${version}`);
    } catch (error: any) {
      console.error('获取密钥版本失败:', error.message);
      process.exit(1);
    }
  });

program.parse();
