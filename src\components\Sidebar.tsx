import React from 'react';
import { Home, Users, Calendar, Wrench, UsersRound, BarChart3, Settings, HelpCircle, FileText, Globe } from 'lucide-react';
import './Sidebar.css';

type PageType = 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help';

interface SidebarProps {
  isCollapsed: boolean;
  currentPage?: PageType;
  onPageChange?: (page: PageType) => void;
}

interface MenuItemProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  collapsed: boolean;
  onClick?: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, label, active = false, collapsed, onClick }) => (
  <button 
    className={`menu-item ${active ? 'active' : ''} ${collapsed ? 'collapsed' : ''}`}
    onClick={onClick}
    title={collapsed ? label : ''}
  >
    <span className="menu-icon">{icon}</span>
    {!collapsed && <span className="menu-label">{label}</span>}
  </button>
);

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, currentPage = 'dashboard', onPageChange }) => {
  const menuItems = [
    { icon: <Home size={20} />, label: '首页', page: 'dashboard' as const },
    { icon: <Users size={20} />, label: '来访者管理', page: 'visitors' as const },
    { icon: <FileText size={20} />, label: '个案管理', page: 'cases' as const },
    { icon: <Calendar size={20} />, label: '日程安排', page: 'schedule' as const },
    { icon: <Wrench size={20} />, label: '沙具管理', page: 'sandtools' as const },
    { icon: <UsersRound size={20} />, label: '团沙管理', page: 'group-sessions' as const },
    { icon: <BarChart3 size={20} />, label: '数据统计', page: 'statistics' as const },
  ];

  const bottomMenuItems = [
    { icon: <Settings size={20} />, label: '系统设置', page: 'settings' as const },
    { icon: <HelpCircle size={20} />, label: '帮助中心', page: 'help' as const },
  ];



  const handleMenuClick = (item: { page: PageType; label: string }) => {
    if (item.page && onPageChange) {
      onPageChange(item.page);
    } else {
      console.log('点击菜单:', item.label);
    }
  };

  return (
    <aside className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <nav className="sidebar-nav">
        <div className="menu-section">
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              active={item.page === currentPage}
              collapsed={isCollapsed}
              onClick={() => handleMenuClick(item)}
            />
          ))}
        </div>
        
        <div className="menu-section bottom">
          {bottomMenuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              active={item.page === currentPage}
              collapsed={isCollapsed}
              onClick={() => {
                if (item.page && onPageChange) {
                  onPageChange(item.page);
                }
              }}
            />
          ))}
        </div>
      </nav>
    </aside>
  );
};

export default Sidebar;
