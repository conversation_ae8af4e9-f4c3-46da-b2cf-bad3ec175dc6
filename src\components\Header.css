/* Header组件样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: saturate(120%) blur(8px);
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  gap: 16px;
  -webkit-app-region: drag; /* 允许拖拽窗口 */
}

/* 顶部渐变高光提升层次感（色彩层次） */
/* .header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(59,130,246,0.25), rgba(45,212,191,0.25));
  pointer-events: none;
} */

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px; /* 更圆润的触达区 */
  color: #6b7280;
  transition: all 0.15s ease;
  margin-left: -14px; /* 调整左边距，使按钮更靠左 */
  width: 36px; /* 固定宽度确保对齐（≥36px 命中区域符合菲茨定律） */
  height: 36px; /* 固定高度确保对齐 */
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-app-region: no-drag; /* 按钮不可拖拽 */
}

.menu-toggle:hover {
  background: rgba(148, 163, 184, 0.15);
  color: #374151;
  box-shadow: inset 0 0 0 1px rgba(148,163,184,0.25);
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 12px; /* 增大与徽章间距，更显呼吸感 */
}

/* 圆形徽章容器，承载 logo，增强品牌识别 */
.logo-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #FCD34D, #F59E0B);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  flex-shrink: 0;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.brand-info:hover .logo-badge {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 让徽章内的图片更协调 */
.logo-badge .logo-image {
  height: 22px;
  width: auto;
  object-fit: contain;
}

.brand-text {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.app-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  letter-spacing: 0.2px;
}

.app-subtitle {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  line-height: 1;
  margin-top: 2px;
}

.header .logo-image {
  height: 36px; /* 根据需求调整大小 */
  width: auto; /* 保持宽高比例 */
  object-fit: contain;
}

/* 中间搜索区域 */
.header-center {
  flex: 1;
  min-width: 200px;
  max-width: 600px;
  margin: 0 24px;
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag; /* 搜索区域允许交互 */
}

.search-form {
  width: 100%;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 36px; /* 留出右侧kbd提示 */
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 14px;
  background:
    linear-gradient(180deg, rgba(255,255,255,0.7), rgba(255,255,255,0.4));
  backdrop-filter: blur(2px);
  transition: all 0.2s ease;
  box-sizing: border-box;
  -webkit-app-region: no-drag; /* 输入框允许交互 */
}

.search-input::placeholder {
  color: #9ca3af; /* 提升可读性，满足WCAG对比 */
}

.search-input:focus {
  outline: none;
  border-color: #4f9cf9;
  background: white;
  box-shadow: 0 0 0 3px rgba(79, 156, 249, 0.12);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 1;
}

.kbd-hint {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
  font-size: 12px;
}

.kbd-hint kbd {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 11px;
  line-height: 1;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(148,163,184,0.5);
  background: rgba(248, 250, 252, 0.7);
  color: #475569;
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9ca3af;
  padding: 6px; /* 命中区域≥8px */
  border-radius: 6px;
  z-index: 1;
  -webkit-app-region: no-drag;
}

.search-clear:hover {
  color: #6b7280;
  background: rgba(148,163,184,0.12);
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  -webkit-app-region: no-drag; /* 右侧交互区域不作为拖拽 */
}

/* 快速操作按钮组 */
.header-actions {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(203,213,225,0.7);
  background: rgba(255,255,255,0.6);
  border-radius: 10px;
  color: #475569;
  cursor: pointer;
  transition: all 0.15s ease;
}

.action-btn:hover {
  background: white;
  color: #1f2937;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transform: translateY(-1px);
}

/* 用户菜单容器 */
.user-menu-container {
  position: relative;
}

.user-btn {
  position: relative;
}

.user-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 220px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1001;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 0;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  text-align: left;
}

.menu-item:hover {
  background: #f3f4f6;
}

.logout-btn {
  color: #dc2626;
}

.logout-btn:hover {
  background: #fef2f2;
  color: #dc2626;
}

.divider {
  width: 1px;
  height: 22px;
  background: linear-gradient(180deg, rgba(203,213,225,0), rgba(203,213,225,1), rgba(203,213,225,0));
}

/* 自定义窗口控制按钮 */
.window-controls {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.window-btn {
  width: 36px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.15s ease, color 0.15s ease, transform 0.15s ease;
  -webkit-app-region: no-drag; /* 避免按钮成为拖拽区域 */
}

.window-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.window-btn:active {
  transform: translateY(1px);
}

.win-close:hover {
  background: #ef4444;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-center {
    max-width: 480px;
    margin: 0 20px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .header-center {
    margin: 0 12px;
    max-width: 320px;
  }
  
  .search-input {
    font-size: 13px;
    padding: 0 32px 0 32px;
  }
  
  .search-icon {
    left: 10px;
  }
  
  .search-clear {
    right: 10px;
  }
  
  .menu-toggle {
    margin-left: 0;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .app-subtitle {
    display: none;
  }
  
  .header-right {
    gap: 12px;
  }
}

@media (max-width: 640px) {
  .header {
    padding: 0 8px;
  }
  
  .header-center {
    margin: 0 8px;
    max-width: 220px;
  }
  
  .brand-info {
    display: none;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-right {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header-center {
    max-width: 180px;
  }
  
  .search-input {
    font-size: 12px;
    padding: 0 28px 0 28px;
  }
  
  .search-input::placeholder {
    font-size: 12px;
  }
  
  .search-icon {
    left: 8px;
    width: 14px;
    height: 14px;
  }
  
  .search-clear {
    right: 8px;
    width: 14px;
    height: 14px;
  }
}
