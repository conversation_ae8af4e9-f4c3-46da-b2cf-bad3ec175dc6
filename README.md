# 沙盘管理系统（桌面版）

一个专业的心理健康服务沙盘疗法管理桌面应用，基于Electron和现代Web技术构建。

## 📋 产品特性

- 🖥️ **原生桌面应用** - 基于Electron，支持Windows、macOS、Linux
- 🗄️ **本地数据存储** - 使用SQLite本地数据库，数据安全可控
- 🚀 **高性能界面** - React 19 + TypeScript构建的现代化UI
- 📊 **数据可视化** - 内置图表和统计分析功能
- 💼 **离线工作** - 无需网络连接，完全离线运行
- 🔄 **数据备份** - 支持数据导入导出功能

## 🏥 功能模块

- 🏥 **来访者管理** - 完整的来访者信息档案管理
- 📋 **个案管理** - 详细的个案记录和治疗跟踪
  - 支持CSV、Word、PDF格式导出
  - 灵活的导出范围选择
  - 完整的个案数据字段
- 📅 **日程安排** - 智能的预约和时间管理
- 🧸 **沙具管理** - 专业的沙盘工具库存管理
- 👫 **团沙管理** - (已停用) 团体沙盘治疗活动组织
- 📊 **数据统计** - 全面的数据分析和可视化报告
- ⚙️ **系统设置** - 灵活的系统配置选项
- 🆘 **帮助中心** - 完整的使用指南和支持

## 🛠️ 技术栈

### 前端技术
- **UI框架**: React 19 + TypeScript
- **构建工具**: Vite 7
- **样式**: CSS3 + 现代设计系统
- **图标**: Lucide React
- **图表**: Recharts
- **UI组件**: 自定义组件库

### 桌面技术
- **桌面框架**: Electron 37
- **进程通信**: IPC (Inter-Process Communication)
- **原生功能**: 文件系统、系统菜单、对话框

### 数据存储
- **数据库**: SQLite3
- **数据导出**: CSV、Word(DOCX)、PDF、JSON格式
- **文件处理**: file-saver, docx, jspdf

### 开发工具
- **代码检查**: ESLint
- **构建**: Electron Builder
- **并发**: concurrently, wait-on

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式（推荐）
```bash
npm start
# 或
npm run electron-dev
```

### 仅运行前端开发服务器
```bash
npm run dev
```

### 构建应用
```bash
npm run build
```

### 打包桌面应用
```bash
# 打包为可分发文件
npm run dist

# 仅构建，不打包
npm run pack
```

### 代码检查
```bash
npm run lint
```

## 📁 项目结构

```
├── electron/                # Electron主进程文件
│   ├── main.cjs             # 主进程入口（sqlite3数据库集成）
│   └── preload.js           # 预加载脚本（IPC API暴露）
├── src/                     # React前端源码
│   ├── components/          # React组件
│   │   ├── cases/           # 个案管理
│   │   ├── visitors/        # 来访者管理
│   │   ├── sandtools/       # 沙具管理
│   │   ├── schedule/        # 日程管理
│   │   ├── group-sessions/  # 团沙管理
│   │   ├── settings/        # 系统设置
│   │   ├── help/            # 帮助中心
│   │   └── ui/              # 通用UI组件
│   ├── data/                # 模拟数据
│   ├── services/            # 业务逻辑服务
│   │   ├── sqliteDataManager.ts  # SQLite数据管理器
│   │   └── helpDataService.ts    # 帮助数据服务
│   ├── types/               # TypeScript类型定义
│   ├── config/              # 配置文件
│   ├── styles/              # 全局样式
│   └── utils/               # 工具函数
├── public/                  # 静态资源
├── docs/                    # 项目文档
├── dist/                    # 前端构建输出
├── dist-electron/           # 桌面应用构建输出
└── package.json             # 项目配置
```

## 🗄️ 数据存储

### SQLite数据库
- **位置**: 用户数据目录 (`app.getPath('userData')/沙盘管理系统/`)
- **文件**: `xlsp.db`
- **引擎**: sqlite3 库 (原生异步API)
- **表结构**: 来访者、个案、团体会话、沙具管理等
- **IPC通信**: 主进程提供 db-query 和 db-run 处理器

### 数据备份
- **导出格式**: JSON
- **导入功能**: 支持数据恢复
- **自动备份**: 可配置定期备份

## 🔧 开发指南

### 数据库架构
- **数据管理器**: `sqliteDataManager.ts` - 专用SQLite数据访问层
- **主进程**: `main.cjs` - sqlite3库集成，提供IPC处理器
- **数据持久化**: 直接SQLite连接，支持事务和异步操作
- **错误处理**: 数据库不可用时降级为只读模式

### 开发模式
1. 前端开发服务器运行在 `http://localhost:5173`
2. Electron主进程自动加载开发服务器
3. SQLite数据库自动初始化和表结构升级
4. 支持热重载和开发者工具

### 构建流程
1. 前端代码构建到 `dist/` 目录
2. Electron Builder打包整个应用
3. sqlite3 native模块自动处理
4. 生成平台特定的安装包

### 添加新功能
详细的开发指南请参考：
- [开发规范](docs/development_guidelines.md)
- [设计系统](docs/design_system.md)

## 📦 打包说明

### Windows
- **格式**: NSIS 安装程序 (.exe)
- **特性**: 可选安装目录、桌面快捷方式

### macOS
- **格式**: DMG 磁盘映像
- **支持**: Intel (x64) 和 Apple Silicon (arm64)

### Linux
- **格式**: AppImage
- **兼容**: 主流Linux发行版

## 📚 文档架构

基于现代开发文档最佳实践，项目采用分层文档架构：

### 核心文档
- **README.md** - 项目概览和快速入门
- **DEVELOPMENT.md** - 详细开发指南和架构说明
- **API.md** - API参考和接口文档

### 功能模块文档
- **CASES.md** - 个案管理模块详细说明
- **VISITORS.md** - 来访者管理模块说明
- **SCHEDULE.md** - 日程管理模块说明

### 技术文档
- **ARCHITECTURE.md** - 系统架构设计
- **DEPLOYMENT.md** - 部署和运维指南
- **TESTING.md** - 测试策略和指南

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/新功能`)
3. 提交更改 (`git commit -am '添加新功能'`)
4. 推送分支 (`git push origin feature/新功能`)
5. 创建Pull Request

## 📄 许可证

本项目基于 [MIT 许可证](LICENSE) 开源。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 查看帮助文档
