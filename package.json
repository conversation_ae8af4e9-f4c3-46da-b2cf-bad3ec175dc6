{"name": "xlsp2", "productName": "沙盘管理系统", "description": "专业的心理健康沙盘疗法管理系统", "author": "您的名字", "version": "1.0.0", "private": true, "main": "electron/main.cjs", "type": "module", "scripts": {"dev": "concurrently \"vite --port 8080 --strictPort=true\" \"wait-on http://localhost:8080 && cross-env NODE_ENV=development DEV_PORT=8080 electron .\"", "build": "tsc -b && vite build", "lint": "eslint .", "electron": "electron .", "electron-dev": "concurrently \"vite --port 8080 --strictPort=true\" \"wait-on http://localhost:8080 && cross-env NODE_ENV=development DEV_PORT=8080 electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "npm run build && electron-builder --dir", "start": "npm run electron-dev", "rebuild-native": "electron-rebuild -f -w better-sqlite3", "postinstall": "npm run rebuild-native || echo skip native rebuild", "prepack": "npm run rebuild-native"}, "dependencies": {"better-sqlite3": "^12.2.0", "clsx": "^2.1.1", "docx": "^9.5.1", "file-saver": "^2.0.5", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/file-saver": "^2.0.7", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "concurrently": "^9.2.1", "cross-env": "^10.0.0", "electron": "^37.4.0", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "wait-on": "^8.0.4"}, "build": {"appId": "com.yourcompany.xlsp", "productName": "沙盘管理系统", "copyright": "Copyright © 2024 ${author}", "icon": "public/icon.png", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraFiles": [{"from": "public/icon.png", "to": "resources/icon.png"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "沙盘管理系统"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "category": "public.app-category.medical"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "category": "Office"}, "fileAssociations": [{"ext": "xlsp", "name": "沙盘数据文件", "description": "沙盘管理系统数据文件", "icon": "public/icon.png"}], "asar": true, "asarUnpack": ["**/node_modules/better-sqlite3/**/*", "**/better_sqlite3.node"]}}