import React, { useState, useEffect } from 'react';
import { Download, Upload, RotateCcw } from 'lucide-react';
import { Card } from '../ui/Card';
import { Input, Select } from '../ui/Form';
import { Button } from '../ui/Button';
import type { AppSettings } from '../../types/settings';
import { electronDataManager } from '../../services/electronDataManager';
import { settingsService } from '../../services/databaseSettingsService';

interface DataManagementProps {
  settings: AppSettings;
  updateSettings: (path: string, value: unknown) => void;
  onSettingsChange: (settings: AppSettings) => void;
  saving: boolean;
}

const DataManagement: React.FC<DataManagementProps> = ({ 
  settings, 
  updateSettings, 
  onSettingsChange: _onSettingsChange
}) => {
  const { dataManagement } = settings;
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [backingUp, setBackingUp] = useState(false);
  const [restoring, setRestoring] = useState(false);
  const [cleaning, setCleaning] = useState(false);
  const [backupList, setBackupList] = useState<Array<{name: string; path: string; size: number; modified: string}>>([]);
  const [loadingBackups, setLoadingBackups] = useState(false);
  const electronAPI = (window as any).electronAPI as any;

  // 导出设置
  const handleExportSettings = async () => {
    if (!electronAPI?.exportData) return alert('Electron 环境不可用');
    setExporting(true);
    try {
      const settings = await settingsService.getSettings();
      const result = await electronAPI.exportData(settings);
      
      if (result.success) {
        alert('设置导出成功！');
      } else if (!result.canceled) {
        alert(`设置导出失败: ${result.error}`);
      }
    } catch (error) {
      alert('设置导出失败，请检查应用状态');
    } finally {
      setExporting(false);
    }
  };

  // 导出数据
  const handleExportData = async () => {
    if (!electronAPI?.exportData) return alert('Electron 环境不可用');
    setExporting(true);
    try {
      // 获取所有需要导出的数据
      const visitors = await electronDataManager.getAllVisitors();
      const cases = await electronDataManager.getAllCases();
      const groupSessions = await electronDataManager.getAllGroupSessions();
      const sandTools = await electronDataManager.getAllSandTools();
      
      const exportData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        visitors,
        cases,
        groupSessions,
        sandTools
      };
      
      const result = await electronAPI.exportData(exportData);
      
      if (result.success) {
        alert('数据导出成功！');
      } else if (!result.canceled) {
        alert(`数据导出失败: ${result.error}`);
      }
    } catch (error) {
      alert('数据导出失败，请检查应用状态');
    } finally {
      setExporting(false);
    }
  };

  // 导入设置
  const handleImportSettings = async () => {
    if (!electronAPI?.importData) return alert('Electron 环境不可用');
    setImporting(true);
    try {
      const result = await electronAPI.importData();
      
      if (result.success) {
        // 验证导入的数据是否为有效的设置格式
        if (result.data && typeof result.data === 'object') {
          await settingsService.saveSettings(result.data);
          alert('设置导入成功！应用可能需要重启才能生效');
        } else {
          alert('导入的文件格式不正确');
        }
      } else if (!result.canceled) {
        alert(`设置导入失败: ${result.error}`);
      }
    } catch (error) {
      alert('设置导入失败，请检查文件格式');
    } finally {
      setImporting(false);
    }
  };

  // 导入数据
  const handleImportData = async () => {
    if (!electronAPI?.importData) return alert('Electron 环境不可用');
    setImporting(true);
    try {
      const result = await electronAPI.importData();
      
      if (result.success) {
        // 验证导入的数据格式
        if (result.data && result.data.version === '1.0.0') {
          const { visitors, cases, groupSessions, sandTools } = result.data;
          
          // 导入数据到数据库
          if (visitors && Array.isArray(visitors)) {
            for (const visitor of visitors) {
              await electronDataManager.saveVisitor(visitor);
            }
          }
          
          if (cases && Array.isArray(cases)) {
            for (const caseData of cases) {
              await electronDataManager.saveCase(caseData);
            }
          }
          
          if (groupSessions && Array.isArray(groupSessions)) {
            for (const session of groupSessions) {
              await electronDataManager.saveGroupSession(session);
            }
          }
          
          if (sandTools && Array.isArray(sandTools)) {
            for (const tool of sandTools) {
              await electronDataManager.saveSandTool(tool);
            }
          }
          
          alert('数据导入成功！');
        } else {
          alert('导入的文件格式不正确');
        }
      } else if (!result.canceled) {
        alert(`数据导入失败: ${result.error}`);
      }
    } catch (error) {
      alert('数据导入失败，请检查文件格式');
    } finally {
      setImporting(false);
    }
  };

  // 重置设置
  const handleResetSettings = async () => {
    if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
      try {
        await settingsService.resetSettings();
        alert('设置已重置为默认值');
      } catch (error) {
        alert('重置失败：' + (error as Error).message);
      }
    }
  };

  const loadBackups = async () => {
    if (!electronAPI?.listBackups) return;
    setLoadingBackups(true);
    try {
      const res = await electronAPI.listBackups();
      if (res.success) {
        setBackupList(res.files.map((f: any) => ({ ...f, modified: new Date(f.modified).toLocaleString() })));
      }
    } catch (e) { /* ignore */ } finally { setLoadingBackups(false); }
  };

  useEffect(() => { loadBackups(); }, []);

  const handleManualBackup = async () => {
    if (!electronAPI?.performBackup) return alert('Electron 环境不可用');
    setBackingUp(true);
    try {
      const res = await electronAPI.performBackup({});
      if (res.success) {
        alert('备份成功: ' + res.filePath);
        loadBackups();
      } else if (!res.canceled) {
        alert('备份失败: ' + (res.error || '未知错误'));
      }
    } catch (e) {
      alert('备份异常: ' + (e as Error).message);
    } finally { setBackingUp(false); }
  };

  const handleRestore = async (path?: string) => {
    if (!electronAPI?.restoreBackup) return alert('Electron 环境不可用');
    if (!confirm('确认执行恢复? 将合并导入数据。')) return;
    setRestoring(true);
    try {
      const res = await electronAPI.restoreBackup(path);
      if (res.success) {
        alert('恢复完成, 建议重启应用。');
      } else if (!res.canceled) {
        alert('恢复失败: ' + (res.error || '未知错误'));
      }
    } catch (e) {
      alert('恢复异常: ' + (e as Error).message);
    } finally { setRestoring(false); }
  };

  const handleCleanup = async () => {
    if (!electronAPI?.cleanupData) return alert('Electron 环境不可用');
    if (!confirm('确认执行清理操作? 旧备份将被删除。')) return;
    setCleaning(true);
    try {
      const res = await electronAPI.cleanupData({
        tempFiles: dataManagement.cleanup.tempFiles,
        logs: dataManagement.cleanup.logs,
        cache: dataManagement.cleanup.cache,
        oldBackups: dataManagement.cleanup.oldBackups,
        backupRetentionDays: 30
      });
      if (res.success) {
        alert('清理完成\n删除文件数量: ' + (res.result.removed?.length || 0));
        loadBackups();
      } else {
        alert('清理失败: ' + (res.error || '未知错误'));
      }
    } catch (e) {
      alert('清理异常: ' + (e as Error).message);
    } finally { setCleaning(false); }
  };

  return (
    <div className="settings-form">
      {/* 新增：备份与恢复操作 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">备份与恢复</h3>
          <p className="card-subtitle">手动执行数据备份或从历史备份恢复</p>
        </div>
        <div className="card-content">
          <div className="settings-actions">
            <Button variant="primary" onClick={handleManualBackup} disabled={backingUp}>
              {backingUp ? '备份中...' : '立即备份'}
            </Button>
            <Button variant="secondary" onClick={() => handleRestore()} disabled={restoring}>
              {restoring ? '恢复中...' : '选择文件恢复'}
            </Button>
            <Button variant="warning" onClick={handleCleanup} disabled={cleaning}>
              {cleaning ? '清理中...' : '执行清理'}
            </Button>
            <Button variant="ghost" onClick={loadBackups} disabled={loadingBackups}>
              {loadingBackups ? '刷新中...' : '刷新列表'}
            </Button>
          </div>
          <div className="mt-lg">
            <p className="text-sm text-secondary">备份文件会保存在本地用户数据目录的 backups 文件夹中。</p>
          </div>
          <div className="mt-md">
            <h4 className="text-sm" style={{marginBottom: '6px'}}>历史备份</h4>
            {backupList.length === 0 && !loadingBackups && <div className="text-sm">暂无备份</div>}
            <ul className="backup-list" style={{maxHeight:'180px',overflowY:'auto',padding:0,margin:0,listStyle:'none'}}>
              {backupList.map(b => (
                <li key={b.path} className="backup-item" style={{display:'flex',justifyContent:'space-between',alignItems:'center',padding:'6px 4px',borderBottom:'1px solid #eee'}}>
                  <div style={{flex:1,minWidth:0}}>
                    <div className="text-sm" style={{fontWeight:500,whiteSpace:'nowrap',overflow:'hidden',textOverflow:'ellipsis'}}>{b.name}</div>
                    <div className="text-xs text-secondary">{b.modified} · {(b.size/1024).toFixed(1)} KB</div>
                  </div>
                  <div style={{display:'flex',gap:'6px'}}>
                    <Button size="sm" variant="secondary" onClick={() => handleRestore(b.path)} disabled={restoring}>恢复</Button>
                    <Button size="sm" variant="ghost" onClick={() => electronAPI?.showItemInFolder?.(b.path)}>位置</Button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </Card>

      {/* 备份设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">备份设置</h3>
          <p className="card-subtitle">数据自动备份配置</p>
        </div>
        <div className="card-content">
          <div className="mb-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.backup.autoBackup}
                onChange={(e) => updateSettings('dataManagement.backup.autoBackup', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用自动备份
            </label>
          </div>
          
          {dataManagement.backup.autoBackup && (
            <div className="settings-form-grid">
              <Input
                label="备份时间"
                type="time"
                value={dataManagement.backup.backupTime}
                onChange={(e) => updateSettings('dataManagement.backup.backupTime', e.target.value)}
              />
              
              <Select
                label="备份频率"
                value={dataManagement.backup.frequency}
                onChange={(e) => updateSettings('dataManagement.backup.frequency', e.target.value)}
                options={[
                  { value: 'daily', label: '每日备份' },
                  { value: 'weekly', label: '每周备份' },
                  { value: 'monthly', label: '每月备份' }
                ]}
              />
              
              <Select
                label="存储位置"
                value={dataManagement.backup.location}
                onChange={(e) => updateSettings('dataManagement.backup.location', e.target.value)}
                options={[
                  { value: 'local', label: '本地存储' },
                  { value: 'cloud', label: '云端存储' }
                ]}
              />
            </div>
          )}
          
          {dataManagement.backup.autoBackup && (
            <div className="mt-lg">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.backup.encryption}
                  onChange={(e) => updateSettings('dataManagement.backup.encryption', e.target.checked)}
                />
                <span className="checkmark"></span>
                启用备份加密
              </label>
            </div>
          )}
        </div>
      </Card>

      {/* 导出设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据导出</h3>
          <p className="card-subtitle">数据导出格式和范围设置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Select
              label="导出格式"
              value={dataManagement.export.format}
              onChange={(e) => updateSettings('dataManagement.export.format', e.target.value)}
              options={[
                { value: 'xlsx', label: 'Excel (.xlsx)' },
                { value: 'csv', label: 'CSV (.csv)' },
                { value: 'pdf', label: 'PDF (.pdf)' }
              ]}
            />
            
            <Select
              label="数据范围"
              value={dataManagement.export.dateRange}
              onChange={(e) => updateSettings('dataManagement.export.dateRange', e.target.value)}
              options={[
                { value: 'all', label: '全部数据' },
                { value: 'year', label: '最近一年' },
                { value: 'month', label: '最近一月' },
                { value: 'custom', label: '自定义范围' }
              ]}
            />
          </div>
          
          <div className="mt-lg">
            <div className="checkbox-group">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.export.includeImages}
                  onChange={(e) => updateSettings('dataManagement.export.includeImages', e.target.checked)}
                />
                <span className="checkmark"></span>
                包含图片文件
              </label>
              
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.export.compression}
                  onChange={(e) => updateSettings('dataManagement.export.compression', e.target.checked)}
                />
                <span className="checkmark"></span>
                压缩导出文件
              </label>
            </div>
          </div>
        </div>
      </Card>

      {/* 清理设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据清理</h3>
          <p className="card-subtitle">系统清理和维护工具</p>
        </div>
        <div className="card-content">
          <div className="checkbox-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.tempFiles}
                onChange={(e) => updateSettings('dataManagement.cleanup.tempFiles', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理临时文件
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.logs}
                onChange={(e) => updateSettings('dataManagement.cleanup.logs', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理系统日志
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.cache}
                onChange={(e) => updateSettings('dataManagement.cleanup.cache', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理缓存文件
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.oldBackups}
                onChange={(e) => updateSettings('dataManagement.cleanup.oldBackups', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理旧备份文件
            </label>
          </div>
        </div>
      </Card>

      {/* 设置管理 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">设置管理</h3>
          <p className="card-subtitle">导入、导出和重置系统设置</p>
        </div>
        <div className="card-content">
          <div className="settings-actions">
            <Button
              variant="primary"
              leftIcon={<Download size={16} />}
              onClick={handleExportSettings}
              disabled={exporting}
            >
              {exporting ? '导出中...' : '导出设置'}
            </Button>
            
            <Button
              variant="secondary"
              leftIcon={<Upload size={16} />}
              onClick={handleImportSettings}
              disabled={importing}
            >
              {importing ? '导入中...' : '导入设置'}
            </Button>
            
            <Button
              variant="warning"
              leftIcon={<RotateCcw size={16} />}
              onClick={handleResetSettings}
            >
              重置设置
            </Button>
          </div>
          
          <div className="mt-lg">
            <p className="text-sm text-secondary">
              <strong>注意：</strong>
              导入设置将覆盖当前所有配置，重置设置将恢复到系统默认值，这些操作无法撤销。
            </p>
          </div>
        </div>
      </Card>

      {/* 数据管理 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据管理</h3>
          <p className="card-subtitle">导入和导出应用数据</p>
        </div>
        <div className="card-content">
          <div className="settings-actions">
            <Button
              variant="primary"
              leftIcon={<Download size={16} />}
              onClick={handleExportData}
              disabled={exporting}
            >
              {exporting ? '导出中...' : '导出数据'}
            </Button>
            
            <Button
              variant="secondary"
              leftIcon={<Upload size={16} />}
              onClick={handleImportData}
              disabled={importing}
            >
              {importing ? '导入中...' : '导入数据'}
            </Button>
          </div>
          
          <div className="mt-lg">
            <p className="text-sm text-secondary">
              <strong>注意：</strong>
              数据导入将添加新数据到数据库，不会删除现有数据。数据导出包含所有来访者、个案、团体会话和沙盘工具数据。
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DataManagement;
