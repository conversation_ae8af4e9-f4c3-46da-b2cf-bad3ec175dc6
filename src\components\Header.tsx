import React, { useState, useRef, useEffect } from 'react';
import { Search, Menu, X, Minus, Square, Bell, HelpCircle } from 'lucide-react';
import './Header.css';

interface HeaderProps {
  onToggleSidebar: () => void;
  isSidebarCollapsed: boolean;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, isSidebarCollapsed }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // 快捷键：Ctrl/Cmd + K 聚焦搜索
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const isMac = /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);
      const combo = (isMac && e.metaKey && e.key.toLowerCase() === 'k') || (!isMac && e.ctrlKey && e.key.toLowerCase() === 'k');
      if (combo) {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('搜索:', searchQuery);
      // 这里可以添加搜索逻辑
    }
  };

  const handleWindowAction = (action: 'minimize' | 'maximize' | 'close') => {
    try {
      (window as any).electronAPI?.windowAction?.(action);
    } catch (_) {
      // 在浏览器开发环境下没有 electronAPI，忽略
    }
  };

  return (
    <header className="header" role="banner">
      <div className="header-left">
        <button 
          className="menu-toggle"
          onClick={onToggleSidebar}
          aria-label={isSidebarCollapsed ? '展开菜单' : '折叠菜单'}
        >
          <Menu size={20} />
        </button>
        
        <div className="brand-info" aria-label="品牌信息">
          <div className="logo-badge" aria-hidden="true">
            <img src="/logo.png" alt="Logo" className="logo-image" />
          </div>
          <div className="brand-text">
            <span className="app-title">沙盘管理系统</span>
            <span className="app-subtitle">心理健康服务平台</span>
          </div>
        </div>
      </div>

      <div className="header-center" role="search">
        <form className="search-form" onSubmit={handleSearch} aria-label="站内搜索">
          <div className="search-container">
            <Search size={18} className="search-icon" aria-hidden="true" />
            <input
              ref={inputRef}
              type="text"
              placeholder="搜索来访者、沙具..."
              aria-label="搜索来访者、沙具"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                type="button"
                className="search-clear"
                onClick={() => setSearchQuery('')}
                aria-label="清空搜索"
                title="清空"
              >
                <X size={16} />
              </button>
            )}
            {!searchQuery && (
              <div className="kbd-hint" aria-hidden="true">
                <kbd>Ctrl</kbd>
                <span>+</span>
                <kbd>K</kbd>
              </div>
            )}
          </div>
        </form>
      </div>

      <div className="header-right">
        {/* 快速操作 */}
        <div className="header-actions" role="group" aria-label="快速操作">
          <button className="action-btn" aria-label="通知" title="通知">
            <Bell size={18} />
          </button>
          <button className="action-btn" aria-label="帮助中心" title="帮助中心">
            <HelpCircle size={18} />
          </button>
        </div>

        <div className="divider" aria-hidden="true" />

        {/* 自定义窗口控制按钮 */}
        <div className="window-controls" aria-label="窗口控制" role="group">
          <button className="window-btn win-min" onClick={() => handleWindowAction('minimize')} aria-label="最小化">
            <Minus size={14} />
          </button>
          <button className="window-btn win-max" onClick={() => handleWindowAction('maximize')} aria-label="最大化或还原">
            <Square size={14} />
          </button>
          <button className="window-btn win-close" onClick={() => handleWindowAction('close')} aria-label="关闭">
            <X size={14} />
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
