/**
 * 密钥管理模块
 */

import { generateKeyPairSync } from 'crypto';
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface KeyPair {
  publicKey: string;
  privateKey: string;
}

export class KeyManager {
  private static readonly KEY_DIR = './keys';
  private static readonly PRIVATE_KEY_FILE = 'private.pem';
  private static readonly PUBLIC_KEY_FILE = 'public.pem';
  
  /**
   * 生成RSA密钥对
   */
  public static generateKeyPair(): KeyPair {
    const { publicKey, privateKey } = generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });
    
    return { publicKey, privateKey };
  }
  
  /**
   * 保存密钥对到文件
   */
  public static saveKeyPair(keyPair: KeyPair): void {
    if (!existsSync(this.KEY_DIR)) {
      mkdirSync(this.KEY_DIR, { recursive: true });
    }
    
    writeFileSync(join(this.KEY_DIR, this.PRIVATE_KEY_FILE), keyPair.privateKey);
    writeFileSync(join(this.KEY_DIR, this.PUBLIC_KEY_FILE), keyPair.publicKey);
    
    console.log('密钥对已保存到:', this.KEY_DIR);
  }
  
  /**
   * 加载私钥
   */
  public static loadPrivateKey(): string {
    const keyPath = join(this.KEY_DIR, this.PRIVATE_KEY_FILE);
    if (!existsSync(keyPath)) {
      throw new Error('私钥文件不存在，请先生成密钥对');
    }
    return readFileSync(keyPath, 'utf8');
  }
  
  /**
   * 加载公钥
   */
  public static loadPublicKey(): string {
    const keyPath = join(this.KEY_DIR, this.PUBLIC_KEY_FILE);
    if (!existsSync(keyPath)) {
      throw new Error('公钥文件不存在，请先生成密钥对');
    }
    return readFileSync(keyPath, 'utf8');
  }
}

export default KeyManager;
