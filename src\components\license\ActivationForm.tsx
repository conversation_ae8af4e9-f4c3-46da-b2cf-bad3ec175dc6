/**
 * 激活表单组件（与 loin 对齐）
 */

import React, { useState } from 'react';
import { activate } from '../../utils/auth/licenseValidator';

interface ActivationFormProps {
  onSuccess: () => void;
  onClose?: () => void;
}

const ActivationForm: React.FC<ActivationFormProps> = ({ onSuccess, onClose }) => {
  const [activationCode, setActivationCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!activationCode.trim()) {
      setError('请输入激活码');
      return;
    }
    setIsValidating(true);
    setError('');
    try {
      const success = await activate(activationCode.trim());
      if (success) {
        onSuccess();
      } else {
        setError('激活失败：激活码无效或与当前设备不匹配');
      }
    } catch (err) {
      console.error('激活过程出错:', err);
      setError('激活过程出错，请稍后重试');
    } finally {
      setIsValidating(false);
    }
  };

  const handleClear = () => {
    setActivationCode('');
    setError('');
  };

  return (
    <div className="activation-form-container">
      <div className="activation-form card-border rounded-2xl p-6">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <div className="w-10 h-10 rounded-full glass border border-green-400/30 flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                <path d="M10 17l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/>
              </svg>
            </div>
            <div>
              <span className="block">软件激活</span>
              <span className="text-sm text-white/60 font-normal">License Activation</span>
            </div>
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-white/60 hover:text-white/80 transition-colors"
              title="关闭"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="activationCode" className="block text-sm font-medium text-white/80 mb-2">
              激活码
            </label>
            <textarea
              id="activationCode"
              value={activationCode}
              onChange={(e) => setActivationCode(e.target.value)}
              placeholder="请粘贴从软件提供商处获得的激活码"
              rows={4}
              className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 resize-none font-mono text-sm"
              disabled={isValidating}
            />
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="p-3 bg-red-500/10 border border-red-400/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-red-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="text-red-300 text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* 按钮组 */}
          <div className="flex space-x-3 pt-2">
            <button
              type="submit"
              disabled={isValidating || !activationCode.trim()}
              className="flex-1 flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
            >
              {isValidating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  验证中...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  激活软件
                </>
              )}
            </button>
            <button
              type="button"
              onClick={handleClear}
              disabled={isValidating}
              className="px-4 py-3 bg-white/5 hover:bg-white/10 disabled:bg-white/5 disabled:cursor-not-allowed text-white/70 hover:text-white border border-white/20 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20"
            >
              清空
            </button>
          </div>
        </form>

        {/* 提示信息 */}
        <div className="mt-6 p-4 bg-blue-500/10 border border-blue-400/20 rounded-lg">
          <div className="flex items-start space-x-3">
            <svg className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div className="text-xs text-blue-300">
              <p className="font-medium mb-1">激活说明：</p>
              <ul className="space-y-1 text-white/70">
                <li>• 激活码与设备硬件绑定，仅限当前设备使用</li>
                <li>• 激活成功后软件将永久可用，无需联网验证</li>
                <li>• 如需在其他设备使用，请重新获取激活码</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivationForm;

