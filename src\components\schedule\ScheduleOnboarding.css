/* 日程安排引导样式，复用共通结构 */
.schedule-onboarding-overlay { position:fixed; inset:0; background:rgba(17,24,39,.55); backdrop-filter:blur(2px); z-index:9999; font-family:var(--font-sans,system-ui); }
.schedule-onboarding-hole { position:fixed; pointer-events:none; box-shadow:0 0 0 9999px rgba(17,24,39,.55); border-radius:10px; transition:all .28s cubic-bezier(.4,0,.2,1); }
.schedule-onboarding-tooltip { position:absolute; max-width:360px; background:#fff; color:#1e293b; border-radius:12px; padding:16px 20px 14px; box-shadow:0 8px 32px -4px rgba(0,0,0,.25),0 2px 8px rgba(0,0,0,.08); font-size:14px; line-height:1.55; animation:fadeIn .25s ease; }
.schedule-onboarding-tooltip:after { content:""; position:absolute; width:12px; height:12px; background:#fff; transform:rotate(45deg); box-shadow:0 2px 6px rgba(0,0,0,.08); z-index:-1; }
.schedule-onboarding-tooltip[data-arrow-pos='top']:after { bottom:-6px; left:32px; }
.schedule-onboarding-tooltip[data-arrow-pos='bottom']:after { top:-6px; left:32px; }
.schedule-onboarding-tooltip[data-arrow-pos='left']:after { right:-6px; top:28px; }
.schedule-onboarding-tooltip[data-arrow-pos='right']:after { left:-6px; top:28px; }
.so-header { display:flex; justify-content:space-between; align-items:center; gap:12px; }
.so-title { margin:0; font-size:15px; font-weight:600; letter-spacing:.5px; }
.so-step { font-size:12px; color:#64748b; }
.so-progress { position:relative; height:4px; background:#e2e8f0; border-radius:2px; margin:10px 0 4px; overflow:hidden; }
.so-progress span { position:absolute; inset:0; background:linear-gradient(90deg,#0ea5e9,#2563eb); width:0; transition:width .3s ease; }
.so-body { margin-top:4px; }
.so-footer { display:flex; justify-content:space-between; align-items:center; gap:12px; margin-top:10px; flex-wrap:wrap; }
.so-actions { display:flex; gap:8px; }
.so-actions button { font-size:13px; line-height:1; padding:6px 14px; border-radius:6px; border:1px solid #cbd5e1; background:#fff; cursor:pointer; transition:.15s; display:inline-flex; align-items:center; }
.so-actions button.primary { background:#0ea5e9; border-color:#0ea5e9; color:#fff; }
.so-actions button:hover { box-shadow:0 2px 6px rgba(0,0,0,.12); transform:translateY(-1px); }
.so-actions button:active { transform:translateY(0); }
.so-dismiss { display:flex; gap:6px; align-items:center; font-size:12px; color:#475569; cursor:pointer; }
.so-dismiss input { width:14px; height:14px; }
.schedule-onboarding-steps { display:flex; gap:4px; margin-top:4px; justify-content:flex-end; }
.schedule-onboarding-steps span { width:6px; height:6px; border-radius:50%; background:#e2e8f0; transition:.25s; }
.schedule-onboarding-steps span.active { background:#0ea5e9; transform:scale(1.4); }
.schedule-onboarding-center:after { display:none; }
@keyframes fadeIn { from { opacity:0; transform:translateY(4px);} to { opacity:1; transform:translateY(0);} }
@media (max-width:560px){ .schedule-onboarding-tooltip{ max-width:calc(100vw - 24px);} .so-footer{ flex-direction:column; align-items:stretch;} .so-actions{ width:100%; justify-content:space-between;} }
