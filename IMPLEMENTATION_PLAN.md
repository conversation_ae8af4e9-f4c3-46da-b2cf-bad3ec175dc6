# Implementation Plan

这个文件记录当前正在实施的开发计划和阶段。

## ✅ 已完成的主要功能模块
- ✅ 来访者管理系统
- ✅ 个案管理系统  
- ✅ 日程安排系统
- ✅ 沙具管理系统
- ❌ 团沙管理系统 (已停用)
- ✅ 数据统计系统
- ✅ 帮助中心系统
- ✅ 数据库迁移系统
- ✅ 桌面端数据持久化

## 📋 代码清理记录 (2025年9月5日)

### 已移除的功能模块：
- ❌ **今日工作台功能** - 包含每日心语、树洞功能等，因需求变更已完全移除
- ❌ **心理树洞功能** - 数据库表结构和相关服务已清理
- ❌ **每日短语功能** - 服务层、脚本文件和数据初始化已移除
- ❌ **相关模拟数据** - mockDailyWorkspace.ts等测试文件已删除

### 清理内容：
1. **数据库层面**：
   - 移除树洞相关表创建和索引
   - 移除每日短语数据初始化
   - 保留方法签名以维持兼容性

2. **服务层面**：
   - 删除 `dailyMessageService.ts`
   - 清理 `migrationService.ts` 中相关代码
   - 更新服务导出索引

3. **数据和脚本**：
   - 删除 `mockDailyWorkspace.ts`
   - 删除每日消息管理脚本
   - 删除功能设计文档

### 保留的核心功能：
- ✅ 快速备注功能 (notesService)
- ✅ 设置管理功能
- ✅ 预约管理系统
- ✅ 所有主要业务模块

## Stage 3: 数据集成与交互 ✅
**目标**: 连接服务层，实现数据显示和交互
**成功标准**: 实时数据显示，用户交互功能正常
**状态**: Completed

### 已完成：
- ✅ 倒计时更新 (每秒)
- ✅ 预约状态实时同步
- ✅ 每日心语刷新机制
- ✅ 快速备注增删改查
- ✅ 今日预约统计和显示
- ✅ 基于日期seed的稳定随机心语选择
- ✅ 用户override机制（一键刷新）

## Stage 4: 集成到Dashboard ✅
**目标**: 将今日工作台集成到Dashboard页面
**成功标准**: 页面布局协调，功能无冲突
**状态**: Completed

### 已完成：
- ✅ 集成 DailyWorkspaceSection 到 Dashboard.tsx
- ✅ 轮播图下方布局完成
- ✅ 保持整体设计风格一致
- ✅ 导航功能完整对接

## Stage 5: 测试与优化 🚧
**目标**: 功能测试和性能优化
**成功标准**: 所有功能稳定，性能良好
**状态**: In Progress

### 当前状态：
- ✅ 开发环境浏览器兼容性（模拟API）
- ❌ Electron桌面环境运行时错误（需要调试）
- ⏳ 功能测试进行中
- ⏳ 性能优化待定

### 发现的问题：
1. Electron启动时出现native模块错误
2. 数据库迁移在桌面环境下正常运行
3. 组件在浏览器环境下可以正常渲染

### 下一步：
- 调试Electron运行时问题
- 完整功能测试
- 边界条件测试

---

### 已完成的主要功能模块：
- ✅ 来访者管理系统
- ✅ 个案管理系统  
- ✅ 日程安排系统
- ✅ 沙具管理系统
- ❌ 团沙管理系统 (已停用)
- ✅ 数据统计系统
- ✅ 帮助中心系统
- ✅ 数据库迁移系统
- ✅ 桌面端数据持久化

### 技术架构：
- 前端：React 19 + TypeScript + Vite 7
- 桌面框架：Electron 
- 数据库：SQLite3 (桌面端)
- 样式：CSS3 模块化
- 状态管理：React Hooks

## Help Center Automation Pipeline (新增)
**目标**: 在不中断现有功能的前提下分步自动完成帮助中心 Stage4~6。

### 流水线阶段
| 阶段 | 说明 | 触发条件 | 退出条件 |
| ---- | ---- | -------- | -------- |
| HC-4A | Article 基础组件抽离 | 计划文件标记进入 | 组件替换无编译错误 |
| HC-4B | TOC/锚点实现 | 4A 成功 | TOC 渲染 & 点击跳转正常 |
| HC-4C | 键盘导航 & 可访问性 | 4B 成功 | 快捷键测试通过 |
| HC-5 | SQLite 持久化映射 | 4C 成功 | CRUD + 懒加载验证 |
| HC-6 | 全局搜索缓存/快捷键 | 5 成功 | Ctrl+K 打开搜索面板 |

### 执行守则
1. 每阶段 <= 200 行新增代码（不含文档）。
2. 发现样式偏差优先记备注，集中到视觉统一阶段再处理。
3. 若 3 次失败：记录失败原因 + 立即停止继续自动阶段。
4. 所有新增 Hook 要求：
   - 具备最小返回类型定义
   - 错误路径返回空结构而非抛出（除非不可逆）

### 验证脚本 (计划)
- 添加脚本 scripts/test-help-center.cjs 验证：
  - Provider 加载顺序
  - 索引构建耗时 < 50ms (mock 数据)
  - TOC 节点数 >= 2
  - 步骤渲染顺序正确

## Stage 6: Licensing UI 优化（进行中）
**Goal**: 改善“设备码 / 激活文件”两个授权相关弹窗的可用性与信息层次（第一轮迭代）。
**Success Criteria**:
- 设备码弹窗高度更紧凑；设备码分段显示且一眼可复制
- 激活文件弹窗：步骤指示（Step 1 选择文件 / Step 2 激活）显示，根据状态高亮当前步骤
- 说明文本默认折叠，点击可展开；不影响主操作视线焦点
- 操作按钮与输入区域关联更紧（激活按钮靠近文件区域或其下方）
- 复制 / 激活操作都有成功反馈（2 秒内自动消失或状态复原）
**Tests**:
- 未选择文件时“激活软件”按钮禁用
- 选择合法 .lic 文件后按钮可用并显示 Step 2 高亮
- 说明区域初始折叠（DOM 中存在但高度折叠）点击“展开说明”后可见
- 设备码显示包含至少 4 个分组（以 '-' 分隔），复制后显示“已复制”状态 2 秒
**Status**: In Progress

### Subtasks (Iteration 1)
- [x] 重构 FileActivationForm 头部下方添加步骤条
- [x] 缩减上传区域内边距 (p-8 -> p-6) 并添加内联激活操作区
- [x] 折叠说明模块 + 展开按钮
- [x] DeviceCodeModal 分段渲染 + 行内复制按钮 + 折叠说明
- [x] 添加统一 toast / inline banner 组件（基础 ToastProvider 已接入根组件）
- [x] 抽象 CollapsibleHelp 组件复用两处授权说明
- [ ] 第二轮微调视觉（间距 / 颜色统一）

### Notes (Iteration 1 完成情况)
- 新增: src/components/ui/Toast.tsx + Toast.css（轻量，无外部依赖）
- 接入点: App.tsx 中用 <ToastProvider> 包裹 Layout
- 使用点: FileActivationForm（激活成功/失败）、DeviceCodeModal（复制/重新生成）
- 待办: 将错误 inline 区域与 toast 风格统一；抽象 CollapsibleHelp 复用；统一按钮高度 (32/40) 及色板 token
