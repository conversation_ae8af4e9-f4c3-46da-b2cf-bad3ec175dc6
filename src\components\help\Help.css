/* 帮助中心样式 */
@import '../../styles/variables.css';

.help-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 搜索栏样式 */
.help-search-bar {
  margin-bottom: var(--spacing-3xl);
}

.help-search-input {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

.help-search-input input {
  width: 100%;
  padding-left: 44px;
  padding-right: 44px;
  font-size: var(--text-base);
  height: 48px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius);
  background: var(--bg-primary);
  transition: border-color var(--transition-fast);
}

.help-search-input input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px;
  transition: color var(--transition-fast);
}

.search-clear:hover {
  color: var(--danger-red);
}

.search-results-summary {
  text-align: center;
  margin-top: var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* --- Stage3 布局与排版精修 --- */
.help-layout { grid-template-columns: 240px 1fr; }
.help-sidebar { width:240px; }
@media (max-width:1200px){ .help-layout { grid-template-columns: 228px 1fr; } .help-sidebar{ width:228px; } }
@media (max-width:960px){ .help-layout { grid-template-columns: 1fr; } .help-sidebar { width:100%; } }

/* 主内容区左右留白，避免贴边 */
.help-content { padding: 0 32px 80px 24px; }
@media (max-width:960px){ .help-content { padding: 0 16px 56px; } }

/* 文章容器内部宽度限制与居中 */
.help-content-container { padding:0; }
.help-content-container .help-article-header,
.help-content-container .help-content-body,
.help-content-container .help-related-content { max-width: 760px; margin:0 auto; }
.help-content-container .help-content-body { padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-2xl); }
.help-content-container .help-article-header { padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-md); }
.help-related-content { padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-2xl); }

/* 行长再收敛：正文文字区域 680px 更易读 */
.article-body { max-width: 680px; }
.help-related-content.article-body { max-width:680px; }

/* 面包屑与标题间距调整 */
.help-article-title { margin-top: var(--spacing-sm); }
.help-article-intro { margin-top: var(--spacing-sm); }

/* 列表排版优化 */
.article-body ul, .article-body ol { margin: 0 0 1em 1.25em; padding:0; line-height:1.6; }
.article-body li { margin: 0 0 .4em; }

/* Onboarding 与正文在同一视觉栅格居中 */
.help-main { max-width: 860px; }
.onboarding-wrapper, .help-search-results-panel { max-width: 860px; margin:0 auto var(--spacing-2xl); }

/* 搜索结果面板内列表统一内边距 */
.help-search-results-panel .results-list { padding: 4px 0; }
.search-result-row { padding:10px 18px; }

/* 侧栏视觉收紧 */
.navigation-category { margin-bottom: var(--spacing-lg); }
.category-header { padding: 12px 16px; }
.category-content { padding: 12px 14px 16px; }
.navigation-section-simplified { margin-bottom: var(--spacing-lg); }
.section-header-simplified { padding:12px 14px; }
.section-content-direct { padding:8px 8px 10px; }
.navigation-item { padding:6px 8px; font-size:13px; }
.item-title { font-size:13px; }
.item-tag { font-size:11px; }

/* 标题层级视觉层次强化 */
.article-body h2, .article-body h3 { margin: 2.2em 0 .9em; line-height:1.3; font-weight:600; }
.article-body h2 { font-size:20px; }
.article-body h3 { font-size:17px; }

/* 分隔增强：章节列表 */
.overview-item, .overview-section { padding:14px 16px; }

/* 快速链接网格宽度与正文对齐 */
.onboarding-quick-links { max-width:860px; margin:0 auto; }
.quick-link { font-size:12.5px; padding:8px 10px; }

/* 可读性：减少字间过密 */
.help-content-container, .article-body { letter-spacing: .2px; }

/* 减少垂直碎片：相关内容上边距 */
.help-related-content { margin-top: var(--spacing-xl); }

/* 滚动体验：侧栏最大高度移除，滚动交给页面 */
.help-sidebar { max-height:none; overflow:auto; }

/* 支持渠道卡片布局 */
.help-support-grid {
  display: grid;
  grid-template-columns: 340px repeat(auto-fit,minmax(250px,1fr));
  align-items: start;
  gap: 28px;
  padding: 24px 8px 64px;
}

.support-card {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 14px;
  padding: 20px 20px 22px;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 2px 4px -2px rgba(0,0,0,.06), 0 4px 12px -4px rgba(0,0,0,.04);
  transition: box-shadow .25s, transform .25s, border-color .25s;
  min-height: 340px;
}
.support-card:hover {
  box-shadow: 0 4px 10px -2px rgba(0,0,0,.10), 0 8px 20px -6px rgba(0,0,0,.06);
  transform: translateY(-2px);
  border-color: #cbd5e1;
}

.card-header { display:flex; align-items:center; gap:10px; margin-bottom:8px; }
.card-header h3 { margin:0; font-size:16px; font-weight:600; letter-spacing:.3px; }
.card-icon { width:38px; height:38px; display:flex; align-items:center; justify-content:center; border-radius:10px; background: linear-gradient(135deg,#f1f5f9,#e2e8f0); color:#334155; box-shadow: inset 0 0 0 1px #cbd5e1; }
.card-icon.qr { background: linear-gradient(135deg,#eef2ff,#e0e7ff); color:#4338ca; box-shadow: inset 0 0 0 1px #c7d2fe; }
.card-icon.phone { background: linear-gradient(135deg,#ecfdf5,#d1fae5); color:#047857; box-shadow: inset 0 0 0 1px #a7f3d0; }
.card-icon.web { background: linear-gradient(135deg,#f0f9ff,#e0f2fe); color:#0369a1; box-shadow: inset 0 0 0 1px #bae6fd; }
.card-icon.support { background: linear-gradient(135deg,#fdf2f8,#fce7f3); color:#be185d; box-shadow: inset 0 0 0 1px #fbcfe8; }

.card-desc { margin:0 0 16px; font-size:13px; line-height:1.55; color:#475569; min-height:42px; }

.qr-card { grid-row: span 2; }
.qr-wrapper { 
  max-width: 150px; 
  margin: 16px auto; 
  aspect-ratio: 1/1; 
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-image { 
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 4px;
  border-radius: 8px;
}

.phone-block { display:flex; align-items:center; justify-content:space-between; gap:12px; background:#f8fafc; border:1px solid #e2e8f0; border-radius:10px; padding:10px 14px; margin-bottom:12px; }
.phone-number { font-weight:600; letter-spacing:.5px; font-size:15px; color:#0f172a; }

.action-row { margin: 4px 0 12px; }
.action-row .btn { display:inline-flex; align-items:center; gap:6px; }

.hint-text { font-size:11px; color:#64748b; line-height:1.4; }

.btn { cursor:pointer; border:none; border-radius:8px; font-size:13px; font-weight:500; line-height:1; padding:9px 14px; display:inline-flex; align-items:center; gap:6px; letter-spacing:.3px; }
.btn.primary { background:#2563eb; color:#fff; box-shadow:0 2px 4px -2px rgba(37,99,235,.5), 0 4px 10px -4px rgba(37,99,235,.4); }
.btn.primary:hover { background:#1d4ed8; }
.btn.secondary { background:#db2777; color:#fff; box-shadow:0 2px 4px -2px rgba(219,39,119,.5),0 4px 10px -4px rgba(219,39,119,.4); }
.btn.secondary:hover { background:#be185d; }
.btn.ghost { background:#fff; color:#334155; border:1px solid #cbd5e1; }
.btn.ghost:hover { background:#f1f5f9; }

.btn:active { transform:translateY(1px); }

/* 响应式 */
@media (max-width:780px){
  .help-support-grid { grid-template-columns: 1fr 1fr; gap:20px; }
}
@media (max-width:560px){
  .help-support-grid { grid-template-columns:1fr; }
  .card-desc { min-height:auto; }
}
@media (max-width:1080px){
  .help-support-grid { grid-template-columns: repeat(auto-fit,minmax(260px,1fr)); }
  .qr-card { grid-row:auto; }
  .qr-wrapper { max-width:140px; }
}
@media (max-width:560px){ .qr-wrapper { max-width:140px; } }

.two-by-two { grid-template-columns: repeat(2, 1fr); }
.two-by-two .support-card { height: 100%; min-height: 340px; display:flex; flex-direction:column; }
.card-body { flex:1; display:flex; flex-direction:column; }
.card-footer { margin-top:12px; border-top:1px dashed #e2e8f0; padding-top:10px; }
.qr-wrapper.small { 
  max-width: 140px; 
  margin: 12px auto; 
  display: flex;
  align-items: center;
  justify-content: center;
}
.mini-list { margin:8px 0 0 0; padding:0 0 0 16px; font-size:11.5px; line-height:1.5; color:#64748b; }
.mini-list li { margin:2px 0; }
@media (max-width:960px){ .two-by-two { grid-template-columns: 1fr; } .two-by-two .support-card { min-height:unset; } }
