import React, { useState, useEffect } from 'react';
import { caseService } from '../../services/caseService';
import type { SimpleCase } from '../../types/case';
import type { Visitor } from '../../types/visitor';
import {
  Card,
  CardContent,
  Button,
  Badge,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  PageContainer,
  PageHeader,
  LoadingSpinner,
  EmptyState,
  FilterBar,
  Pagination
} from '../ui';
import { 
  Plus, 
  Search, 
  FileX, 
  Eye, 
  Edit, 
  Trash2, 
  CheckSquare, 
  Square, 
  AlertTriangle, 
  Clock, 
  Calendar,
  FileText,
  Star,
  User
} from 'lucide-react';
import CreateCaseModal from './CreateCaseModal';
import CaseDetailModal from './CaseDetailModal';
import EditCaseModal from './EditCaseModal';
import CaseOnboarding from './CaseOnboarding';
import './ProfessionalCases.css';

const ProfessionalCases: React.FC = () => {
  const [cases, setCases] = useState<(SimpleCase & { visitor?: Visitor })[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCase, setSelectedCase] = useState<SimpleCase | null>(null);
  const [selectedCaseIds, setSelectedCaseIds] = useState<Set<string>>(new Set());

  // 筛选状态
  const [filters, setFilters] = useState({
    search: '',
    crisis: '',
    progress: '',
    therapyMethod: '',
    timeRange: ''
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);

  // 加载个案数据
  const loadCases = async () => {
    try {
      setLoading(true);
      const casesWithVisitor = await caseService.getAllCasesWithVisitor();
      setCases(casesWithVisitor);
    } catch (error) {
      console.error('加载个案数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCases();
  }, []);

  // 筛选逻辑
  const filteredCases = cases.filter(caseItem => {
    const matchesSearch = caseItem.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                         caseItem.summary.toLowerCase().includes(filters.search.toLowerCase());
    
    const matchesCrisis = !filters.crisis || caseItem.crisis === filters.crisis;
    const matchesProgress = !filters.progress || caseItem.progress === filters.progress;
    const matchesTherapy = !filters.therapyMethod || caseItem.therapyMethod === filters.therapyMethod;
    
    // 时间范围筛选
    let matchesTime = true;
    if (filters.timeRange && caseItem.lastDate) {
      const daysDiff = getDaysSinceLastSession(caseItem.lastDate);
      switch (filters.timeRange) {
        case 'recent':
          matchesTime = daysDiff <= 7;
          break;
        case 'overdue':
          matchesTime = daysDiff > 14;
          break;
        case 'thisWeek':
          matchesTime = daysDiff <= 7;
          break;
      }
    }

    return matchesSearch && matchesCrisis && matchesProgress && matchesTherapy && matchesTime;
  });

  // 分页计算
  const totalItems = filteredCases.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const currentPageCases = filteredCases.slice(startIndex, startIndex + pageSize);

  // 选择逻辑
  const handleSelectCase = (caseId: string) => {
    const newSelected = new Set(selectedCaseIds);
    if (newSelected.has(caseId)) {
      newSelected.delete(caseId);
    } else {
      newSelected.add(caseId);
    }
    setSelectedCaseIds(newSelected);
  };

  const handleSelectAll = () => {
    const allFilteredIds = filteredCases.map(c => c.id);
    const allFilteredSelected = allFilteredIds.every(id => selectedCaseIds.has(id));
    
    if (allFilteredSelected) {
      // 取消选择所有过滤后的项目
      const newSet = new Set(selectedCaseIds);
      allFilteredIds.forEach(id => newSet.delete(id));
      setSelectedCaseIds(newSet);
    } else {
      // 选择所有过滤后的项目
      const newSet = new Set(selectedCaseIds);
      allFilteredIds.forEach(id => newSet.add(id));
      setSelectedCaseIds(newSet);
    }
  };

  // 操作函数
  const handleViewCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setShowDetailModal(true);
  };

  const handleEditCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setShowEditModal(true);
  };

  const handleDeleteCase = async (caseId: string) => {
    if (confirm('确定要删除这个个案吗？此操作不可恢复。')) {
      try {
        await caseService.deleteCase(caseId);
        await loadCases();
      } catch (error) {
        console.error('删除个案失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  // 批量操作
  const handleBatchDelete = async () => {
    if (selectedCaseIds.size === 0) return;
    
    if (confirm(`确定要删除选中的 ${selectedCaseIds.size} 个个案吗？此操作不可恢复。`)) {
      try {
        await Promise.all(Array.from(selectedCaseIds).map(id => caseService.deleteCase(id)));
        setSelectedCaseIds(new Set());
        await loadCases();
      } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败，请重试');
      }
    }
  };

  // 工具函数
  const getDaysSinceLastSession = (lastDate: string): number => {
    const today = new Date();
    const lastSessionDate = new Date(lastDate);
    const timeDiff = today.getTime() - lastSessionDate.getTime();
    return Math.floor(timeDiff / (1000 * 3600 * 24));
  };

  const formatLastSession = (lastDate?: string): { text: string; isOverdue: boolean } => {
    if (!lastDate) return { text: '未开始', isOverdue: false };
    
    const days = getDaysSinceLastSession(lastDate);
    const isOverdue = days > 14;
    
    if (days === 0) return { text: '今天', isOverdue };
    if (days === 1) return { text: '昨天', isOverdue };
    if (days <= 7) return { text: `${days}天前`, isOverdue };
    if (days <= 30) return { text: `${days}天前`, isOverdue };
    
    return { text: `${Math.floor(days / 30)}月前`, isOverdue };
  };

  const getCrisisBadge = (crisis?: string) => {
    const crisisMap = {
      '⚠️': { text: '高危', variant: 'danger' as const },
      '⚡': { text: '关注', variant: 'warning' as const },
      '✅': { text: '安全', variant: 'success' as const }
    };
    
    const crisisInfo = crisisMap[crisis as keyof typeof crisisMap];
    if (!crisisInfo) return <Badge variant="gray">未评估</Badge>;
    
    return (
      <Badge variant={crisisInfo.variant} className="crisis-badge">
        {crisisInfo.text}
      </Badge>
    );
  };

  const getProgressBadge = (progress?: string) => {
    const progressMap = {
      '⬆️': { text: '改善', variant: 'success' as const },
      '➡️': { text: '稳定', variant: 'primary' as const },
      '⬇️': { text: '恶化', variant: 'danger' as const }
    };
    
    const progressInfo = progressMap[progress as keyof typeof progressMap];
    if (!progressInfo) return <Badge variant="gray">待评估</Badge>;
    
    return (
      <Badge variant={progressInfo.variant} className="progress-badge">
        {progressInfo.text}
      </Badge>
    );
  };

  // 统计数据
  const stats = {
    total: cases.length,
    highRisk: cases.filter(c => c.crisis === '⚠️').length,
    needAttention: cases.filter(c => c.crisis === '⚡').length,
    overdue: cases.filter(c => c.lastDate && getDaysSinceLastSession(c.lastDate) > 14).length,
    improving: cases.filter(c => c.progress === '⬆️').length
  };

  return (
    <PageContainer>
      <CaseOnboarding />
      <PageHeader 
        title="个案管理" 
        subtitle="专业心理咨询个案跟踪与管理"
        className="professional-cases-header"
        actions={
          <div className="flex gap-2">
            <Button
              variant="secondary"
              leftIcon={<FileText size={16} />}
              onClick={() => {/* 导出报告功能 */}}
            >
              导出报告
            </Button>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={() => setShowCreateModal(true)}
            >
              新建个案
            </Button>
          </div>
        }
      />

      {/* 统计概览 */}
      <div className="stats-overview">
        <Card>
          <CardContent className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">{stats.total}</div>
              <div className="stat-label">总个案数</div>
            </div>
            <div className="stat-item danger">
              <div className="stat-number">{stats.highRisk}</div>
              <div className="stat-label">
                <AlertTriangle size={14} />
                高危个案
              </div>
            </div>
            <div className="stat-item warning">
              <div className="stat-number">{stats.needAttention}</div>
              <div className="stat-label">需要关注</div>
            </div>
            <div className="stat-item info">
              <div className="stat-number">{stats.overdue}</div>
              <div className="stat-label">
                <Clock size={14} />
                超期个案
              </div>
            </div>
            <div className="stat-item success">
              <div className="stat-number">{stats.improving}</div>
              <div className="stat-label">改善中</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 筛选栏 */}
      <FilterBar 
        className="professional-filters"
        searchProps={{
          value: filters.search,
          onChange: (e) => setFilters({ ...filters, search: e.target.value }),
          placeholder: "搜索来访者姓名或问题描述...",
          leftIcon: <Search size={16} />
        }}
        filters={[
          {
            value: filters.crisis,
            onChange: (e) => setFilters({ ...filters, crisis: e.target.value }),
            options: [
              { value: "", label: "全部等级" },
              { value: "⚠️", label: "⚠️ 高危" },
              { value: "⚡", label: "⚡ 需关注" },
              { value: "✅", label: "✅ 安全" }
            ]
          },
          {
            value: filters.progress,
            onChange: (e) => setFilters({ ...filters, progress: e.target.value }),
            options: [
              { value: "", label: "全部进展" },
              { value: "⬆️", label: "⬆️ 改善" },
              { value: "➡️", label: "➡️ 稳定" },
              { value: "⬇️", label: "⬇️ 恶化" }
            ]
          },
          {
            value: filters.therapyMethod,
            onChange: (e) => setFilters({ ...filters, therapyMethod: e.target.value }),
            options: [
              { value: "", label: "全部方法" },
              { value: "箱庭疗法", label: "箱庭疗法" },
              { value: "认知行为疗法", label: "认知行为疗法" },
              { value: "人本主义疗法", label: "人本主义疗法" },
              { value: "精神动力学疗法", label: "精神动力学疗法" }
            ]
          },
          {
            value: filters.timeRange,
            onChange: (e) => setFilters({ ...filters, timeRange: e.target.value }),
            options: [
              { value: "", label: "全部时间" },
              { value: "recent", label: "近期咨询" },
              { value: "overdue", label: "超期未来" },
              { value: "thisWeek", label: "本周咨询" }
            ]
          }
        ]}
      />

      {/* 批量操作工具栏 */}
      {selectedCaseIds.size > 0 && (
        <Card className="batch-toolbar">
          <CardContent>
            <div className="flex justify-between items-center">
              <div className="text-sm">
                已选择 <strong>{selectedCaseIds.size}</strong> 个个案
              </div>
              <div className="flex gap-2">
                <Button
                  variant="danger"
                  size="sm"
                  leftIcon={<Trash2 size={14} />}
                  onClick={handleBatchDelete}
                >
                  批量删除
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 个案列表 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner size="lg" />
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : currentPageCases.length === 0 ? (
            <EmptyState
              icon={<FileX size={48} />}
              title="暂无个案数据"
              description="还没有任何个案信息，开始创建您的第一个个案吧。"
              action={
                <Button
                  leftIcon={<Plus size={16} />}
                  onClick={() => setShowCreateModal(true)}
                >
                  新建个案
                </Button>
              }
            />
          ) : (
            <Table className="professional-cases-table">
              <TableHeader>
                <TableRow>
                  <TableCell isHeader style={{ width: '50px' }}>
                    <button
                      onClick={handleSelectAll}
                      className="select-button"
                      title={filteredCases.length > 0 && filteredCases.every(c => selectedCaseIds.has(c.id)) ? '取消全选' : '全选所有'}
                    >
                      {filteredCases.length > 0 && filteredCases.every(c => selectedCaseIds.has(c.id)) ? (
                        <CheckSquare size={16} />
                      ) : (
                        <Square size={16} />
                      )}
                    </button>
                  </TableCell>
                  <TableCell isHeader>来访者</TableCell>
                  <TableCell isHeader>问题类型</TableCell>
                  <TableCell isHeader>治疗方法</TableCell>
                  <TableCell isHeader>危机等级</TableCell>
                  <TableCell isHeader>治疗进展</TableCell>
                  <TableCell isHeader>最后咨询</TableCell>
                  <TableCell isHeader>下次预约</TableCell>
                  <TableCell isHeader>总次数</TableCell>
                  <TableCell isHeader>操作</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageCases.map(caseItem => {
                  const lastSession = formatLastSession(caseItem.lastDate);
                  return (
                    <TableRow 
                      key={caseItem.id}
                      className={`
                        ${selectedCaseIds.has(caseItem.id) ? 'selected' : ''}
                        ${lastSession.isOverdue ? 'overdue-row' : ''}
                        ${caseItem.crisis === '⚠️' ? 'high-risk-row' : ''}
                      `}
                    >
                      <TableCell>
                        <button
                          onClick={() => handleSelectCase(caseItem.id)}
                          className="select-button"
                        >
                          {selectedCaseIds.has(caseItem.id) ? (
                            <CheckSquare size={16} className="text-blue-600" />
                          ) : (
                            <Square size={16} />
                          )}
                        </button>
                      </TableCell>
                      <TableCell>
                        <div className="visitor-info">
                          <div className="visitor-name">
                            <User size={14} />
                            {caseItem.name}
                            {caseItem.star && <Star size={12} className="star-icon" />}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="problem-summary" title={caseItem.summary}>
                          {caseItem.summary}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="gray" className="therapy-method">
                          {caseItem.therapyMethod}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {getCrisisBadge(caseItem.crisis)}
                      </TableCell>
                      <TableCell>
                        {getProgressBadge(caseItem.progress)}
                      </TableCell>
                      <TableCell>
                        <div className={`last-session ${lastSession.isOverdue ? 'overdue' : ''}`}>
                          {lastSession.text}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="next-appointment">
                          {caseItem.nextDate ? (
                            <>
                              <Calendar size={12} />
                              {new Date(caseItem.nextDate).toLocaleDateString()}
                            </>
                          ) : (
                            <span className="no-appointment">未安排</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="session-count">
                          第 {caseItem.total} 次
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="action-buttons">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCase(caseItem)}
                            title="查看详情"
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCase(caseItem)}
                            title="编辑个案"
                          >
                            <Edit size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCase(caseItem.id)}
                            title="删除个案"
                            className="delete-button"
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}

          {/* 分页组件 */}
          {filteredCases.length > 0 && (
            <div className="pagination-wrapper">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                showTotal={true}
                total={totalItems}
                pageSize={pageSize}
                onPageSizeChange={setPageSize}
                pageSizeOptions={[10, 15, 20, 30]}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 模态框 */}
      {showCreateModal && (
        <CreateCaseModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={async (newCase) => {
            await caseService.createCase(newCase);
            setShowCreateModal(false);
            loadCases();
          }}
        />
      )}

      {showDetailModal && selectedCase && (
        <CaseDetailModal
          isOpen={showDetailModal}
          case={selectedCase}
          onClose={() => setShowDetailModal(false)}
          onEdit={() => {
            setShowDetailModal(false);
            setShowEditModal(true);
          }}
        />
      )}

      {showEditModal && selectedCase && (
        <EditCaseModal
          isOpen={showEditModal}
          case={selectedCase}
          onClose={() => setShowEditModal(false)}
          onUpdate={async (updatedCase) => {
            await caseService.updateCase(updatedCase.id, updatedCase);
            setShowEditModal(false);
            loadCases();
          }}
        />
      )}
    </PageContainer>
  );
};

export default ProfessionalCases;
