// 纯SQLite数据管理器 - 专业版只使用SQLite
import type { SimpleCase, HomeworkStatus, Progress, TherapyMethod, CrisisLevel } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';
import type { SandTool } from '../types/sandtool';
import type { 
  Appointment, 
  AppointmentType, 
  AppointmentStatus, 
  UrgencyLevel 
} from '../types/schedule';
import type { SandToolCondition, SandToolCategory, SandToolSize } from '../types/sandtool';
import type { AppSettings } from '../types/settings';

class SQLiteDataManager {
  
  // 环境检查 - 只支持桌面环境
  isElectronEnvironment(): boolean {
    const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
    if (!isElectron) {
      throw new Error('此应用只支持桌面环境运行，请使用桌面版本');
    }
    return true;
  }

  // 数据库查询封装
  private async query(sql: string, params: unknown[] = []): Promise<unknown[]> {
    this.isElectronEnvironment();
    try {
      return await window.electronAPI!.dbQuery(sql, params);
    } catch (error) {
      console.error('数据库查询失败:', error);
      return []; // 返回空数组而不是抛出错误
    }
  }

  private async run(sql: string, params: unknown[] = []): Promise<unknown> {
    this.isElectronEnvironment();
    try {
      return await window.electronAPI!.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      return { changes: 0, lastInsertRowid: null }; // 返回默认结果
    }
  }

  // 公共查询方法，供其他服务使用
  async executeQuery(sql: string, params: unknown[] = []): Promise<unknown[]> {
    return this.query(sql, params);
  }

  async executeRun(sql: string, params: unknown[] = []): Promise<unknown> {
    return this.run(sql, params);
  }

  // ==================== 访客管理 ====================
  async getAllVisitors(): Promise<Visitor[]> {
    const rows = await this.query('SELECT * FROM visitors ORDER BY created_at DESC');
    return rows.map(row => this.mapRowToVisitor(row as Record<string, unknown>));
  }

  async getVisitor(id: string): Promise<Visitor | null> {
    const rows = await this.query('SELECT * FROM visitors WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToVisitor(rows[0] as Record<string, unknown>) : null;
  }

  async saveVisitor(visitor: Visitor): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO visitors (
        id, name, age, gender, phone, email, emergency_contact, 
        emergency_phone, occupation, education, address, notes, 
        status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      visitor.id,
      visitor.name,
      visitor.age,
      visitor.gender,
      visitor.phone || '',
      visitor.email || '',
      visitor.emergencyContact || '',
      visitor.emergencyPhone || '',
      visitor.occupation || '',
      visitor.education || '',
      visitor.address || '',
      visitor.notes || '',
      visitor.status,
      visitor.createdAt,
      visitor.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteVisitor(id: string): Promise<void> {
    await this.run('DELETE FROM visitors WHERE id = ?', [id]);
  }

  // ==================== 个案管理 ====================
  async getAllCases(): Promise<SimpleCase[]> {
    const sql = `
      SELECT 
        c.*,
        v.name as visitor_name
      FROM cases c
      LEFT JOIN visitors v ON c.visitor_id = v.id
      ORDER BY c.created_at DESC
    `;
    const rows = await this.query(sql);
    return rows.map(row => this.mapRowToCase(row as Record<string, unknown>));
  }

  async getCase(id: string): Promise<SimpleCase | null> {
    const sql = `
      SELECT 
        c.*,
        v.name as visitor_name
      FROM cases c
      LEFT JOIN visitors v ON c.visitor_id = v.id
      WHERE c.id = ?
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? this.mapRowToCase(rows[0] as Record<string, unknown>) : null;
  }

  async saveCase(caseData: SimpleCase): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO cases (
        id, visitor_id, name, summary, therapy_method, selected_sand_tools,
        last_date, next_date, total, star, duration, crisis, homework,
        progress, keywords, supervision, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      caseData.id,
      caseData.visitorId,
      caseData.name,
      caseData.summary,
      caseData.therapyMethod,
      JSON.stringify(caseData.selectedSandTools || []),
      caseData.lastDate || null,
      caseData.nextDate || null,
      caseData.total,
      caseData.star ? 1 : 0,
      caseData.duration,
      caseData.crisis || null,
      caseData.homework || null,
      caseData.progress || null,
      JSON.stringify(caseData.keywords || []),
      caseData.supervision || null,
      caseData.createdAt,
      caseData.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteCase(id: string): Promise<void> {
    await this.run('DELETE FROM cases WHERE id = ?', [id]);
  }

  // ==================== 预约管理 ====================
  async getAllAppointments(): Promise<Appointment[]> {
    const sql = `
      SELECT 
        a.*,
        v.name as visitor_name,
        c.summary as case_summary
      FROM appointments a
      LEFT JOIN visitors v ON a.visitor_id = v.id
      LEFT JOIN cases c ON a.case_id = c.id
      ORDER BY a.date DESC, a.start_time DESC
    `;
    const rows = await this.query(sql);
    return rows.map(row => this.mapRowToAppointment(row as Record<string, unknown>));
  }

  async getAppointment(id: string): Promise<Appointment | null> {
    const sql = `
      SELECT 
        a.*,
        v.name as visitor_name,
        c.summary as case_summary
      FROM appointments a
      LEFT JOIN visitors v ON a.visitor_id = v.id
      LEFT JOIN cases c ON a.case_id = c.id
      WHERE a.id = ?
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? this.mapRowToAppointment(rows[0] as Record<string, unknown>) : null;
  }

  async saveAppointment(appointment: Appointment): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO appointments (
        id, visitor_id, case_id, visitor_name, visitor_phone, visitor_age, visitor_gender,
        date, start_time, end_time, duration, type, status, urgency, room, therapist_id,
        therapist_name, subject, description, notes, reminder_enabled, reminder_time,
        is_first_session, session_number, total_planned_sessions, fee, payment_status,
        created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      appointment.id,
      appointment.visitorId || null,
      appointment.caseId || null,
      appointment.visitorName,
      appointment.visitorPhone || null,
      appointment.visitorAge || null,
      appointment.visitorGender || null,
      appointment.date,
      appointment.startTime,
      appointment.endTime,
      appointment.duration,
      appointment.type,
      appointment.status,
      appointment.urgency,
      appointment.room,
      appointment.therapistId,
      appointment.therapistName,
      appointment.subject,
      appointment.description || null,
      appointment.notes || null,
      appointment.reminderEnabled ? 1 : 0,
      appointment.reminderTime,
      appointment.isFirstSession ? 1 : 0,
      appointment.sessionNumber || null,
      appointment.totalPlannedSessions || null,
      appointment.fee || null,
      appointment.paymentStatus || null,
      appointment.createdAt,
      appointment.updatedAt,
      appointment.createdBy
    ];

    await this.run(sql, params);
  }

  async deleteAppointment(id: string): Promise<void> {
    await this.run('DELETE FROM appointments WHERE id = ?', [id]);
  }

  // ==================== 团体会话管理 ====================
  async getAllGroupSessions(): Promise<GroupSession[]> {
    const rows = await this.query('SELECT * FROM group_sessions ORDER BY created_at DESC');
    return rows.map(row => this.mapRowToGroupSession(row as Record<string, unknown>));
  }

  async getGroupSession(id: string): Promise<GroupSession | null> {
    const rows = await this.query('SELECT * FROM group_sessions WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToGroupSession(rows[0] as Record<string, unknown>) : null;
  }

  async saveGroupSession(session: GroupSession): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO group_sessions (
        id, title, description, therapist_id, therapist_name, max_participants,
        current_participants, participants, session_type, target_age, duration,
        frequency, total_sessions, current_session, start_date, end_date,
        meeting_time, location, status, requirements, materials,
        selected_sand_tools, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      session.id,
      session.title,
      session.description || null,
      session.therapistId,
      session.therapistName,
      session.maxParticipants,
      session.currentParticipants,
      JSON.stringify(session.participants || []),
      session.sessionType,
      session.targetAge,
      session.duration,
      session.frequency,
      session.totalSessions || null,
      session.currentSession || null,
      session.startDate,
      session.endDate || null,
      session.meetingTime,
      session.location,
      session.status,
      session.requirements || null,
      JSON.stringify(session.materials || []),
      JSON.stringify(session.selectedSandTools || []),
      session.notes || null,
      session.createdAt,
      session.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteGroupSession(id: string): Promise<void> {
    await this.run('DELETE FROM group_sessions WHERE id = ?', [id]);
  }

  // ==================== 设置管理 ====================
  async getSettings(): Promise<AppSettings | null> {
    const rows = await this.query('SELECT * FROM settings ORDER BY updated_at DESC LIMIT 1');
    if (rows.length === 0) return null;
    
    try {
      return JSON.parse(String((rows[0] as Record<string, unknown>).data));
    } catch {
      return null;
    }
  }

  async saveSettings(settings: AppSettings): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO settings (id, data, updated_at)
      VALUES (1, ?, ?)
    `;
    
    const params = [
      JSON.stringify(settings),
      new Date().toISOString()
    ];

    await this.run(sql, params);
  }

  // ==================== 沙具管理 ====================
  async getAllSandTools(): Promise<SandTool[]> {
    const rows = await this.query('SELECT * FROM sand_tools ORDER BY name');
    return rows.map(row => this.mapRowToSandTool(row as Record<string, unknown>));
  }

  async getSandTool(id: string): Promise<SandTool | null> {
    const rows = await this.query('SELECT * FROM sand_tools WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToSandTool(rows[0] as Record<string, unknown>) : null;
  }

  async saveSandTool(tool: SandTool): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO sand_tools (
        id, name, category, subcategory, description, material, size, color,
        quantity, available, condition, location, image_url, notes, tags,
        usage_count, last_used, is_fragile, needs_care, replacement_needed
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      tool.id, tool.name, tool.category, tool.subcategory || null, tool.description || null,
      tool.material, tool.size, tool.color || null, tool.quantity, tool.available,
      tool.condition, tool.location, tool.imageData || null, tool.notes || null,
      JSON.stringify(tool.tags || []), tool.usageCount || 0, tool.lastUsed || null,
      tool.isFragile ? 1 : 0, tool.needsCare ? 1 : 0, tool.replacementNeeded ? 1 : 0
    ];

    await this.run(sql, params);
  }

  async deleteSandTool(id: string): Promise<void> {
    await this.run('DELETE FROM sand_tools WHERE id = ?', [id]);
  }

  // ==================== 数据映射方法 ====================
  private mapRowToVisitor(row: Record<string, unknown>): Visitor {
    return {
      id: String(row.id),
      name: String(row.name),
      age: Number(row.age),
      gender: String(row.gender) as '男' | '女',
      phone: row.phone ? String(row.phone) : '',
      email: row.email ? String(row.email) : '',
      emergencyContact: row.emergency_contact ? String(row.emergency_contact) : '',
      emergencyPhone: row.emergency_phone ? String(row.emergency_phone) : '',
      occupation: row.occupation ? String(row.occupation) : '',
      education: row.education ? String(row.education) : '',
      address: row.address ? String(row.address) : '',
      notes: row.notes ? String(row.notes) : '',
      status: String(row.status) as '活跃' | '暂停' | '完成',
      createdAt: String(row.created_at),
      updatedAt: String(row.updated_at)
    };
  }

  private mapRowToCase(row: Record<string, unknown>): SimpleCase {
    return {
      id: String(row.id),
      visitorId: String(row.visitor_id),
      name: String(row.name),
      summary: String(row.summary),
      therapyMethod: String(row.therapy_method) as TherapyMethod,
      selectedSandTools: row.selected_sand_tools ? JSON.parse(String(row.selected_sand_tools)) : [],
      lastDate: row.last_date ? String(row.last_date) : undefined,
      nextDate: row.next_date ? String(row.next_date) : undefined,
      total: Number(row.total),
      star: Boolean(row.star),
      duration: Number(row.duration),
      crisis: row.crisis ? String(row.crisis) as CrisisLevel : undefined,
      homework: row.homework ? String(row.homework) as HomeworkStatus : undefined,
      progress: row.progress ? String(row.progress) as Progress : undefined,
      keywords: row.keywords ? JSON.parse(String(row.keywords)) : [],
      supervision: row.supervision ? String(row.supervision) : undefined,
      createdAt: String(row.created_at),
      updatedAt: String(row.updated_at)
    };
  }

  private mapRowToGroupSession(row: Record<string, unknown>): GroupSession {
    return {
      id: String(row.id),
      title: String(row.title),
      description: String(row.description),
      therapistId: String(row.therapist_id),
      therapistName: String(row.therapist_name),
      maxParticipants: Number(row.max_participants),
      currentParticipants: Number(row.current_participants),
      participants: row.participants ? JSON.parse(String(row.participants)) : [],
      sessionType: String(row.session_type) as '开放式团体' | '封闭式团体' | '主题团体' | '发展性团体',
      targetAge: String(row.target_age) as '儿童' | '青少年' | '成人' | '老年' | '混合',
      duration: Number(row.duration),
      frequency: String(row.frequency) as '单次' | '每周' | '每两周' | '每月',
      totalSessions: Number(row.total_sessions),
      currentSession: Number(row.current_session),
      startDate: String(row.start_date),
      endDate: String(row.end_date),
      meetingTime: String(row.meeting_time),
      location: String(row.location),
      status: String(row.status) as '计划中' | '进行中' | '已完成' | '已取消' | '暂停',
      requirements: row.requirements ? String(row.requirements) : undefined,
      materials: row.materials ? JSON.parse(String(row.materials)) : [],
      selectedSandTools: row.selected_sand_tools ? JSON.parse(String(row.selected_sand_tools)) : [],
      notes: row.notes ? String(row.notes) : undefined,
      createdAt: String(row.created_at),
      updatedAt: String(row.updated_at)
    };
  }

  private mapRowToSandTool(row: Record<string, unknown>): SandTool {
    return {
      id: String(row.id),
      name: String(row.name),
      category: String(row.category) as SandToolCategory,
      subcategory: row.subcategory ? String(row.subcategory) : undefined,
      description: row.description ? String(row.description) : undefined,
      material: String(row.material),
      size: String(row.size) as SandToolSize,
      color: row.color ? String(row.color) : undefined,
      quantity: Number(row.quantity) || 1,
      available: Number(row.available) || 1,
      condition: String(row.condition) as SandToolCondition,
      location: String(row.location),
      imageData: row.image_url ? String(row.image_url) : undefined,
      notes: row.notes ? String(row.notes) : undefined,
      tags: row.tags ? JSON.parse(String(row.tags)) : [],
      usageCount: Number(row.usage_count) || 0,
      lastUsed: row.last_used ? String(row.last_used) : undefined,
      isFragile: Boolean(row.is_fragile),
      needsCare: Boolean(row.needs_care),
      replacementNeeded: Boolean(row.replacement_needed)
    };
  }

  private mapRowToAppointment(row: Record<string, unknown>): Appointment {
    return {
      id: String(row.id),
      visitorId: String(row.visitor_id),
      caseId: row.case_id ? String(row.case_id) : undefined,
      visitorName: String(row.visitor_name),
      visitorPhone: String(row.visitor_phone),
      visitorAge: Number(row.visitor_age),
      visitorGender: row.visitor_gender ? String(row.visitor_gender) as '男' | '女' : undefined,
      date: String(row.date),
      startTime: String(row.start_time),
      endTime: String(row.end_time),
      duration: Number(row.duration),
      type: String(row.type) as AppointmentType,
      status: String(row.status) as AppointmentStatus,
      urgency: String(row.urgency) as UrgencyLevel,
      room: String(row.room),
      therapistId: String(row.therapist_id),
      therapistName: String(row.therapist_name),
      subject: String(row.subject),
      description: String(row.description),
      notes: row.notes ? String(row.notes) : undefined,
      reminderEnabled: Boolean(row.reminder_enabled),
      reminderTime: row.reminder_time ? Number(row.reminder_time) : 0,
      isFirstSession: Boolean(row.is_first_session),
      sessionNumber: Number(row.session_number),
      totalPlannedSessions: Number(row.total_planned_sessions),
      fee: Number(row.fee),
      paymentStatus: row.payment_status ? String(row.payment_status) as '未支付' | '已支付' | '部分支付' : undefined,
      createdAt: String(row.created_at),
      updatedAt: String(row.updated_at),
      createdBy: String(row.created_by)
    };
  }

  // ==================== 系统信息 ====================
  async getSystemInfo() {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API is not available');
      }
      const version = await window.electronAPI.getAppVersion();
      const userDataPath = await window.electronAPI.getAppPath('userData');
      return {
        platform: 'Desktop',
        storageType: 'SQLite',
        databasePath: userDataPath + '/xlsp.db',
        isElectron: true,
        version: version
      };
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return {
        platform: 'Desktop',
        storageType: 'SQLite',
        databasePath: 'xlsp.db',
        isElectron: true,
        version: '1.0.0'
      };
    }
  }

  // ==================== 数据导出 ====================
  async exportAllData() {
    const [visitors, cases, sessions, tools] = await Promise.all([
      this.getAllVisitors(),
      this.getAllCases(),
      this.getAllGroupSessions(),
      this.getAllSandTools()
    ]);

    return {
      exportDate: new Date().toISOString(),
      version: '1.0.0',
      data: {
        visitors,
        cases,
        sessions,
        tools
      }
    };
  }
}

// 导出单例
export const sqliteDataManager = new SQLiteDataManager();
