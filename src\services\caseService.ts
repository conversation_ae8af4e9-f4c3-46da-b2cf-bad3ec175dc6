import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import { sqliteDataManager } from './sqliteDataManager';
import { visitorService } from './visitorService';

export class CaseService {
  
  // 获取所有个案
  async getAllCases(): Promise<SimpleCase[]> {
    try {
      return await sqliteDataManager.getAllCases();
    } catch (error) {
      console.error('获取个案列表失败:', error);
      return [];
    }
  }

  // 根据ID获取个案
  async getCase(id: string): Promise<SimpleCase | null> {
    try {
      return await sqliteDataManager.getCase(id);
    } catch (error) {
      console.error('获取个案失败:', error);
      return null;
    }
  }

  // 根据ID获取个案 (为了兼容性保留)
  async getById(id: string): Promise<SimpleCase | null> {
    return this.getCase(id);
  }

  // 获取个案及其来访者信息
  async getCaseWithVisitor(id: string): Promise<(SimpleCase & { visitor?: Visitor }) | null> {
    try {
      const caseItem = await this.getCase(id);
      if (!caseItem) return null;

      const visitor = await visitorService.getVisitor(caseItem.visitorId);
      return { ...caseItem, visitor: visitor || undefined };
    } catch (error) {
      console.error('获取个案及来访者信息失败:', error);
      return null;
    }
  }

  // 获取所有个案及其来访者信息（兼容方法名）
  async getAllCasesWithVisitor(): Promise<(SimpleCase & { visitor?: Visitor })[]> {
    return this.getAllCasesWithVisitors();
  }

  // 获取所有个案及其来访者信息
  async getAllCasesWithVisitors(): Promise<(SimpleCase & { visitor?: Visitor })[]> {
    try {
      const cases = await this.getAllCases();
      const casesWithVisitors = await Promise.all(
        cases.map(async (caseItem) => {
          const visitor = await visitorService.getVisitor(caseItem.visitorId);
          return { ...caseItem, visitor: visitor || undefined };
        })
      );
      return casesWithVisitors;
    } catch (error) {
      console.error('获取个案及来访者信息失败:', error);
      return [];
    }
  }

  // 创建个案
  async createCase(caseData: Omit<SimpleCase, 'id' | 'createdAt' | 'updatedAt'>): Promise<SimpleCase> {
    try {
      const id = `case_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();
      
      const newCase: SimpleCase = {
        ...caseData,
        id,
        createdAt: now,
        updatedAt: now
      };

      await sqliteDataManager.saveCase(newCase);
      return newCase;
    } catch (error) {
      console.error('创建个案失败:', error);
      throw new Error('创建个案失败');
    }
  }

  // 更新个案
  async updateCase(id: string, updates: Partial<SimpleCase>): Promise<SimpleCase | null> {
    try {
      const existingCase = await this.getCase(id);
      if (!existingCase) return null;

      const updatedCase = {
        ...existingCase,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await sqliteDataManager.saveCase(updatedCase);
      return updatedCase;
    } catch (error) {
      console.error('更新个案失败:', error);
      return null;
    }
  }

  // 删除个案
  async deleteCase(id: string): Promise<boolean> {
    try {
      await sqliteDataManager.deleteCase(id);
      return true;
    } catch (error) {
      console.error('删除个案失败:', error);
      return false;
    }
  }

  // 搜索个案
  async searchCases(query: string): Promise<SimpleCase[]> {
    try {
      const allCases = await this.getAllCases();
      const searchTerm = query.toLowerCase();
      return allCases.filter(caseItem => 
        caseItem.name.toLowerCase().includes(searchTerm) ||
        caseItem.summary.toLowerCase().includes(searchTerm) ||
        caseItem.therapyMethod.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('搜索个案失败:', error);
      return [];
    }
  }

  // 筛选个案
  async filterCases(filters: {
    therapyMethod?: string;
    star?: boolean;
    crisis?: string;
    progress?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<SimpleCase[]> {
    try {
      const allCases = await this.getAllCases();
      
      return allCases.filter(caseItem => {
        if (filters.therapyMethod && caseItem.therapyMethod !== filters.therapyMethod) {
          return false;
        }
        
        if (filters.star !== undefined && caseItem.star !== filters.star) {
          return false;
        }
        
        if (filters.crisis && caseItem.crisis !== filters.crisis) {
          return false;
        }
        
        if (filters.progress && caseItem.progress !== filters.progress) {
          return false;
        }
        
        if (filters.startDate && caseItem.lastDate && caseItem.lastDate < filters.startDate) {
          return false;
        }
        
        if (filters.endDate && caseItem.lastDate && caseItem.lastDate > filters.endDate) {
          return false;
        }
        
        return true;
      });
    } catch (error) {
      console.error('筛选个案失败:', error);
      return [];
    }
  }

  // 获取个案统计信息
  async getCaseStats() {
    try {
      const allCases = await this.getAllCases();
      
      const stats = {
        total: allCases.length,
        starred: allCases.filter(c => c.star).length,
        urgent: allCases.filter(c => c.crisis === '⚠️').length,
        needsAttention: allCases.filter(c => c.crisis === '⚡').length,
        improving: allCases.filter(c => c.progress === '⬆️').length,
        declining: allCases.filter(c => c.progress === '⬇️').length,
        needsFollowUp: 0
      };
      
      // 计算需要跟进的个案（超过14天未咨询）
      const now = new Date();
      stats.needsFollowUp = allCases.filter(caseItem => {
        if (!caseItem.lastDate) return false;
        const lastDate = new Date(caseItem.lastDate);
        const daysDiff = Math.floor((now.getTime() - lastDate.getTime()) / (1000 * 3600 * 24));
        return daysDiff > 14;
      }).length;
      
      return stats;
    } catch (error) {
      console.error('获取个案统计失败:', error);
      return {
        total: 0,
        starred: 0,
        urgent: 0,
        needsAttention: 0,
        improving: 0,
        declining: 0,
        needsFollowUp: 0
      };
    }
  }

  // 根据治疗方法分组个案
  async getCasesByTherapyMethod() {
    try {
      const allCases = await this.getAllCases();
      const grouped: { [key: string]: number } = {};
      
      allCases.forEach((caseItem: SimpleCase) => {
        const method = caseItem.therapyMethod;
        grouped[method] = (grouped[method] || 0) + 1;
      });
      
      return grouped;
    } catch (error) {
      console.error('按治疗方法分组失败:', error);
      return {};
    }
  }

  // 获取最近个案
  async getRecentCases(limit: number = 10): Promise<SimpleCase[]> {
    try {
      const allCases = await this.getAllCases();
      return allCases
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('获取最近个案失败:', error);
      return [];
    }
  }

  // 获取需要跟进的个案
  async getCasesNeedingFollowUp(): Promise<SimpleCase[]> {
    try {
      const allCases = await this.getAllCases();
      const now = new Date();
      
      return allCases.filter(caseItem => {
        if (!caseItem.lastDate) return false;
        const lastDate = new Date(caseItem.lastDate);
        const daysDiff = Math.floor((now.getTime() - lastDate.getTime()) / (1000 * 3600 * 24));
        return daysDiff > 14;
      }).sort((a, b) => {
        const dateA = a.lastDate ? new Date(a.lastDate).getTime() : 0;
        const dateB = b.lastDate ? new Date(b.lastDate).getTime() : 0;
        return dateA - dateB; // 按最后咨询时间升序排列
      });
    } catch (error) {
      console.error('获取需要跟进的个案失败:', error);
      return [];
    }
  }

  // 批量创建个案
  async createCasesBatch(casesData: Array<Omit<SimpleCase, 'id' | 'createdAt' | 'updatedAt'>>): Promise<{
    success: SimpleCase[];
    failed: Array<{ data: SimpleCase; error: string }>;
  }> {
    const success: SimpleCase[] = [];
    const failed: Array<{ data: SimpleCase; error: string }> = [];

    for (const caseData of casesData) {
      try {
        const newCase = await this.createCase(caseData);
        success.push(newCase);
      } catch (error) {
        failed.push({
          data: caseData,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    return { success, failed };
  }
}

// 导出实例
export const caseService = new CaseService();
