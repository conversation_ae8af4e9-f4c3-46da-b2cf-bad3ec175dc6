"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { RefreshCw, Heart, Share2 } from "lucide-react"

const moodPhrases = [
  {
    chinese: "今天也要元气满满呀！",
    pinyin: "<PERSON><PERSON><PERSON><PERSON> yě yào yuánqì mǎnmǎn ya!",
    english: "Today, let's be full of energy too!",
    category: "积极向上",
  },
  {
    chinese: "每一天都是新的开始",
    pinyin: "Měi yītiān dōu shì xīn de kāishǐ",
    english: "Every day is a new beginning",
    category: "积极向上",
  },
  {
    chinese: "阳光总在风雨后",
    pinyin: "Yángguāng zǒng zài fēngyǔ hòu",
    english: "Sunshine always comes after the storm",
    category: "积极向上",
  },
  {
    chinese: "保持微笑，拥抱美好",
    pinyin: "<PERSON><PERSON><PERSON><PERSON> wēix<PERSON>o, y<PERSON><PERSON><PERSON><PERSON><PERSON> mě<PERSON>",
    english: "Keep smiling, embrace the beautiful",
    category: "积极向上",
  },
  {
    chinese: "慢慢来，比较快",
    pinyin: "Mànmàn lái, bǐjiào kuài",
    english: "Take it slow, it's actually faster",
    category: "励志成长",
  },
  {
    chinese: "做自己的太阳，无需凭借谁的光",
    pinyin: "Zuò zìjǐ de tàiyáng, wúxū píngjiè shuí de guāng",
    english: "Be your own sun, no need to rely on anyone's light",
    category: "励志成长",
  },
  {
    chinese: "愿你被这个世界温柔以待",
    pinyin: "Yuàn nǐ bèi zhège shìjiè wēnróu yǐ dài",
    english: "May you be treated gently by this world",
    category: "温暖治愈",
  },
  {
    chinese: "平凡的日子也有微光",
    pinyin: "Píngfán de rìzi yě yǒu wēiguāng",
    english: "Ordinary days also have glimmers of light",
    category: "温暖治愈",
  },
  {
    chinese: "心中有光，何惧黑暗",
    pinyin: "Xīnzhōng yǒu guāng, hé jù hēi'àn",
    english: "With light in your heart, why fear darkness",
    category: "温暖治愈",
  },
]

export default function DailyMoodPhrases() {
  const [currentPhrase, setCurrentPhrase] = useState(moodPhrases[0])
  const [isLiked, setIsLiked] = useState(false)
  const [showTranslation, setShowTranslation] = useState(false)

  const getRandomPhrase = () => {
    const randomIndex = Math.floor(Math.random() * moodPhrases.length)
    setCurrentPhrase(moodPhrases[randomIndex])
    setIsLiked(false)
    setShowTranslation(false)
  }

  const toggleLike = () => {
    setIsLiked(!isLiked)
  }

  const sharePhrase = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "每日心情短语",
          text: `${currentPhrase.chinese}\n${currentPhrase.english}`,
        })
      } catch (err) {
        console.log("Error sharing:", err)
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(`${currentPhrase.chinese}\n${currentPhrase.english}`)
    }
  }

  useEffect(() => {
    // Get a random phrase on initial load
    getRandomPhrase()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-6 flex flex-col items-center justify-center">
      <div className="text-center mb-12">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">每日短语</h1>
        <p className="text-gray-500 text-sm">让美好的话语点亮每一天</p>
      </div>

      <main className="w-full max-w-md">
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="p-8 text-center space-y-6">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium">
              {currentPhrase.category}
            </div>

            <div className="space-y-4">
              <h2 className="text-xl font-medium text-gray-900 leading-relaxed">{currentPhrase.chinese}</h2>

              <p className="text-gray-500 text-sm">{currentPhrase.pinyin}</p>

              {showTranslation && (
                <div className="pt-4 border-t border-gray-100">
                  <p className="text-gray-600 text-sm leading-relaxed">{currentPhrase.english}</p>
                </div>
              )}
            </div>

            <div className="flex items-center justify-center gap-2 pt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTranslation(!showTranslation)}
                className="text-xs"
              >
                {showTranslation ? "隐藏翻译" : "显示翻译"}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleLike}
                className={`p-2 ${isLiked ? "text-red-500" : "text-gray-400 hover:text-red-400"}`}
              >
                <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
              </Button>

              <Button variant="ghost" size="sm" onClick={sharePhrase} className="p-2 text-gray-400 hover:text-gray-600">
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="text-center mt-8">
          <Button onClick={getRandomPhrase} className="bg-gray-900 hover:bg-gray-800 text-white px-6 py-2 text-sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            换一句话
          </Button>
        </div>
      </main>
    </div>
  )
}
