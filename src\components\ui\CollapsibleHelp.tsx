import React, { useState, type ReactNode } from 'react';

interface CollapsibleHelpProps {
  title: string;                 // 标题，例如 “设备码使用说明”
  items?: string[];              // 纯文本段落（前缀圆点）
  children?: ReactNode;          // 自定义内容（若提供则忽略 items）
  defaultOpen?: boolean;         // 初始是否展开
  className?: string;            // 外层自定义样式
}

/**
 * 统一的折叠说明组件（授权弹窗等复用）
 * - 动画：max-height + opacity 渐变
 * - A11y: 按钮带 aria-expanded
 */
const CollapsibleHelp: React.FC<CollapsibleHelpProps> = ({
  title,
  items,
  children,
  defaultOpen = false,
  className = ''
}) => {
  const [open, setOpen] = useState(defaultOpen);
  const contentId = `help-panel-${title.replace(/\s+/g, '-')}-${Math.random().toString(36).slice(2,7)}`;

  return (
    <div className={className}>
      <button
        type="button"
        aria-expanded={open}
        aria-controls={contentId}
        onClick={() => setOpen(o => !o)}
        className="flex items-center text-xs text-white/55 hover:text-white/85 transition-colors group select-none"
      >
        <span className="inline-flex w-5 h-5 mr-2 items-center justify-center rounded-full bg-white/10 group-hover:bg-white/20 text-[10px] font-bold">
          {open ? '-' : '+'}
        </span>
        {open ? `收起${title}` : `展开${title}`}
      </button>
      <div
        id={contentId}
        className={`mt-2 overflow-hidden transition-all duration-300 ease-in-out ${open ? 'max-h-[340px] opacity-100' : 'max-h-0 opacity-0'} `}
      >
        <div className="p-5 glass rounded-xl border border-yellow-400/20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-400/5"></div>
          <div className="flex items-start space-x-4 relative z-10">
            <svg className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div className="text-sm text-yellow-300">
              <p className="font-semibold mb-3 text-base">{title}：</p>
              <div className="space-y-2 text-white/80 leading-relaxed">
                {children ? children : items?.map((t, i) => <p key={i}>• {t}</p>)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollapsibleHelp;
