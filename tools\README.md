# 发行方工具与说明

本目录提供两个与授权激活相关的发行端工具与说明：

## 1) 激活码签名器（离线）
- 文件：tools/license-signer.html
- 用途：发行方在离线环境中生成与软件兼容的激活码。
- 使用方法：
  1. 在浏览器直接打开该 HTML（无需服务器）。
  2. 填入私钥（建议 PKCS#8 格式）、kv(公钥版本)、版本 v、盐ID sid。
  3. 将设备哈希 d（Base64URL）粘贴到页面。
     - 设备哈希 d 的来源：终端用户在登录页点击“设备码”按钮复制设备唯一码，发行方将唯一码转为哈希后得到 d（若使用本页面，可直接在“设备哈希 d”输入框粘贴已计算的 d）。
  4. 点击“生成激活码”，复制输出即可。

- 兼容性：页面使用 WebCrypto 进行 SHA-256 与 RSA-PKCS1-v1_5，支持 Chrome/Edge/Firefox（桌面）。

### 关于 d 的计算
- 软件端的设备码是基于硬件指纹的 Crockford Base32 表示；验证端最终使用的是其 canonical 序列经 SHA-256 后的 Base64URL 值。
- 若需要从设备唯一码推导 d，可使用后端脚本完成；或在本页面适配相同算法（保留为发行端私有流程）。

## 2) 激活流程说明
1. 终端用户在登录页点击“设备码”→ 复制设备唯一码；
2. 发送给发行方；
3. 发行方计算设备哈希 d 并使用签名器生成激活码（kv 与 v 与软件端公钥配置相符）；
4. 终端用户在登录页点击“激活”→ 粘贴激活码→ 激活成功后进入主界面；
5. 激活信息离线保存（localStorage 版本），可改为数据库存储按需升级。

## 3) 注意事项
- 私钥必须安全保管，严禁泄露；建议使用独立的离线设备生成激活码。
- kv 与软件端 src/utils/auth/publicKeys.ts 中公钥版本号对齐。
- 若需要吊销/迁移授权，可生成新激活码覆盖或提供 revoke 说明。

