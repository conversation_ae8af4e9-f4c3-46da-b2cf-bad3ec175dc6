<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>沙盘管理系统 - 激活码生成器</title>
  <style>
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }
    .container {
      max-width: 900px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      font-size: 28px;
      margin-bottom: 10px;
      font-weight: 600;
    }
    .header p {
      opacity: 0.9;
      font-size: 16px;
    }
    .content {
      padding: 30px;
    }
    .section {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 25px;
      margin-bottom: 25px;
      border: 1px solid #e9ecef;
    }
    .section h3 {
      color: #495057;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #495057;
    }
    input, textarea, select {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: white;
    }
    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: #4facfe;
      box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    }
    textarea {
      min-height: 120px;
      resize: vertical;
      font-family: 'Courier New', monospace;
    }
    .btn {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-right: 10px;
    }
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
    }
    .btn:active {
      transform: translateY(0);
    }
    .btn-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    }
    .btn-danger {
      background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    }
    .status {
      margin-left: 15px;
      font-weight: 500;
    }
    .status.success { color: #28a745; }
    .status.error { color: #dc3545; }
    .output-area {
      background: #2d3748;
      color: #e2e8f0;
      border-radius: 8px;
      padding: 20px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.5;
      word-break: break-all;
      min-height: 100px;
    }
    .help-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
    }
    .help-box h4 {
      color: #856404;
      margin-bottom: 10px;
    }
    .help-box ul {
      color: #856404;
      padding-left: 20px;
    }
    .help-box li {
      margin-bottom: 5px;
    }
    .device-hash-section {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
    }
    .private-key-section {
      background: #fff8e1;
      border: 1px solid #ffecb3;
    }
    .output-section {
      background: #f3e5f5;
      border: 1px solid #e1bee7;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔐 沙盘管理系统</h1>
      <p>激活码生成器 - 发行方专用工具</p>
    </div>
    
    <div class="content">
      <!-- 设备码转换区域 -->
      <div class="section device-hash-section">
        <h3>📱 第一步：设备码转换</h3>
        <div class="form-group">
          <label>用户设备码（从软件"设备码"按钮获取）</label>
          <input type="text" id="deviceCode" placeholder="例如：ABCDE-12345-FGHIJ-67890-KLMNO" />
        </div>
        <div class="form-group">
          <button class="btn" onclick="convertDeviceCode()">转换为设备哈希</button>
          <span id="convertStatus" class="status"></span>
        </div>
        <div class="form-group">
          <label>设备哈希 d（自动生成，用于激活码）</label>
          <input type="text" id="deviceHash" readonly style="background: #f8f9fa;" />
        </div>
      </div>

      <!-- 私钥和参数配置 -->
      <div class="section private-key-section">
        <h3>🔑 第二步：私钥和参数配置</h3>
        <div class="form-group">
          <label>RSA 私钥（PKCS#8 格式）</label>
          <textarea id="privateKey" placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----"></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label>公钥版本 (kv)</label>
            <select id="keyVersion">
              <option value="1">版本 1</option>
              <option value="2">版本 2</option>
              <option value="3">版本 3</option>
            </select>
          </div>
          <div class="form-group">
            <label>许可证版本 (v)</label>
            <select id="licenseVersion">
              <option value="1">版本 1</option>
              <option value="2">版本 2</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>盐ID (sid)</label>
            <input type="text" id="saltId" placeholder="例如：SALT2025A" />
          </div>
          <div class="form-group">
            <label>签发时间 (可选)</label>
            <input type="datetime-local" id="issuedAt" />
          </div>
        </div>

        <div class="form-group">
          <label>额外字段 (JSON格式，可选)</label>
          <input type="text" id="extraFields" placeholder='例如：{"plan":"pro","exp":**********}' />
        </div>
      </div>

      <!-- 生成激活码 -->
      <div class="section output-section">
        <h3>⚡ 第三步：生成激活码</h3>
        <div class="form-group">
          <button class="btn btn-success" onclick="generateActivationCode()">🚀 生成激活码</button>
          <button class="btn" onclick="copyActivationCode()">📋 复制激活码</button>
          <button class="btn btn-danger" onclick="clearAll()">🗑️ 清空所有</button>
          <span id="generateStatus" class="status"></span>
        </div>
        
        <div class="form-group">
          <label>生成的激活码</label>
          <div id="activationCodeOutput" class="output-area">点击"生成激活码"按钮开始...</div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="section">
        <h3>📖 使用说明</h3>
        <div class="help-box">
          <h4>操作流程：</h4>
          <ul>
            <li><strong>获取设备码：</strong>用户在软件登录页点击"设备码"按钮，复制完整设备码</li>
            <li><strong>转换设备码：</strong>将用户设备码粘贴到上方输入框，点击"转换为设备哈希"</li>
            <li><strong>配置参数：</strong>填入RSA私钥、选择版本号、设置盐ID等参数</li>
            <li><strong>生成激活码：</strong>点击"生成激活码"，将生成的激活码发送给用户</li>
            <li><strong>用户激活：</strong>用户在软件登录页点击"激活"，粘贴激活码完成激活</li>
          </ul>
        </div>
        
        <div class="help-box" style="background: #f8d7da; border-color: #f5c6cb; margin-top: 15px;">
          <h4 style="color: #721c24;">安全提醒：</h4>
          <ul style="color: #721c24;">
            <li>私钥必须严格保密，建议在离线环境中使用本工具</li>
            <li>每个设备码只能生成一次激活码，激活码与设备硬件绑定</li>
            <li>本工具完全离线运行，不会上传任何数据到服务器</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Base64URL 编码解码
    const b64u = {
      enc: (u8) => btoa(String.fromCharCode.apply(null, Array.from(u8))).replace(/\+/g,'-').replace(/\//g,'_').replace(/=+$/,''),
      dec: (s) => Uint8Array.from(atob(s.replace(/-/g,'+').replace(/_/g,'/')), c => c.charCodeAt(0)),
      encStr: (s) => b64u.enc(new TextEncoder().encode(s))
    };

    // SHA-256 哈希
    async function sha256Hex(str) {
      const buf = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(str));
      return Array.from(new Uint8Array(buf)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // 设备码转换为设备哈希
    async function convertDeviceCode() {
      const deviceCode = document.getElementById('deviceCode').value.trim();
      const status = document.getElementById('convertStatus');
      
      if (!deviceCode) {
        status.textContent = '请输入设备码';
        status.className = 'status error';
        return;
      }

      try {
        // 移除连字符，获取canonical形式
        const canonical = deviceCode.replace(/-/g, '');
        
        // 计算SHA-256哈希
        const hex = await sha256Hex(canonical);
        const bytes = new Uint8Array(hex.match(/.{1,2}/g).map(h => parseInt(h, 16)));
        const deviceHash = b64u.enc(bytes);
        
        document.getElementById('deviceHash').value = deviceHash;
        status.textContent = '转换成功';
        status.className = 'status success';
      } catch (error) {
        status.textContent = '转换失败：' + error.message;
        status.className = 'status error';
      }
    }

    // 导入私钥
    async function importPrivateKey(pem) {
      const lines = pem.trim().replace(/-----[^-]+-----/g, '').replace(/\s+/g, '');
      const bin = Uint8Array.from(atob(lines), c => c.charCodeAt(0));
      
      try {
        return await crypto.subtle.importKey(
          'pkcs8', 
          bin, 
          { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' }, 
          false, 
          ['sign']
        );
      } catch (e) {
        throw new Error('私钥导入失败，请确保使用PKCS#8格式');
      }
    }

    // 生成激活码
    async function generateActivationCode() {
      const status = document.getElementById('generateStatus');
      const output = document.getElementById('activationCodeOutput');
      
      try {
        // 获取所有参数
        const privateKey = document.getElementById('privateKey').value.trim();
        const deviceHash = document.getElementById('deviceHash').value.trim();
        const keyVersion = parseInt(document.getElementById('keyVersion').value);
        const licenseVersion = parseInt(document.getElementById('licenseVersion').value);
        const saltId = document.getElementById('saltId').value.trim();
        const issuedAtInput = document.getElementById('issuedAt').value;
        const extraFields = document.getElementById('extraFields').value.trim();

        // 验证必填字段
        if (!privateKey) throw new Error('请输入RSA私钥');
        if (!deviceHash) throw new Error('请先转换设备码');
        if (!saltId) throw new Error('请输入盐ID');

        // 计算签发时间
        let issuedAt;
        if (issuedAtInput) {
          issuedAt = Math.floor(new Date(issuedAtInput).getTime() / 1000);
        } else {
          issuedAt = Math.floor(Date.now() / 1000);
        }

        // 构建payload
        const payload = {
          v: licenseVersion,
          kv: keyVersion,
          d: deviceHash,
          sid: saltId,
          ia: issuedAt
        };

        // 添加额外字段
        if (extraFields) {
          try {
            const extra = JSON.parse(extraFields);
            Object.assign(payload, extra);
          } catch {
            throw new Error('额外字段JSON格式无效');
          }
        }

        // Base64URL编码payload
        const payloadB64 = b64u.encStr(JSON.stringify(payload));

        // 导入私钥并签名
        const key = await importPrivateKey(privateKey);
        const signature = await crypto.subtle.sign(
          'RSASSA-PKCS1-v1_5', 
          key, 
          new TextEncoder().encode(payloadB64)
        );
        const signatureB64 = b64u.enc(new Uint8Array(signature));

        // 生成最终激活码
        const activationCode = `${payloadB64}.${signatureB64}`;
        
        output.textContent = activationCode;
        status.textContent = '生成成功！';
        status.className = 'status success';
        
      } catch (error) {
        output.textContent = '生成失败：' + error.message;
        status.textContent = '生成失败';
        status.className = 'status error';
      }
    }

    // 复制激活码
    async function copyActivationCode() {
      const output = document.getElementById('activationCodeOutput');
      const text = output.textContent;
      
      if (!text || text.includes('失败') || text.includes('点击')) {
        alert('请先生成激活码');
        return;
      }
      
      try {
        await navigator.clipboard.writeText(text);
        alert('激活码已复制到剪贴板');
      } catch {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('激活码已复制到剪贴板');
      }
    }

    // 清空所有字段
    function clearAll() {
      if (confirm('确定要清空所有字段吗？')) {
        document.getElementById('deviceCode').value = '';
        document.getElementById('deviceHash').value = '';
        document.getElementById('privateKey').value = '';
        document.getElementById('saltId').value = '';
        document.getElementById('issuedAt').value = '';
        document.getElementById('extraFields').value = '';
        document.getElementById('activationCodeOutput').textContent = '点击"生成激活码"按钮开始...';
        document.getElementById('convertStatus').textContent = '';
        document.getElementById('generateStatus').textContent = '';
      }
    }

    // 页面加载时设置当前时间
    window.addEventListener('load', () => {
      const now = new Date();
      now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
      document.getElementById('issuedAt').value = now.toISOString().slice(0, 16);
    });
  </script>
</body>
</html>
