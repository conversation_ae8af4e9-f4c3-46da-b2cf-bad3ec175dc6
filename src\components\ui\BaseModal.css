/* 基础弹窗组件样式 */

/* 弹窗遮罩层 */
.base-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
}

/* 弹窗容器 */
.base-modal-container {
  background: var(--bg-primary, #ffffff);
  border-radius: 12px;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.18),
    0 4px 6px -2px rgba(0, 0, 0, 0.12);
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalSlideIn 0.15s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
  border: 1px solid var(--border-light, #e5e7eb);
}

/* 玻璃风格变体：与登录页一致 */
.base-modal--glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
.base-modal--glass .base-modal-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.18);
}
.base-modal--glass .base-modal-title {
  color: #ffffff;
}
.base-modal--glass .base-modal-subtitle {
  color: rgba(255, 255, 255, 0.65);
}
.base-modal--glass .base-modal-close {
  color: rgba(255, 255, 255, 0.7);
}
.base-modal--glass .base-modal-close:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #fff;
}
.base-modal--glass .base-modal-content { padding: 0; }

/* 弹窗尺寸 */
.base-modal-sm {
  max-width: 480px;
}

.base-modal-md {
  max-width: 640px;
}

.base-modal-lg {
  max-width: 800px;
}

.base-modal-xl {
  max-width: 1024px;
}

/* 弹窗头部 */
.base-modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid var(--border-light, #e5e7eb);
  background: var(--bg-primary, #ffffff);
  flex-shrink: 0;
}

.base-modal-header-content {
  flex: 1;
  min-width: 0;
}

.base-modal-title-section {
  margin-right: 16px;
}

.base-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #111827);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.base-modal-subtitle {
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  margin: 0;
  line-height: 1.4;
}

/* 关闭按钮 */
.base-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.base-modal-close svg {
  width: 20px !important;
  height: 20px !important;
  min-width: 20px;
  min-height: 20px;
  flex-shrink: 0;
}

.base-modal-close:hover {
  background: var(--bg-secondary, #f3f4f6);
  color: var(--text-primary, #111827);
}

.base-modal-close:active {
  transform: scale(0.95);
}

/* 内容区域 */
.base-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  min-height: 0;
}

/* 动画效果 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .base-modal-overlay {
    padding: 12px;
  }
  
  .base-modal-container {
    max-height: 95vh;
    border-radius: 8px;
  }
  
  .base-modal-sm,
  .base-modal-md,
  .base-modal-lg,
  .base-modal-xl {
    max-width: 100%;
  }
  
  .base-modal-header {
    padding: 20px 16px 12px 16px;
  }
  
  .base-modal-title {
    font-size: 18px;
  }
  
  .base-modal-subtitle {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .base-modal-overlay {
    padding: 8px;
  }
  
  .base-modal-header {
    padding: 16px 12px 8px 12px;
  }
  
  .base-modal-title {
    font-size: 16px;
  }
  
  .base-modal-close {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }
  
  .base-modal-close svg {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px;
    min-height: 18px;
  }
}
