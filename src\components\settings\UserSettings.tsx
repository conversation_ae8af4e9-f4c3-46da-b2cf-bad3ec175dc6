import React from 'react';
import { Card } from '../ui/Card';
import { Input, Select } from '../ui/Form';
import type { AppSettings } from '../../types/settings';

interface UserSettingsProps {
  settings: AppSettings;
  updateSettings: (path: string, value: unknown) => void;
  saving: boolean;
}

const UserSettings: React.FC<UserSettingsProps> = ({ settings, updateSettings }) => {
  const { user } = settings;

  return (
    <div className="settings-form">

      {/* 工作偏好 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">工作偏好</h3>
          <p className="card-subtitle">默认设置和工作习惯配置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Select
              label="默认咨询时长"
              value={user.preferences.defaultSessionDuration.toString()}
              onChange={(e) => updateSettings('user.preferences.defaultSessionDuration', parseInt(e.target.value))}
              options={[
                { value: '30', label: '30分钟' },
                { value: '45', label: '45分钟' },
                { value: '50', label: '50分钟' },
                { value: '60', label: '60分钟' },
                { value: '90', label: '90分钟' }
              ]}
            />
            
            <div className="settings-form-row">
              <Input
                label="工作开始时间"
                type="time"
                value={user.preferences.workingHours.start}
                onChange={(e) => updateSettings('user.preferences.workingHours.start', e.target.value)}
              />
              
              <Input
                label="工作结束时间"
                type="time"
                value={user.preferences.workingHours.end}
                onChange={(e) => updateSettings('user.preferences.workingHours.end', e.target.value)}
              />
            </div>
          </div>

          <div className="mt-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={user.preferences.autoSave}
                onChange={(e) => updateSettings('user.preferences.autoSave', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用自动保存功能
            </label>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UserSettings;
