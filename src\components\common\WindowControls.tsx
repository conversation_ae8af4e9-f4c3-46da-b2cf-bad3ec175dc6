import React from 'react';

type Action = 'minimize' | 'maximize' | 'close';

const send = (action: Action) => {
  try { (window as any).electronAPI?.windowAction?.(action); } catch {}
};

const WindowControls: React.FC = () => {
  return (
    <div className="window-controls">
      <button className="wc-btn wc-minimize" title="最小化" onClick={() => send('minimize')}>
        <span className="wc-dot" />
      </button>
      <button className="wc-btn wc-maximize" title="最大化/还原" onClick={() => send('maximize')}>
        <span className="wc-dot" />
      </button>
      <button className="wc-btn wc-close" title="关闭" onClick={() => send('close')}>
        <span className="wc-dot" />
      </button>
    </div>
  );
};

export default WindowControls;

