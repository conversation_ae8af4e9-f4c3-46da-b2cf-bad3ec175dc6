<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>沙盘管理系统 - 激活文件生成器</title>
  <style>
    body{font-family: system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; margin:0; padding:24px; background:#0b0b13; color:#fff}
    .wrap{max-width:1000px; margin:0 auto}
    h1{margin:0 0 24px; font-size:24px; text-align:center; color:#3b82f6}
    .card{background:rgba(255,255,255,.05); border:1px solid rgba(255,255,255,.1); border-radius:12px; padding:20px; margin-bottom:20px}
    label{display:block; font-size:14px; opacity:.9; margin-top:12px; font-weight:500}
    input,textarea,select{width:100%; padding:10px 12px; border-radius:8px; border:1px solid rgba(255,255,255,.2); background:rgba(255,255,255,.06); color:#fff; font-size:14px}
    textarea{min-height:120px; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas}
    .row{display:grid; grid-template-columns:1fr 1fr; gap:16px; margin-top:12px}
    .row3{display:grid; grid-template-columns:1fr 1fr 1fr; gap:16px; margin-top:12px}
    button{padding:12px 20px; border:0; border-radius:8px; background:#3b82f6; color:#fff; cursor:pointer; font-size:14px; font-weight:500; transition:all 0.2s}
    button:hover{background:#2563eb}
    button:disabled{background:#6b7280; cursor:not-allowed}
    .btn-success{background:#10b981}
    .btn-success:hover{background:#059669}
    .muted{opacity:.7; font-size:13px}
    code{background:rgba(0,0,0,.35); padding:2px 6px; border-radius:4px; font-size:12px}
    .status{margin-left:12px; font-size:13px}
    .status.success{color:#10b981}
    .status.error{color:#ef4444}
    .status.info{color:#3b82f6}
    .highlight{background:rgba(59,130,246,.1); border:1px solid rgba(59,130,246,.3); border-radius:8px; padding:16px; margin:12px 0}
    .step{background:rgba(16,185,129,.1); border:1px solid rgba(16,185,129,.3); border-radius:8px; padding:16px; margin:12px 0}
    .warning{background:rgba(245,158,11,.1); border:1px solid rgba(245,158,11,.3); border-radius:8px; padding:16px; margin:12px 0}
  </style>
</head>
<body>
<div class="wrap">
  <h1>🔐 沙盘管理系统 - 激活文件生成器</h1>
  
  <div class="step">
    <h3>📋 使用步骤</h3>
    <ol class="muted">
      <li><strong>获取设备码</strong>：客户在软件登录页点击"设备码"按钮，复制完整设备码</li>
      <li><strong>转换设备哈希</strong>：在下方输入设备码，点击"转换为设备哈希"</li>
      <li><strong>配置私钥</strong>：粘贴您的RSA私钥（PKCS#8格式）</li>
      <li><strong>生成激活文件</strong>：点击"生成激活文件"，文件将自动下载</li>
      <li><strong>发送给客户</strong>：客户上传激活文件完成软件激活</li>
    </ol>
  </div>

  <div class="card">
    <h3>第一步：设备码转换</h3>
    <label>客户设备码（格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX）</label>
    <input id="deviceCode" placeholder="例如：ABCDE-12345-FGHIJ-67890-KLMNO" />
    <div style="margin-top:12px;">
      <button id="btn-convert">转换为设备哈希</button>
      <span class="status" id="convertStatus"></span>
    </div>
    <label>设备哈希（自动生成）</label>
    <input id="deviceHash" readonly style="background:rgba(255,255,255,.02);" />
  </div>

  <div class="card">
    <h3>第二步：配置签名参数</h3>
    <label>RSA私钥（PKCS#8格式）⚠️ 请确保私钥安全</label>
    <textarea id="privateKey" placeholder="-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----END PRIVATE KEY-----"></textarea>

    <div class="row3">
      <div>
        <label>公钥版本 (kv)</label>
        <select id="keyVersion">
          <option value="1">版本 1（默认）</option>
          <option value="2">版本 2</option>
        </select>
      </div>
      <div>
        <label>许可证版本 (v)</label>
        <select id="licenseVersion">
          <option value="1">版本 1（默认）</option>
        </select>
      </div>
      <div>
        <label>盐ID (sid)</label>
        <input id="saltId" placeholder="例如：SALT2025A" />
      </div>
    </div>

    <div class="row">
      <div>
        <label>签发时间（留空为当前时间）</label>
        <input id="issuedAt" type="datetime-local" />
      </div>
      <div>
        <label>额外字段（JSON格式，可选）</label>
        <input id="extraFields" placeholder='例如：{"plan":"pro","features":["advanced"]}' />
      </div>
    </div>
  </div>

  <div class="card">
    <h3>第三步：生成激活文件</h3>
    <div style="display:flex; gap:12px; align-items:center;">
      <button id="btn-generate" class="btn-success">🚀 生成激活文件</button>
      <span class="status" id="generateStatus"></span>
    </div>
    
    <div class="highlight" id="result" style="display:none;">
      <h4>✅ 激活文件生成成功！</h4>
      <p id="resultText"></p>
      <button id="btn-download" style="margin-top:8px;">📥 重新下载文件</button>
    </div>
  </div>

  <div class="warning">
    <h3>⚠️ 安全注意事项</h3>
    <ul class="muted">
      <li><strong>私钥安全</strong>：绝对不要在联网环境中使用私钥，建议在专用离线设备上运行</li>
      <li><strong>文件管理</strong>：每个设备码只能生成一次激活文件，请妥善保管</li>
      <li><strong>数据安全</strong>：本工具完全在浏览器本地运行，不会上传任何数据</li>
      <li><strong>激活绑定</strong>：激活文件与客户设备硬件绑定，无法转移使用</li>
    </ul>
  </div>
</div>

<script>
// Base64URL 编码解码
const b64u = {
  enc: (u8) => btoa(String.fromCharCode.apply(null, Array.from(u8))).replace(/\+/g,'-').replace(/\//g,'_').replace(/=+$/,''),
  dec: (s) => Uint8Array.from(atob(s.replace(/-/g,'+').replace(/_/g,'/')), c => c.charCodeAt(0)),
  encStr: (s) => b64u.enc(new TextEncoder().encode(s))
};

// SHA256 哈希
async function sha256Hex(str) {
  const buf = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(str));
  return Array.from(new Uint8Array(buf)).map(b => b.toString(16).padStart(2, '0')).join('');
}

// 从设备码生成设备哈希
async function deviceHashFromDeviceCode(deviceCode) {
  const clean = deviceCode.replace(/-/g, '').toUpperCase();
  const hex = await sha256Hex(clean);
  const bytes = new Uint8Array(hex.match(/.{1,2}/g).map(h => parseInt(h, 16)));
  return b64u.enc(bytes);
}

// 导入私钥
async function importPrivateKey(pem) {
  const lines = pem.trim().replace(/-----[^-]+-----/g, '').replace(/\s+/g, '');
  const bin = Uint8Array.from(atob(lines), c => c.charCodeAt(0));
  
  try {
    return await crypto.subtle.importKey(
      'pkcs8', 
      bin, 
      { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' }, 
      false, 
      ['sign']
    );
  } catch (e) {
    throw new Error('私钥导入失败，请确保使用PKCS#8格式');
  }
}

// 下载文件
function downloadFile(filename, content) {
  const blob = new Blob([content], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

let currentActivationFile = null;

// 转换设备码
document.getElementById('btn-convert').addEventListener('click', async () => {
  const deviceCode = document.getElementById('deviceCode').value.trim();
  const status = document.getElementById('convertStatus');
  
  if (!deviceCode) {
    status.textContent = '请输入设备码';
    status.className = 'status error';
    return;
  }
  
  if (!/^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/.test(deviceCode)) {
    status.textContent = '设备码格式不正确';
    status.className = 'status error';
    return;
  }
  
  try {
    status.textContent = '转换中...';
    status.className = 'status info';
    
    const deviceHash = await deviceHashFromDeviceCode(deviceCode);
    document.getElementById('deviceHash').value = deviceHash;
    
    status.textContent = '转换成功';
    status.className = 'status success';
  } catch (error) {
    status.textContent = '转换失败: ' + error.message;
    status.className = 'status error';
  }
});

// 生成激活文件
document.getElementById('btn-generate').addEventListener('click', async () => {
  const status = document.getElementById('generateStatus');
  const result = document.getElementById('result');
  
  try {
    status.textContent = '正在生成激活文件...';
    status.className = 'status info';
    result.style.display = 'none';
    
    const deviceCode = document.getElementById('deviceCode').value.trim();
    const deviceHash = document.getElementById('deviceHash').value.trim();
    const privateKey = document.getElementById('privateKey').value.trim();
    const keyVersion = parseInt(document.getElementById('keyVersion').value);
    const licenseVersion = parseInt(document.getElementById('licenseVersion').value);
    const saltId = document.getElementById('saltId').value.trim();
    const issuedAtInput = document.getElementById('issuedAt').value;
    const extraFields = document.getElementById('extraFields').value.trim();
    
    if (!deviceCode || !deviceHash || !privateKey || !saltId) {
      throw new Error('请填写所有必填字段');
    }

    // 计算签发时间
    let issuedAt;
    if (issuedAtInput) {
      issuedAt = new Date(issuedAtInput).getTime();
    } else {
      issuedAt = Date.now();
    }

    // 构建payload
    const payload = {
      v: licenseVersion,
      kv: keyVersion,
      d: deviceHash,
      sid: saltId,
      ia: issuedAt
    };

    // 添加额外字段
    if (extraFields) {
      try {
        const extra = JSON.parse(extraFields);
        Object.assign(payload, extra);
      } catch {
        throw new Error('额外字段JSON格式无效');
      }
    }

    // Base64URL编码payload
    const payloadB64 = b64u.encStr(JSON.stringify(payload));

    // 导入私钥并签名
    const key = await importPrivateKey(privateKey);
    const signature = await crypto.subtle.sign(
      'RSASSA-PKCS1-v1_5', 
      key, 
      new TextEncoder().encode(payloadB64)
    );

    // 转换签名为Base64URL
    const sigB64 = b64u.enc(new Uint8Array(signature));

    // 组合激活码
    const activationCode = `${payloadB64}.${sigB64}`;

    // 创建激活文件内容
    const activationFileContent = {
      deviceCode,
      activationCode,
      keyVersion,
      issuedAt,
      timestamp: new Date(issuedAt).toISOString(),
      version: '1.0',
      description: '沙盘管理系统激活文件'
    };

    // 生成文件名并下载
    const fileName = `激活文件_${deviceCode.replace(/-/g, '')}.lic`;
    const fileContent = JSON.stringify(activationFileContent, null, 2);
    
    currentActivationFile = { fileName, fileContent };
    downloadFile(fileName, fileContent);

    // 显示成功结果
    status.textContent = '激活文件生成成功！';
    status.className = 'status success';
    
    document.getElementById('resultText').innerHTML = `
      <strong>文件名：</strong>${fileName}<br>
      <strong>设备码：</strong>${deviceCode}<br>
      <strong>生成时间：</strong>${new Date(issuedAt).toLocaleString()}<br>
      <strong>密钥版本：</strong>${keyVersion}
    `;
    result.style.display = 'block';
    
  } catch (error) {
    status.textContent = `生成失败: ${error.message}`;
    status.className = 'status error';
    result.style.display = 'none';
  }
});

// 重新下载文件
document.getElementById('btn-download').addEventListener('click', () => {
  if (currentActivationFile) {
    downloadFile(currentActivationFile.fileName, currentActivationFile.fileContent);
  }
});

// 页面加载时设置默认私钥（基于种子RDTCKT2619JN0270生成的私钥）
window.addEventListener('load', () => {
  document.getElementById('privateKey').value = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
  
  document.getElementById('saltId').value = 'RDTCKT2619JN0270';
});
</script>
</body>
</html>
