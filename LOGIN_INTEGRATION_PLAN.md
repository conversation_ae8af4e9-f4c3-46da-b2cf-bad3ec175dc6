# 登录界面和激活系统集成方案

## 项目概述
将loin目录中的现代化登录界面和完整的离线授权系统集成到主项目（沙盘管理软件）中。

## 当前状态分析

### ✅ 已完成的部分
1. **离线授权系统实现**（loin目录）
   - 设备指纹生成：基于硬件信息生成唯一设备码
   - 激活码验证：RSA签名 + 设备锁定 + HMAC防篡改
   - 公钥管理：支持多版本密钥轮换
   - 签名器工具：完整的CLI工具用于生成激活码

2. **登录界面实现**（loin目录）
   - 现代化WebGL极光背景
   - 响应式设计和动画效果
   - 设备码显示模态框
   - 激活表单组件

### 🔍 系统实现验证

#### 设备指纹生成 (deviceId.ts)
- ✅ 符合方案：使用CPU/屏幕/时区/GUID作为硬件特征
- ✅ 格式正确：SHA256 → Base32 → 5-5-5-5-5格式
- ✅ Web环境适配：使用浏览器API替代系统命令
- ⚠️ 需要适配：Electron环境下需要真实硬件信息

#### 激活码验证 (licenseValidator.ts)
- ✅ 符合方案：实现了完整的验证流程
- ✅ Payload格式：{v, kv, d, sid, ia}
- ✅ 安全机制：RSA签名 + 设备锁定 + HMAC防篡改
- ✅ 存储格式：{lic, dLock, hmac}

#### 签名器工具 (tools/signer/)
- ✅ 完整实现：支持密钥生成、激活码签名
- ✅ 多版本支持：密钥轮换机制
- ✅ CLI接口：便于自动化操作

## 集成计划

### 阶段1：环境适配和工具类集成
**目标**：将激活系统的核心工具类集成到主项目

**任务**：
1. 复制loin/src/utils/到主项目src/utils/license/
2. 适配Electron环境的设备指纹生成
3. 更新导入路径和依赖关系
4. 测试激活系统在主项目中的功能

**验收标准**：
- 设备码生成正常工作
- 激活码验证流程完整
- 存储机制正常

### 阶段2：登录界面集成 ✅
**目标**：替换现有登录界面，集成新的UI组件
**状态**：已完成

**已完成任务**：
1. ✅ 创建 `src/components/license/DeviceCodeModal.tsx` - 设备码获取界面
2. ✅ 创建 `src/components/license/ActivationForm.tsx` - 激活码输入界面
3. ✅ 完全重写 `src/components/auth/Login.tsx` 集成激活系统
4. ✅ 添加WebGL Aurora背景效果（简化版着色器）
5. ✅ 更新 `src/components/auth/Login.css` 支持新设计
6. ✅ 实现激活状态检查和条件渲染

**技术实现**：
- 条件渲染：根据激活状态显示不同界面
- WebGL背景：使用片段着色器创建Aurora效果
- Glass morphism：现代毛玻璃效果设计
- 模态框系统：设备码和激活表单的模态显示

### 阶段3：桌面签名器创建 ✅
**目标**：创建独立的桌面签名器应用
**状态**：已完成

**已完成任务**：
1. ✅ 创建独立的Electron应用 `desktop-signer/`
2. ✅ 设计现代化GUI界面（HTML + CSS + JavaScript）
3. ✅ 实现完整的密钥管理功能（生成、导入、导出）
4. ✅ 实现激活码生成和格式化功能
5. ✅ 添加多版本密钥支持
6. ✅ 创建详细的使用文档

**功能特性**：
- RSA-2048密钥对生成和管理
- 多版本密钥支持，支持密钥轮换
- 设备码绑定激活码生成
- 密钥导入导出功能
- 现代化桌面界面
- 一键复制激活码
- 离线工作，无需网络连接

### 阶段4：测试和优化
**目标**：全面测试激活系统，确保安全性和稳定性

**任务**：
1. 端到端测试激活流程
2. 验证设备绑定机制
3. 测试跨设备复制防护
4. 性能优化和错误处理

**验收标准**：
- 激活流程完整无误
- 设备绑定有效
- 防篡改机制正常
- 错误处理完善

## 技术要点

### Electron环境适配
- 设备指纹需要使用真实的硬件信息
- 可能需要在主进程中实现硬件信息采集
- 通过IPC通信传递设备信息

### 存储机制
- 考虑使用Electron的安全存储
- 保持与现有localStorage的兼容性

### 安全考虑
- 公钥和HMAC密钥的混淆处理
- 防止客户端代码被轻易修改
- 考虑代码混淆和打包优化

## 风险和缓解

### 风险1：Electron环境兼容性
- **风险**：Web环境的设备指纹在Electron中可能不准确
- **缓解**：实现Electron专用的硬件信息采集

### 风险2：现有功能影响
- **风险**：集成过程可能影响现有登录功能
- **缓解**：渐进式集成，保持向后兼容

### 风险3：安全性降低
- **风险**：集成过程可能引入安全漏洞
- **缓解**：严格测试，代码审查

## 下一步行动
1. 开始阶段1：环境适配和工具类集成
2. 创建测试用例验证激活系统
3. 逐步替换现有登录界面
