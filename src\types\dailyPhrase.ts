export interface DailyPhrase {
  id: string;
  chinese: string;
  pinyin: string;
  english: string;
  category: string;
  description?: string;
  source?: string;
  tags?: string[];
  isActive: boolean;
  usageCount: number;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DailyPhraseStats {
  totalPhrases: number;
  todayPhrase?: DailyPhrase;
  popularCategory: string;
  mostUsedPhrase?: DailyPhrase;
}

export const PHRASE_CATEGORIES = [
  '积极向上',
  '励志成长',
  '温暖治愈',
  '内心平静',
  '生活智慧',
  '心理健康',
  '人际关系',
  '自我接纳'
] as const;

export type PhraseCategory = typeof PHRASE_CATEGORIES[number];
