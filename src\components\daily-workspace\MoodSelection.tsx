import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, RotateCcw } from 'lucide-react';
import { moodOptions, getMoodAnimation } from '../../data/moodSelectionData';
import { moodSelectionService } from '../../services/moodSelectionService';
import type { MoodType, MoodStats } from '../../types/moodSelection';
import './MoodSelection.css';

interface MoodSelectionProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help') => void;
}

const MoodSelection: React.FC<MoodSelectionProps> = () => {
  const [selectedMood, setSelectedMood] = useState<MoodType | null>(null);
  const [responseMessage, setResponseMessage] = useState<string>('');
  const [isAnimating, setIsAnimating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [moodStats, setMoodStats] = useState<MoodStats | null>(null);
  const [hasSelectedToday, setHasSelectedToday] = useState(false);

  // 加载心情统计和今日状态
  const loadMoodData = async () => {
    try {
      const [stats, todayStatus] = await Promise.all([
        moodSelectionService.getMoodStats(),
        moodSelectionService.hasTodayMoodRecord()
      ]);
      
      setMoodStats(stats);
      setHasSelectedToday(todayStatus);
    } catch (error) {
      console.error('加载心情数据失败:', error);
    }
  };

  // 选择心情
  const handleMoodSelect = async (moodType: MoodType) => {
    if (loading || selectedMood === moodType) return;
    
    try {
      setLoading(true);
      setIsAnimating(true);
      setSelectedMood(moodType);

      // 记录心情选择并获取回应
      const result = await moodSelectionService.recordMoodSelection(moodType);
      setResponseMessage(result.response.message);
      
      // 重新加载数据
      await loadMoodData();
      
      // 动画效果
      setTimeout(() => {
        setIsAnimating(false);
      }, 2000);
      
    } catch (error) {
      console.error('选择心情失败:', error);
      setResponseMessage('记录心情时出现了小问题，但没关系，你的感受我们都能理解。');
      setIsAnimating(false);
    } finally {
      setLoading(false);
    }
  };

  // 重置选择
  const resetSelection = () => {
    setSelectedMood(null);
    setResponseMessage('');
    setIsAnimating(false);
  };

  // 获取心情选项的样式类
  const getMoodOptionClass = (moodType: MoodType) => {
    const baseClass = 'mood-option';
    const classes = [baseClass];
    
    if (selectedMood === moodType) {
      classes.push('selected');
      if (isAnimating) {
        classes.push(getMoodAnimation(moodType));
      }
    }
    
    if (loading && selectedMood !== moodType) {
      classes.push('disabled');
    }
    
    return classes.join(' ');
  };

  useEffect(() => {
    loadMoodData();
  }, []);

  return (
    <div className="mood-selection-container">
      <div className="mood-selection-header">
        <h3>💭 今日心情</h3>
        {moodStats?.lastMood && !hasSelectedToday && (
          <p className="last-mood-info">
            上次记录：{moodOptions.find(opt => opt.id === moodStats.lastMood!.moodType)?.emoji} {moodOptions.find(opt => opt.id === moodStats.lastMood!.moodType)?.name}
            {moodStats.lastMood.daysAgo > 0 && ` (${moodStats.lastMood.daysAgo}天前)`}
          </p>
        )}
      </div>

      <div className="mood-selection-card">
        {!selectedMood ? (
          <>
            <div className="mood-prompt">
              <p className="prompt-text">今天的心情如何？选择一个来记录此刻的感受吧</p>
            </div>
            
            <div className="mood-options-grid">
              {moodOptions.map((option) => (
                <button
                  key={option.id}
                  className={getMoodOptionClass(option.id)}
                  onClick={() => handleMoodSelect(option.id)}
                  disabled={loading}
                  style={{
                    '--mood-color': option.color,
                    '--mood-bg': option.bgColor
                  } as React.CSSProperties}
                >
                  <span className="mood-emoji">{option.emoji}</span>
                  <span className="mood-name">{option.name}</span>
                </button>
              ))}
            </div>
            
            {hasSelectedToday && (
              <div className="today-completed">
                <Sparkles size={16} />
                <span>今天已经记录过心情了，明天再来分享吧</span>
              </div>
            )}
          </>
        ) : (
          <div className="mood-response">
            <div className="response-mood">
              <span className="response-emoji">
                {moodOptions.find(opt => opt.id === selectedMood)?.emoji}
              </span>
              <span className="response-mood-name">
                {moodOptions.find(opt => opt.id === selectedMood)?.name}
              </span>
            </div>
            
            {responseMessage && (
              <div className="response-message">
                <p>{responseMessage}</p>
              </div>
            )}
            
            <div className="response-actions">
              <button 
                onClick={resetSelection}
                className="btn-reset"
                disabled={loading}
              >
                <RotateCcw size={16} />
                重新选择
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 心情花园预览 */}
      {moodStats && moodStats.totalRecords > 0 && (
        <div className="mood-garden-preview">
          <div className="garden-header">
            <span>🌱 心情花园</span>
            <span className="garden-count">已收集 {moodStats.totalRecords} 个心情</span>
          </div>
          <div className="garden-emotions">
            {Object.entries(moodStats.moodDistribution)
              .filter(([_, count]) => count > 0)
              .map(([moodType, count]) => {
                const option = moodOptions.find(opt => opt.id === moodType);
                return option ? (
                  <div key={moodType} className="garden-emotion" title={`${option.name}: ${count}次`}>
                    <span className="garden-emoji">{option.emoji}</span>
                    <span className="garden-count-dot">{count}</span>
                  </div>
                ) : null;
              })}
          </div>
        </div>
      )}
    </div>
  );
};

export default MoodSelection;
