@import '../../styles/variables.css';

/* 沙具管理页面样式 */

/* 联系客服按钮样式 */
.contact-service-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  transition: all 0.2s ease;
}

.contact-service-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* 页面头部视图切换控件 */
.view-toggle-control {
  display: flex;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 2px;
  gap: 2px;
}

.view-toggle-control .toggle-btn {
  border-radius: 6px;
  border: none;
  background: transparent;
  transition: all 0.2s ease;
  min-width: 80px;
  font-size: 13px;
  font-weight: 500;
}

.view-toggle-control .toggle-btn.active {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-toggle-control .toggle-btn:not(.active) {
  color: #64748b;
}

.view-toggle-control .toggle-btn:not(.active):hover {
  background: rgba(255, 255, 255, 0.5);
  color: #475569;
}

.view-switcher {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

/* 缩略图样式 */
.tool-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

/* 卡片视图网格 */
.sandtools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 16px 0;
}

/* 沙具卡片样式 */
.tool-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

.tool-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tool-card.selected {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* 卡片选择框 */
.card-select {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.select-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: white;
  border-color: #d1d5db;
}

/* 卡片图片区域 */
.card-image {
  width: 100%;
  height: 180px;
  position: relative;
  overflow: hidden;
}

.tool-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  gap: 8px;
}

.image-placeholder span {
  font-size: 14px;
  font-weight: 500;
}

/* 卡片内容区域 */
.card-content {
  padding: 16px;
}

.tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.tool-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.info-label {
  color: #6b7280;
  font-weight: 500;
  min-width: 50px;
}

.info-value {
  color: #374151;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tool-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  overflow: hidden;
}

/* 卡片操作按钮 */
.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-row {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .view-switcher {
    justify-content: flex-start;
  }
  
  .sandtools-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .sandtools-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .card-image {
    height: 140px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .tool-title {
    font-size: 14px;
  }
  
  .info-row {
    font-size: 12px;
  }
}

@media (max-width: 640px) {
  .sandtools-grid {
    grid-template-columns: 1fr;
  }
}

/* 详情模态框中的特殊样式 */
.tool-image-preview {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.tool-image-preview .preview-image {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: cover;
  display: block;
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.special-marks-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

/* 统计行样式 - 与个案管理完全一致 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.stat-value.urgent {
  color: #dc2626;
}

.stat-value.warning {
  color: #f59e0b;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* 复选框筛选 */
.checkbox-filter {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  user-select: none;
}

.checkbox-filter input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
  accent-color: var(--primary-blue);
}

/* 工具缩略图 */
.tool-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-light);
  position: relative;
  flex-shrink: 0;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

.thumbnail-icon {
  font-size: 20px;
}

/* 工具信息 - 与个案管理的case-name样式一致 */
.tool-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 0;
  flex: 1;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

.name-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.name {
  font-weight: 500;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 6px;
}

.details {
  font-size: 12px;
  color: #6b7280;
}

/* 类别信息 */
.category-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.category {
  font-size: 14px;
  color: #374151;
}

.subcategory {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 库存信息 */
.stock-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  min-width: 0;
}

.stock-numbers {
  font-weight: 500;
  color: #111827;
  margin: 0;
  white-space: nowrap;
  font-size: 14px;
}

/* 位置信息 */
.location-info {
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 使用信息 - 与last-session样式一致 */
.usage-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 14px;
  min-width: 0;
}

.last-used {
  color: #111827;
}

.usage-count {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  white-space: nowrap;
}

.never-used {
  color: #6b7280;
  font-style: italic;
  white-space: nowrap;
  font-size: 12px;
}

/* 网格视图 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.tool-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.tool-card-content {
  padding: 0;
}

.tool-card-image {
  width: 100%;
  height: 160px;
  position: relative;
  overflow: hidden;
  background: var(--bg-secondary);
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
}

.card-icon {
  font-size: 32px;
}

.tool-card-info {
  padding: var(--spacing-lg);
}

.tool-card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tool-card-category {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-md) 0;
}

.tool-card-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.tool-card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tools-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-md);
  }
  
  .tool-card-info {
    padding: var(--spacing-md);
  }
}