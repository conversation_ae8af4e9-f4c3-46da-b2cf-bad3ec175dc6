import type { MoodType, MoodRecord, MoodStats } from '../types/moodSelection';
import { getRandomMoodResponse } from '../data/moodSelectionData';
import { sqliteDataManager } from './sqliteDataManager';

class MoodSelectionService {
  private isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;

  private async ensureTables() {
    if (!this.isElectron) return;
    await sqliteDataManager.executeRun(`CREATE TABLE IF NOT EXISTS mood_records (
      id TEXT PRIMARY KEY,
      mood_type TEXT NOT NULL,
      selected_at TEXT NOT NULL,
      response_id TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`);
    await sqliteDataManager.executeRun(`CREATE TABLE IF NOT EXISTS mood_responses (
      id TEXT PRIMARY KEY,
      mood_type TEXT NOT NULL,
      message TEXT NOT NULL,
      category TEXT,
      is_active INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`);
    // 初始化心情回应数据(只在空表时写入)
    const cntRows = await sqliteDataManager.executeQuery('SELECT COUNT(*) as c FROM mood_responses');
    const count = (cntRows[0] as any)?.c || 0;
    if (count === 0) {
      const { moodResponses } = await import('../data/moodSelectionData');
      const now = new Date().toISOString();
      for (const moodKey of Object.keys(moodResponses) as (keyof typeof moodResponses)[]) {
        for (const resp of moodResponses[moodKey]) {
          await sqliteDataManager.executeRun(`INSERT INTO mood_responses (id, mood_type, message, category, created_at, updated_at) VALUES (?,?,?,?,?,?)`, [resp.id, resp.moodType, resp.message, resp.category, now, now]);
        }
      }
      console.log('已初始化心情回应数据');
    }
  }

  // 记录心情选择
  async recordMoodSelection(moodType: MoodType): Promise<{
    record: MoodRecord;
    response: { message: string; category: string };
  }> {
    try {
      await this.ensureTables();
      const response = getRandomMoodResponse(moodType);
      const now = new Date().toISOString();
      
      const record: MoodRecord = {
        id: `mood_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        moodType,
        selectedAt: now,
        responseId: response.id,
        isActive: true,
        createdAt: now,
        updatedAt: now
      };

      if (this.isElectron) {
        // 桌面端：保存到数据库
        await sqliteDataManager.executeRun(`
          INSERT INTO mood_records (id, mood_type, selected_at, response_id, is_active, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [record.id, record.moodType, record.selectedAt, record.responseId, 1, record.createdAt, record.updatedAt]);
      } else {
        // 浏览器端：保存到本地存储
        const existingRecords = this.getBrowserMoodRecords();
        existingRecords.push(record);
        localStorage.setItem('xlsp_mood_records', JSON.stringify(existingRecords));
      }

      return {
        record,
        response: {
          message: response.message,
          category: response.category
        }
      };
    } catch (error) {
      console.error('记录心情选择失败:', error);
      throw new Error('记录心情选择失败');
    }
  }

  // 获取最近的心情记录
  async getLastMoodRecord(): Promise<MoodRecord | null> {
    try {
      await this.ensureTables();
      if (this.isElectron) {
        // 桌面端：从数据库查询
        const results = await sqliteDataManager.executeQuery(`
          SELECT * FROM mood_records 
          WHERE is_active = 1 
          ORDER BY selected_at DESC 
          LIMIT 1
        `) as any[];
        
        if (results.length > 0) {
          const row = results[0];
          return {
            id: row.id,
            moodType: row.mood_type as MoodType,
            selectedAt: row.selected_at,
            responseId: row.response_id,
            isActive: Boolean(row.is_active),
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };
        }
      } else {
        // 浏览器端：从本地存储获取
        const records = this.getBrowserMoodRecords();
        return records.length > 0 ? records[records.length - 1] : null;
      }
      
      return null;
    } catch (error) {
      console.error('获取最近心情记录失败:', error);
      return null;
    }
  }

  // 获取心情统计数据
  async getMoodStats(): Promise<MoodStats> {
    try {
      await this.ensureTables();
      if (this.isElectron) {
        // 桌面端：从数据库统计
        const totalResult = await sqliteDataManager.executeQuery(`
          SELECT COUNT(*) as total FROM mood_records WHERE is_active = 1
        `) as any[];
        
        const distributionResult = await sqliteDataManager.executeQuery(`
          SELECT mood_type, COUNT(*) as count 
          FROM mood_records 
          WHERE is_active = 1 
          GROUP BY mood_type
        `) as any[];
        
        const weeklyResult = await sqliteDataManager.executeQuery(`
          SELECT mood_type, DATE(selected_at) as date
          FROM mood_records 
          WHERE is_active = 1 
            AND selected_at >= datetime('now', '-7 days')
          ORDER BY selected_at ASC
        `) as any[];

        const lastRecord = await this.getLastMoodRecord();
        
        const stats: MoodStats = {
          totalRecords: totalResult[0]?.total || 0,
          lastMood: lastRecord ? {
            moodType: lastRecord.moodType,
            selectedAt: lastRecord.selectedAt,
            daysAgo: Math.floor((Date.now() - new Date(lastRecord.selectedAt).getTime()) / (1000 * 60 * 60 * 24))
          } : undefined,
          moodDistribution: this.processMoodDistribution(distributionResult),
          weeklyTrend: weeklyResult.map((row: any) => ({
            date: row.date,
            moodType: row.mood_type as MoodType
          }))
        };
        
        return stats;
      } else {
        // 浏览器端：从本地存储统计
        const records = this.getBrowserMoodRecords();
        const lastRecord = records.length > 0 ? records[records.length - 1] : null;
        
        const moodDistribution = records.reduce((acc, record) => {
          acc[record.moodType] = (acc[record.moodType] || 0) + 1;
          return acc;
        }, {} as Record<MoodType, number>);
        
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const weeklyTrend = records
          .filter(record => new Date(record.selectedAt) >= oneWeekAgo)
          .map(record => ({
            date: record.selectedAt.split('T')[0],
            moodType: record.moodType
          }));
        
        return {
          totalRecords: records.length,
          lastMood: lastRecord ? {
            moodType: lastRecord.moodType,
            selectedAt: lastRecord.selectedAt,
            daysAgo: Math.floor((Date.now() - new Date(lastRecord.selectedAt).getTime()) / (1000 * 60 * 60 * 24))
          } : undefined,
          moodDistribution: moodDistribution as Record<MoodType, number>,
          weeklyTrend
        };
      }
    } catch (error) {
      console.error('获取心情统计失败:', error);
      return {
        totalRecords: 0,
        moodDistribution: {} as Record<MoodType, number>,
        weeklyTrend: []
      };
    }
  }

  // 浏览器端获取心情记录
  private getBrowserMoodRecords(): MoodRecord[] {
    try {
      const stored = localStorage.getItem('xlsp_mood_records');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取浏览器心情记录失败:', error);
      return [];
    }
  }

  // 处理心情分布数据
  private processMoodDistribution(results: any[]): Record<MoodType, number> {
    const distribution: Record<MoodType, number> = {
      joy: 0,
      calm: 0,
      nervous: 0,
      melancholy: 0,
      tired: 0,
      expectant: 0
    };
    
    results.forEach(row => {
      if (row.mood_type in distribution) {
        distribution[row.mood_type as MoodType] = row.count;
      }
    });
    
    return distribution;
  }

  // 获取今日是否已记录心情
  async hasTodayMoodRecord(): Promise<boolean> {
    try {
      await this.ensureTables();
      const today = new Date().toISOString().split('T')[0];
      
      if (this.isElectron) {
        const results = await sqliteDataManager.executeQuery(`
          SELECT COUNT(*) as count 
          FROM mood_records 
          WHERE is_active = 1 
            AND DATE(selected_at) = ?
        `, [today]) as any[];
        
        return (results[0]?.count || 0) > 0;
      } else {
        const records = this.getBrowserMoodRecords();
        return records.some(record => 
          record.selectedAt.split('T')[0] === today
        );
      }
    } catch (error) {
      console.error('检查今日心情记录失败:', error);
      return false;
    }
  }
}

export const moodSelectionService = new MoodSelectionService();
