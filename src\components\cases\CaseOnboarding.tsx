import React, { useEffect, useRef, useState } from 'react';
import './CaseOnboarding.css';

interface CaseOnboardingStep {
  id: string;
  title: string;
  content: string;
  target?: string | null;
}

interface CaseOnboardingProps {
  steps?: CaseOnboardingStep[];
  storageKey?: string;
  onFinish?: () => void;
}

const defaultSteps: CaseOnboardingStep[] = [
  { id: 'welcome', title: '欢迎进入个案管理', content: '这里帮助你系统化跟踪咨询历程、风险等级与进展。我们将快速浏览关键区域。', target: null },
  { id: 'create', title: '新建个案', content: '点击“新建个案”录入初始资料。建议填写概述、问题焦点及首选治疗方法。', target: 'button:has(svg[data-icon="plus"])' },
  { id: 'stats', title: '风险与进展概览', content: '顶部统计显示总个案、高危/需关注、超期与改善中，帮助你快速排定跟进优先级。', target: '.stats-overview' },
  { id: 'filters', title: '精准筛选', content: '使用搜索与筛选组合按危机等级、进展、治疗方法、时间范围定位目标个案。', target: '.professional-filters' },
  { id: 'table', title: '核心信息表格', content: '列表集中展示危机等级、进展、上次/下次咨询及累计次数。可点击查看或编辑。', target: '.professional-cases-table' },
  { id: 'select', title: '批量操作', content: '左侧勾选进行批量删除（谨慎操作）。暂不支持批量编辑，保持数据准确性。', target: '.professional-cases-table th:nth-child(1)' },
  { id: 'badges', title: '危机与进展标签', content: '危机(⚠️/⚡/✅) 与 进展(⬆️/➡️/⬇️) 通过颜色突出状态，及时识别需要干预的个案。', target: '.professional-cases-table th:nth-child(5)' },
  { id: 'done', title: '完成', content: '引导结束。可在帮助中心重新查看，或清除 localStorage 重新触发。祝工作顺利！', target: null }
];

const CaseOnboarding: React.FC<CaseOnboardingProps> = ({ steps = defaultSteps, storageKey = 'case_onboarding_done', onFinish }) => {
  const [current, setCurrent] = useState(0);
  const [visible, setVisible] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const holeRef = useRef<HTMLDivElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const done = localStorage.getItem(storageKey);
    if (!done) {
      const t = setTimeout(() => setVisible(true), 800);
      return () => clearTimeout(t);
    }
  }, [storageKey]);

  useEffect(() => {
    if (!visible) return;
    positionTooltip();
    const handler = () => positionTooltip();
    window.addEventListener('resize', handler);
    window.addEventListener('scroll', handler, true);
    return () => {
      window.removeEventListener('resize', handler);
      window.removeEventListener('scroll', handler, true);
    };
  }, [visible, current]);

  const currentStep = steps[current];

  const queryTarget = (selector?: string | null): HTMLElement | null => {
    if (!selector) return null;
    try { return document.querySelector(selector) as HTMLElement | null; } catch { return null; }
  };

  const positionTooltip = () => {
    if (!holeRef.current || !tooltipRef.current) return;
    const targetEl = queryTarget(currentStep?.target);
    const hole = holeRef.current;
    const tooltip = tooltipRef.current;
    let arrowPos: 'top' | 'bottom' | 'left' | 'right' | undefined = 'top';

    if (targetEl) {
      const rect = targetEl.getBoundingClientRect();
      const padding = 8;
      
      // 使用fixed定位避免scroll问题
      hole.style.position = 'fixed';
      hole.style.top = (rect.top - padding) + 'px';
      hole.style.left = (rect.left - padding) + 'px';
      hole.style.width = (rect.width + padding * 2) + 'px';
      hole.style.height = (rect.height + padding * 2) + 'px';

      let top = rect.bottom + 12; // 默认下方
      let left = rect.left;
      arrowPos = 'top';
      
      if (left + tooltip.offsetWidth > window.innerWidth - 16) {
        left = window.innerWidth - tooltip.offsetWidth - 16;
      }
      if (top + tooltip.offsetHeight > window.innerHeight - 16) { 
        top = rect.top - tooltip.offsetHeight - 12; 
        arrowPos = 'bottom'; 
      }
      left = Math.max(left, 16);
      
      tooltip.style.position = 'fixed';
      tooltip.style.top = top + 'px';
      tooltip.style.left = left + 'px';
      tooltip.classList.remove('case-onboarding-center');
      tooltip.setAttribute('data-arrow-pos', arrowPos);
    } else {
      hole.style.position = 'fixed';
      hole.style.top = '-1000px';
      hole.style.left = '-1000px';
      hole.style.width = '0px';
      hole.style.height = '0px';
      
      tooltip.style.position = 'fixed';
      tooltip.style.top = '50%';
      tooltip.style.left = '50%';
      tooltip.classList.add('case-onboarding-center');
      tooltip.removeAttribute('data-arrow-pos');
    }
  };

  const next = () => current < steps.length - 1 ? setCurrent(c => c + 1) : finish();
  const prev = () => current > 0 && setCurrent(c => c - 1);
  const skip = () => finish();
  const finish = () => {
    if (dontShowAgain) localStorage.setItem(storageKey, '1'); else localStorage.removeItem(storageKey);
    setVisible(false); onFinish?.();
  };

  useEffect(() => {
    if (!visible) return;
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') finish();
      else if (e.key === 'Enter' || e.key === 'ArrowRight') next();
      else if (e.key === 'ArrowLeft') prev();
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [visible, current]);

  if (!visible || !currentStep) return null;

  return (
    <div className="case-onboarding-overlay" role="dialog" aria-modal="true">
      <div ref={holeRef} className="case-onboarding-hole" aria-hidden="true" />
      <div ref={tooltipRef} className="case-onboarding-tooltip" data-step={currentStep.id}>
        <div className="co-header">
          <h4 className="co-title">{currentStep.title}</h4>
          <div className="co-step">{current + 1} / {steps.length}</div>
        </div>
        <div className="co-progress" aria-hidden="true"><span style={{ width: `${((current + 1) / steps.length) * 100}%` }} /></div>
        <div className="co-body" style={{ whiteSpace: 'pre-line' }}>{currentStep.content}</div>
        <div className="co-footer">
          <label className="co-dismiss">
            <input type="checkbox" checked={dontShowAgain} onChange={e => setDontShowAgain(e.target.checked)} /> 不再显示
          </label>
          <div className="co-actions">
            {current > 0 && <button onClick={prev}>上一步</button>}
            {current < steps.length - 1 && <button onClick={skip}>跳过</button>}
            <button className="primary" onClick={next}>{current === steps.length - 1 ? '完成' : '下一步'}</button>
          </div>
        </div>
        <div className="case-onboarding-steps" aria-label="进度指示">
          {steps.map((s,i) => <span key={s.id} className={i===current ? 'active':''} />)}
        </div>
      </div>
    </div>
  );
};

export default CaseOnboarding;
