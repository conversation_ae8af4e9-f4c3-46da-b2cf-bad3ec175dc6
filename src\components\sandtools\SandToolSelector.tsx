import React, { useState, useMemo } from 'react';
import { Search, Plus, Minus, Package, X } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  Button, 
  Input, 
  Select, 
  Badge
} from '../ui/index';
import { sandToolCategories } from '../../data/mockSandTools';
import type { SandTool } from '../../types/sandtool';

interface SandToolSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  availableTools: SandTool[];
  selectedToolIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  maxSelections?: number;
  title?: string;
}

interface SelectedTool {
  tool: SandTool;
  quantity: number;
}

const SandToolSelector: React.FC<SandToolSelectorProps> = ({
  isOpen,
  onClose,
  availableTools,
  selectedToolIds,
  onSelectionChange,
  maxSelections,
  title = "选择沙具"
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedTools, setSelectedTools] = useState<Map<string, number>>(
    new Map(selectedToolIds.map(id => [id, 1]))
  );

  // 筛选可用的沙具
  const filteredTools = useMemo(() => {
    return availableTools.filter(tool => {
      if (tool.available <= 0) return false;
      
      const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tool.category.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = categoryFilter === 'all' || tool.category === categoryFilter;
      
      return matchesSearch && matchesCategory;
    });
  }, [availableTools, searchTerm, categoryFilter]);

  // 获取已选择的沙具信息
  const selectedToolsInfo = useMemo(() => {
    const result: SelectedTool[] = [];
    selectedTools.forEach((quantity, toolId) => {
      const tool = availableTools.find(t => t.id === toolId);
      if (tool) {
        result.push({ tool, quantity });
      }
    });
    return result;
  }, [selectedTools, availableTools]);

  // 添加沙具
  const handleAddTool = (tool: SandTool) => {
    if (maxSelections && selectedTools.size >= maxSelections) {
      alert(`最多只能选择 ${maxSelections} 个沙具`);
      return;
    }

    const newSelectedTools = new Map(selectedTools);
    newSelectedTools.set(tool.id, 1);
    setSelectedTools(newSelectedTools);
  };

  // 移除沙具
  const handleRemoveTool = (toolId: string) => {
    const newSelectedTools = new Map(selectedTools);
    newSelectedTools.delete(toolId);
    setSelectedTools(newSelectedTools);
  };

  // 更新数量
  const handleQuantityChange = (toolId: string, quantity: number) => {
    const tool = availableTools.find(t => t.id === toolId);
    if (!tool) return;

    if (quantity <= 0) {
      handleRemoveTool(toolId);
      return;
    }

    if (quantity > tool.available) {
      alert(`数量不能超过可用库存 ${tool.available}`);
      return;
    }

    const newSelectedTools = new Map(selectedTools);
    newSelectedTools.set(toolId, quantity);
    setSelectedTools(newSelectedTools);
  };

  // 确认选择
  const handleConfirm = () => {
    const selectedIds: string[] = [];
    selectedTools.forEach((quantity, toolId) => {
      for (let i = 0; i < quantity; i++) {
        selectedIds.push(toolId);
      }
    });
    onSelectionChange(selectedIds);
    onClose();
  };

  // 取消选择
  const handleCancel = () => {
    setSelectedTools(new Map(selectedToolIds.map(id => [id, 1])));
    onClose();
  };

  // 获取类别名称
  const getCategoryName = (category: string) => {
    const categoryInfo = sandToolCategories.find(cat => cat.id === category);
    return categoryInfo?.name || category;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">{title}</h2>
            <p className="text-sm text-gray-600 mt-1">
              选择治疗过程中需要使用的沙具
              {maxSelections && ` (最多 ${maxSelections} 个)`}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={handleCancel}>
            <X size={20} />
          </Button>
        </div>

        <div className="flex h-[70vh]">
          {/* 左侧：可选沙具列表 */}
          <div className="flex-1 border-r">
            <div className="p-4 border-b">
              <div className="flex gap-md mb-4">
                <Input
                  placeholder="搜索沙具名称或类别..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search size={16} />}
                  className="flex-1"
                />
                <Select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-48"
                  options={[
                    { value: 'all', label: '全部类别' },
                    ...sandToolCategories.map(category => ({
                      value: category.id,
                      label: category.name
                    }))
                  ]}
                />
              </div>
            </div>

            <div className="overflow-y-auto h-full p-4">
              {filteredTools.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>暂无可用沙具</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredTools.map(tool => (
                    <Card key={tool.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-md">
                              <div>
                                <h4 className="font-medium">{tool.name}</h4>
                                <div className="text-sm text-gray-600">
                                  {getCategoryName(tool.category)} • {tool.size} • {tool.material}
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                  库存: {tool.available}/{tool.quantity}
                                  <Badge 
                                    variant={tool.condition === '良好' || tool.condition === '全新' ? 'success' : 'warning'}
                                    className="ml-2"
                                  >
                                    {tool.condition}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-sm">
                            {selectedTools.has(tool.id) ? (
                              <div className="flex items-center gap-sm">
                                <span className="text-sm text-green-600">已选择</span>
                                <Button
                                  variant="danger"
                                  size="sm"
                                  onClick={() => handleRemoveTool(tool.id)}
                                  leftIcon={<Minus size={14} />}
                                >
                                  移除
                                </Button>
                              </div>
                            ) : (
                              <Button
                                variant="primary"
                                size="sm"
                                onClick={() => handleAddTool(tool)}
                                leftIcon={<Plus size={14} />}
                                disabled={!!(maxSelections && selectedTools.size >= maxSelections)}
                              >
                                选择
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 右侧：已选择的沙具 */}
          <div className="w-96">
            <div className="p-4 border-b">
              <h3 className="font-medium">已选择的沙具 ({selectedToolsInfo.length})</h3>
              {maxSelections && (
                <p className="text-sm text-gray-600">
                  {selectedTools.size}/{maxSelections}
                </p>
              )}
            </div>

            <div className="overflow-y-auto h-full p-4">
              {selectedToolsInfo.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>还未选择任何沙具</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedToolsInfo.map(({ tool, quantity }) => (
                    <Card key={tool.id} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{tool.name}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveTool(tool.id)}
                          >
                            <X size={14} />
                          </Button>
                        </div>
                        <div className="text-xs text-gray-600 mb-2">
                          {getCategoryName(tool.category)} • {tool.size}
                        </div>
                        <div className="flex items-center gap-sm">
                          <span className="text-xs text-gray-500">数量:</span>
                          <div className="flex items-center gap-xs">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleQuantityChange(tool.id, quantity - 1)}
                              disabled={quantity <= 1}
                            >
                              <Minus size={12} />
                            </Button>
                            <span className="text-sm font-medium w-8 text-center">
                              {quantity}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleQuantityChange(tool.id, quantity + 1)}
                              disabled={quantity >= tool.available}
                            >
                              <Plus size={12} />
                            </Button>
                          </div>
                          <span className="text-xs text-gray-500">
                            (可用: {tool.available})
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部操作 */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            已选择 {selectedToolsInfo.length} 个沙具
            {selectedToolsInfo.reduce((sum, item) => sum + item.quantity, 0) > selectedToolsInfo.length && 
              ` (共 ${selectedToolsInfo.reduce((sum, item) => sum + item.quantity, 0)} 件)`
            }
          </div>
          <div className="flex gap-md">
            <Button variant="secondary" onClick={handleCancel}>
              取消
            </Button>
            <Button onClick={handleConfirm}>
              确认选择
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SandToolSelector;
