/* 沙具管理样式 */

/* 详情模态框样式 */
.sand-tool-detail {
  padding: 0;
}

.detail-section {
  padding: 20px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-item span {
  font-size: 14px;
  color: #1e293b;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.special-marks {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tool-image {
  max-width: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.tool-image img {
  width: 100%;
  height: auto;
  display: block;
}

.notes-content {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.5;
  background-color: #f8fafc;
  padding: 12px;
  border-radius: 6px;

}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item.full-width {
    grid-column: 1;
  }
}
