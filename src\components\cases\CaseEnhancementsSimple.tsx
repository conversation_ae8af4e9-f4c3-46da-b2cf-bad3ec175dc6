// 个案管理增强组件 - 简洁版
import React from 'react';
import { 
  AlertTriangle, 
  Bell,
  Activity
} from 'lucide-react';
import type { SimpleCase } from '../../types/case';
import { getDaysSinceLastSession, needsFollowUp } from '../../data/mockCases';
import './CaseEnhancementsSimple.css';

// 简洁的危机预警组件
interface CrisisAlertPanelProps {
  cases: SimpleCase[];
  onQuickAction: (caseId: string, action: string) => void;
}

export const CrisisAlertPanel: React.FC<CrisisAlertPanelProps> = ({ cases, onQuickAction }) => {
  const urgentCases = cases.filter(c => c.crisis === '⚠️');
  
  if (urgentCases.length === 0) return null;

  return (
    <div className="simple-alert-panel">
      <div className="panel-header">
        <AlertTriangle size={16} />
        <span>紧急个案 ({urgentCases.length})</span>
      </div>
      <div className="panel-content">
        {urgentCases.slice(0, 1).map(case_ => (
          <div key={case_.id} className="panel-item">
            <span className="item-name">{case_.name}</span>
            <button 
              className="item-action urgent"
              onClick={() => onQuickAction(case_.id, 'contact')}
            >
              立即联系
            </button>
          </div>
        ))}
        {urgentCases.length > 1 && (
          <div className="panel-more">还有 {urgentCases.length - 1} 个紧急个案</div>
        )}
      </div>
    </div>
  );
};

// 简洁的智能提醒组件
interface SmartRemindersProps {
  cases: SimpleCase[];
  onReminderAction: (type: string, caseId?: string) => void;
}

export const SmartReminders: React.FC<SmartRemindersProps> = ({ cases, onReminderAction }) => {
  const followUpCases = cases.filter(c => c.lastDate && needsFollowUp(c.lastDate));
  
  if (followUpCases.length === 0) return null;

  return (
    <div className="simple-reminder-panel">
      <div className="panel-header">
        <Bell size={16} />
        <span>需要跟进 ({followUpCases.length})</span>
      </div>
      <div className="panel-content">
        {followUpCases.slice(0, 5).map(case_ => (
          <div key={case_.id} className="panel-item">
            <span className="item-name">{case_.name}</span>
            <span className="item-info">{getDaysSinceLastSession(case_.lastDate)}天</span>
            <button 
              className="item-action"
              onClick={() => onReminderAction('followup', case_.id)}
            >
              处理
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

// 简洁的工作负荷指示器
interface WorkloadIndicatorProps {
  totalCases: number;
  activeCases: number;
  urgentCases: number;
}

export const WorkloadIndicator: React.FC<WorkloadIndicatorProps> = ({ 
  totalCases, 
  activeCases, 
  urgentCases 
}) => {
  const getWorkloadStatus = () => {
    if (urgentCases > 3) return { level: 'high', text: '工作负荷高', color: '#ef4444' };
    if (activeCases > 20) return { level: 'medium', text: '工作负荷中等', color: '#f59e0b' };
    return { level: 'normal', text: '工作负荷正常', color: '#10b981' };
  };

  const status = getWorkloadStatus();

  return (
    <div className="simple-workload-panel">
      <div className="panel-header">
        <Activity size={16} />
        <span>{status.text}</span>
      </div>
      <div className="workload-stats">
        <div className="stat-item">
          <span className="stat-number">{totalCases}</span>
          <span className="stat-label">总个案</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{activeCases}</span>
          <span className="stat-label">活跃</span>
        </div>
        <div className="stat-item">
          <span className="stat-number" style={{ color: status.color }}>{urgentCases}</span>
          <span className="stat-label">紧急</span>
        </div>
      </div>
    </div>
  );
};

// 专业发展提醒组件 - 简化版
interface ProfessionalDevelopmentProps {
  urgentCases: number;
  supervisionNotes: number;
}

export const ProfessionalDevelopment: React.FC<ProfessionalDevelopmentProps> = ({ 
  urgentCases, 
  supervisionNotes 
}) => {
  const getMessage = () => {
    if (urgentCases > 3) return '注意工作负荷，建议寻求督导支持';
    if (supervisionNotes < 3) return '建议增加督导记录，提升专业反思';
    return '工作负荷正常';
  };

  const getLevel = () => {
    if (urgentCases > 3) return 'high';
    if (supervisionNotes < 3) return 'medium';
    return 'normal';
  };

  return (
    <div className={`simple-development-panel ${getLevel()}`}>
      <div className="panel-header">
        <span>📚 专业发展提醒</span>
      </div>
      <div className="panel-content">
        <p>{getMessage()}</p>
      </div>
    </div>
  );
};

// 其他简化组件的占位符，保持原有接口
export const QuickRecordTemplates: React.FC = () => null;
export const AIKeywordSuggestions: React.FC = () => null;
export const SupervisionHighlight: React.FC = () => null;
export const ProgressTrend: React.FC = () => null;
