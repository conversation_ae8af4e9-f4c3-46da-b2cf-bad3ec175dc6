import type { MoodOption, MoodResponse, MoodType } from '../types/moodSelection';

// 心情选项配置
export const moodOptions: MoodOption[] = [
  {
    id: 'joy',
    emoji: '🌸',
    name: '喜悦',
    color: '#ec4899',
    bgColor: '#fdf2f8'
  },
  {
    id: 'calm',
    emoji: '☀',
    name: '平静',
    color: '#f59e0b',
    bgColor: '#fffbeb'
  },
  {
    id: 'nervous',
    emoji: '🌊',
    name: '紧张',
    color: '#3b82f6',
    bgColor: '#eff6ff'
  },
  {
    id: 'melancholy',
    emoji: '🌧',
    name: '忧郁',
    color: '#6b7280',
    bgColor: '#f9fafb'
  },
  {
    id: 'tired',
    emoji: '🌑',
    name: '疲惫',
    color: '#374151',
    bgColor: '#f3f4f6'
  },
  {
    id: 'expectant',
    emoji: '⭐',
    name: '期待',
    color: '#8b5cf6',
    bgColor: '#faf5ff'
  }
];

// 为保证离线 3 年使用的丰富性, 每类生成 >= 60 条(总 >= 360) 可轮换避免短期重复
// 生成逻辑: 基础模板 + 语义片段组合, 避免完全重复
interface MoodFragment { prefix: string; core: string; suffix: string; }

const moodFragments: Record<MoodType, MoodFragment[]> = {
  joy: [
    { prefix: '今天的喜悦像', core: '晨光', suffix: '一样清新, 让它在沙盘里延伸。' },
    { prefix: '你带来的光像', core: '春芽', suffix: '一般安静却充满力量。' },
    { prefix: '这份开心如', core: '微风', suffix: '掠过心湖, 留下圈圈涟漪。' },
    { prefix: '你的笑意似', core: '一朵新开的花', suffix: '，轻轻提醒世界可以更柔软。' },
    { prefix: '这股能量像', core: '清晨第一缕暖色', suffix: '，适合被好好珍藏。' },
    { prefix: '喜悦正在像', core: '细雨后的光斑', suffix: '，悄悄洒在一切角落。' },
    { prefix: '你的内在亮度像', core: '群星的余温', suffix: '，稳稳地陪着自己。' }
  ],
  calm: [
    { prefix: '此刻的平静如', core: '缓慢潮汐', suffix: '，来回抚平紧绷。' },
    { prefix: '你的安宁像', core: '一池澄澈的水', suffix: '， quietly 养护着力量。' },
    { prefix: '沉着像', core: '午后柔光', suffix: '，不耀眼却持续在场。' },
    { prefix: '平和正如', core: '一段均匀呼吸', suffix: '，自然而不需用力。' },
    { prefix: '稳定像', core: '岩层里的温热', suffix: '，被时间悄悄凝结。' },
    { prefix: '你的从容似', core: '慢速旋转的叶', suffix: '，轻而有节奏。' },
    { prefix: '宁静像', core: '雨后空气的淡香', suffix: '，需要细细感受。' }
  ],
  nervous: [
    { prefix: '这份紧张像', core: '未调匀的弦', suffix: '，调整一下会更动听。' },
    { prefix: '你感到的波动如', core: '浪前的起伏', suffix: '，是力量蓄积的信号。' },
    { prefix: '心跳的快节奏像', core: '赶路的轻步', suffix: '，慢下来会更稳。' },
    { prefix: '紧绷似', core: '风前轻摆的枝叶', suffix: '，仍然扎根土壤。' },
    { prefix: '这种在意像', core: '航前的检查', suffix: '，说明你认真投入。' },
    { prefix: '情绪的起伏像', core: '云层翻卷', suffix: '，终会透出亮光。' },
    { prefix: '你的专注像', core: '拉满的弓弦', suffix: '，记得适度放松。' }
  ],
  melancholy: [
    { prefix: '此刻的情绪像', core: '薄雾', suffix: '，会在温度里慢慢散开。' },
    { prefix: '淡淡忧伤如', core: '低空缓行的云', suffix: '，只是暂时停驻。' },
    { prefix: '你的小低落像', core: '暮色未收的余灰', suffix: '，无需赶走。' },
    { prefix: '沉静的感受似', core: '雨前压低的风', suffix: '，也含着转晴的预告。' },
    { prefix: '这份情绪像', core: '未开封的信', suffix: '，等你慢慢去读。' },
    { prefix: '心里的阴影像', core: '树下柔软的暗影', suffix: '，与光共生。' },
    { prefix: '你的安静像', core: '深水缓流', suffix: '，沉稳中孕育更新。' }
  ],
  tired: [
    { prefix: '这份疲倦像', core: '旅途中磨损的鞋底', suffix: '，需要被好好护理。' },
    { prefix: '你的乏力如', core: '熄火后尚温的炉壁', suffix: '，仍保留核心余热。' },
    { prefix: '倦意像', core: '长途列车的轻晃', suffix: '，提示该稍作停靠。' },
    { prefix: '需要休息像', core: '树在冬天的停生长', suffix: '，是为了再次萌发。' },
    { prefix: '身体的讯号似', core: '渐暗的灯芯', suffix: '，调低亮度也是照顾。' },
    { prefix: '你的慢下来像', core: '潮水回撤', suffix: '，为下一次涌动储能。' },
    { prefix: '这份停顿像', core: '翻页前的空白', suffix: '，也是故事一部分。' }
  ],
  expectant: [
    { prefix: '期待像', core: '晨空第一颗褪不掉的星', suffix: '，指向未写的篇章。' },
    { prefix: '心中的盼望似', core: '种子埋在温润土里', suffix: '，静默积累。' },
    { prefix: '你的好奇像', core: '刚展开的纸卷', suffix: '，边缘还带着卷曲。' },
    { prefix: '这种跃动如', core: '风起前湖面的轻纹', suffix: '，预示新方向。' },
    { prefix: '向前的冲力像', core: '地平线泛起的亮色', suffix: '，正在扩散。' },
    { prefix: '你的愿景似', core: '尚未成形的雕坯', suffix: '，可以慢慢打磨。' },
    { prefix: '这份盼头像', core: '旅途中远方微光', suffix: '，足够支撑脚步。' }
  ]
};

function expandMood(mood: MoodType, base: MoodFragment[]): MoodResponse[] {
  const extrasA = ['静静流动', '轻柔存在', '悄悄积累', '缓缓展开', '默默支撑', '持续发亮'];
  const extrasB = ['请给它空间', '值得被记录', '让它自然生长', '无需过度控制', '它正在成形', '相信自己的节奏'];
  const responses: MoodResponse[] = [];
  let id = 1;
  for (const frag of base) {
    for (const a of extrasA) {
      for (const b of extrasB) {
        if (responses.length >= 60) break; // 每类 60 条
        const message = `${frag.prefix}${frag.core}${frag.suffix}${a}，${b}。`;
        responses.push({
          id: `${mood}_${id++}`,
          moodType: mood,
          message,
          category: mood === 'joy' ? '积极回应' : mood === 'calm' ? '温和鼓励' : mood === 'nervous' ? '安抚支持' : mood === 'melancholy' ? '温柔陪伴' : mood === 'tired' ? '关怀体贴' : '希望激励'
        });
      }
      if (responses.length >= 60) break;
    }
  }
  return responses.slice(0, 60);
}

export const moodResponses: Record<MoodType, MoodResponse[]> = {
  joy: expandMood('joy', moodFragments.joy),
  calm: expandMood('calm', moodFragments.calm),
  nervous: expandMood('nervous', moodFragments.nervous),
  melancholy: expandMood('melancholy', moodFragments.melancholy),
  tired: expandMood('tired', moodFragments.tired),
  expectant: expandMood('expectant', moodFragments.expectant)
};

// 获取随机心情回应
export const getRandomMoodResponse = (moodType: MoodType): MoodResponse => {
  const responses = moodResponses[moodType];
  const randomIndex = Math.floor(Math.random() * responses.length);
  return responses[randomIndex];
};

// 根据心情类型获取动画类名
export const getMoodAnimation = (moodType: MoodType): string => {
  const animations: Record<MoodType, string> = {
    joy: 'mood-animation-joy',
    calm: 'mood-animation-calm',
    nervous: 'mood-animation-nervous',
    melancholy: 'mood-animation-melancholy',
    tired: 'mood-animation-tired',
    expectant: 'mood-animation-expectant'
  };
  return animations[moodType];
};
