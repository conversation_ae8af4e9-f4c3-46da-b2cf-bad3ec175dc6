/**
 * 激活码验证器 - 与 loin 同步（先使用 localStorage 版本，后续可切换 DB 存储）
 */

import { b64u } from './base64url';
import { getPublicKey } from './publicKeys';
import DeviceIdGenerator from './deviceId';

interface Payload {
  v: number;    // license schema version
  kv: number;   // key version
  d: string;    // device hash
  sid: string;  // salt id
  ia: number;   // issued at
}

interface StoredActivation {
  lic: string;    // 原始激活码
  dLock: string;  // 设备锁定哈希
  hmac: string;   // HMAC防篡改签名
}

const K_PARTS = ['A1', 'b9', 'Z_', '7q', 'x!', 'm#', 'R2', 'p@'];
function buildHmacKey(): string {
  return K_PARTS.slice().reverse().join('');
}

async function sha256Hex(data: string): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function hmacHex(data: string): Promise<string> {
  const key = buildHmacKey();
  const encoder = new TextEncoder();
  const keyBuffer = encoder.encode(key);
  const dataBuffer = encoder.encode(data);
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
  const signatureArray = Array.from(new Uint8Array(signature));
  return signatureArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

function parseActivationCode(code: string): { payload: Payload; p64: string; s64: string } | null {
  try {
    const clean = code.replace(/\s|-/g, '');
    const parts = clean.split('.');
    if (parts.length !== 2) return null;
    const [p64, s64] = parts;
    const payloadJson = b64u.decodeString(p64);
    const payload = JSON.parse(payloadJson) as Payload;
    return { payload, p64, s64 };
  } catch {
    return null;
  }
}

async function verifySignature(p64: string, s64: string, publicKey: string): Promise<boolean> {
  try {
    const keyData = publicKey
      .replace('-----BEGIN PUBLIC KEY-----', '')
      .replace('-----END PUBLIC KEY-----', '')
      .replace(/\s/g, '');
    const keyBuffer = Uint8Array.from(atob(keyData), (c) => c.charCodeAt(0));
    const cryptoKey = await crypto.subtle.importKey(
      'spki',
      keyBuffer,
      { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },
      false,
      ['verify']
    );
    const dataBuffer = new TextEncoder().encode(p64);
    const signatureBuffer = b64u.dec(s64);
    return await crypto.subtle.verify('RSASSA-PKCS1-v1_5', cryptoKey, signatureBuffer, dataBuffer);
  } catch (error) {
    console.error('签名验证失败:', error);
    return false;
  }
}

async function deviceHash(canonical: string): Promise<string> {
  const hash = await sha256Hex(canonical);
  const hashBuffer = new Uint8Array(hash.match(/.{1,2}/g)!.map((byte) => parseInt(byte, 16)));
  return b64u.enc(hashBuffer);
}

const STORE_KEY = 'activation_v1';

export async function activate(code: string): Promise<boolean> {
  try {
    const parsed = parseActivationCode(code);
    if (!parsed) return false;

    const publicKey = getPublicKey(parsed.payload.kv);
    if (!publicKey) return false;

    if (!(await verifySignature(parsed.p64, parsed.s64, publicKey))) return false;

    const { canonical } = await DeviceIdGenerator.getDeviceCode();
    const currentHash = await deviceHash(canonical);
    if (parsed.payload.d !== currentHash) return false;

    const dLock = await sha256Hex(currentHash + parsed.payload.sid);
    const licRaw = `${parsed.p64}.${parsed.s64}`;
    const hmac = await hmacHex(licRaw + dLock);

    const activation: StoredActivation = { lic: licRaw, dLock, hmac };
    localStorage.setItem(STORE_KEY, JSON.stringify(activation));
    return true;
  } catch (error) {
    console.error('激活过程出错:', error);
    return false;
  }
}

export async function checkActivated(): Promise<boolean> {
  try {
    const raw = localStorage.getItem(STORE_KEY);
    if (!raw) return false;

    const stored: StoredActivation = JSON.parse(raw);
    const parsed = parseActivationCode(stored.lic);
    if (!parsed) return false;

    const publicKey = getPublicKey(parsed.payload.kv);
    if (!publicKey) return false;

    if (!(await verifySignature(parsed.p64, parsed.s64, publicKey))) return false;

    const { canonical } = await DeviceIdGenerator.getDeviceCode();
    const currentHash = await deviceHash(canonical);
    const dLock = await sha256Hex(currentHash + parsed.payload.sid);
    if (dLock !== stored.dLock) return false;

    const expectedHmac = await hmacHex(stored.lic + stored.dLock);
    if (expectedHmac !== stored.hmac) return false;

    return true;
  } catch (error) {
    console.error('检查激活状态出错:', error);
    return false;
  }
}

export function revoke(): void {
  localStorage.removeItem(STORE_KEY);
}

/**
 * 通过文件激活软件
 */
export async function activateWithFile(file: File): Promise<boolean> {
  try {
    // 读取文件内容
    const fileContent = await readFileContent(file);

    // 解析激活文件
    let activationData;
    try {
      activationData = JSON.parse(fileContent);
    } catch {
      console.error('激活文件格式无效');
      return false;
    }

    // 验证激活文件结构
    if (!activationData.activationCode || !activationData.deviceCode || !activationData.timestamp) {
      console.error('激活文件缺少必要字段');
      return false;
    }

    // 使用激活码进行激活
    return await activate(activationData.activationCode);

  } catch (error) {
    console.error('文件激活失败:', error);
    return false;
  }
}

/**
 * 读取文件内容
 */
function readFileContent(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve(content);
    };
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    reader.readAsText(file, 'utf-8');
  });
}

