import React, { useState, useEffect } from 'react';
import { User, Bell, Database } from 'lucide-react';
import { PageContainer, PageHeader } from '../ui/Layout';
import { settingsService } from '../../services/databaseSettingsService';
import type { AppSettings, SettingsTab } from '../../types/settings';
import UserSettings from './UserSettings';
import NotificationSettings from './NotificationSettings';
import DataManagement from './DataManagement';
import './Settings.css';

const Settings: React.FC = () => {
  // 获取默认设置
  const getDefaultSettings = (): AppSettings => ({
    user: {
      profile: {
        name: '',
        title: ''
      },
      preferences: {
        defaultSessionDuration: 60,
        workingHours: {
          start: '09:00',
          end: '18:00'
        },
        autoSave: true
      }
    },

    notifications: {
      appointments: {
        enabled: true,
        advanceTime: 15
      },
      system: {
        updates: true,
        errors: true,
        maintenance: true
      }
    },
    dataManagement: {
      backup: {
        autoBackup: true,
        backupTime: '02:00',
        frequency: 'daily',
        location: 'local',
        encryption: false
      },
      export: {
        format: 'xlsx',
        dateRange: 'all',
        includeImages: true,
        compression: true
      },
      cleanup: {
        tempFiles: true,
        logs: true,
        cache: true,
        oldBackups: true
      }
    },
    version: '1.0.0',
    lastUpdated: new Date().toISOString()
  });

  const [settings, setSettings] = useState<AppSettings>(getDefaultSettings());
  const [activeTab, setActiveTab] = useState<SettingsTab>('user');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // 标签页配置
  const tabs = [
    {
      key: 'user',
      label: '用户设置',
      icon: <User size={16} />,
      description: '工作偏好设置'
    },

    {
      key: 'notifications',
      label: '通知设置',
      icon: <Bell size={16} />,
      description: '提醒和通知配置'
    },
    {
      key: 'data',
      label: '数据管理',
      icon: <Database size={16} />,
      description: '备份、导出和清理'
    }
  ];

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const loadedSettings = await settingsService.getSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error('加载设置失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 保存设置
  const handleSaveSettings = async (newSettings: AppSettings) => {
    setSaving(true);
    try {
      await settingsService.saveSettings(newSettings);
      setSettings(newSettings);
      // 这里可以添加成功提示
      console.log('设置保存成功');
    } catch (error) {
      console.error('保存设置时出错:', error);
      // 这里可以添加错误提示
    } finally {
      setSaving(false);
    }
  };

  // 更新部分设置
  const updateSettings = (path: string, value: unknown) => {
    const newSettings = { ...settings };
    setNestedValue(newSettings, path, value);
    handleSaveSettings(newSettings);
  };

  // 设置嵌套值的辅助函数
  const setNestedValue = (obj: Record<string, unknown>, path: string, value: unknown) => {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key] as Record<string, unknown>;
    }
    
    current[keys[keys.length - 1]] = value;
  };

  // 渲染标签页内容
  const renderTabContent = () => {
    if (loading) {
      return (
        <div className="settings-loading">
          <div className="loading-spinner">加载中...</div>
        </div>
      );
    }

    const commonProps = {
      settings,
      updateSettings,
      saving
    };

    switch (activeTab) {
      case 'user':
        return <UserSettings {...commonProps} />;

      case 'notifications':
        return <NotificationSettings {...commonProps} />;
      case 'data':
        return <DataManagement {...commonProps} onSettingsChange={handleSaveSettings} />;
      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <PageHeader
        title="系统设置"
        subtitle="配置系统参数，个性化您的工作环境"
      />
      
      <div className="settings-container">
        <div className="settings-layout">
          {/* 左侧标签页导航 */}
          <div className="settings-sidebar">
            <div className="settings-nav">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  className={`settings-nav-item ${activeTab === tab.key ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.key as SettingsTab)}
                >
                  <div className="nav-item-icon">{tab.icon}</div>
                  <div className="nav-item-content">
                    <div className="nav-item-label">{tab.label}</div>
                    <div className="nav-item-description">{tab.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 右侧内容区域 */}
          <div className="settings-content">
            <div className="settings-content-header">
              <div className="content-header-info">
                <div className="content-header-icon">
                  {tabs.find(tab => tab.key === activeTab)?.icon}
                </div>
                <div>
                  <h3 className="content-header-title">
                    {tabs.find(tab => tab.key === activeTab)?.label}
                  </h3>
                  <p className="content-header-description">
                    {tabs.find(tab => tab.key === activeTab)?.description}
                  </p>
                </div>
              </div>
              {saving && (
                <div className="content-header-status">
                  <div className="saving-indicator">
                    <div className="saving-spinner"></div>
                    <span>保存中...</span>
                  </div>
                </div>
              )}
            </div>

            <div className="settings-content-body">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Settings;
