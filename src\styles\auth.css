/* 自定义标题栏窗口控件 */
.window-controls{ position: fixed; top: 10px; right: 12px; display:flex; gap:8px; z-index: 50; }
.wc-btn{ width: 14px; height:14px; border-radius: 9999px; border: none; padding:0; display:flex; align-items:center; justify-content:center; background: transparent; cursor: pointer; }
.wc-btn .wc-dot{ width: 100%; height:100%; border-radius:9999px; display:block; box-shadow: inset 0 0 6px rgba(0,0,0,.25); }
.wc-minimize .wc-dot{ background:#fdbc40; }
.wc-maximize .wc-dot{ background:#34c749; }
.wc-close .wc-dot{ background:#ff5f57; }
.wc-btn:hover{ transform: scale(1.08); }


/* Aurora Login Styles - 纯CSS（无Tailwind），与 loin 视觉一致 */

/* 容器与背景 */
body.login-mode {
  background-color: #0b0b13;
  color: #fff;
  display: block; /* 覆盖全局 index.css 的 flex 布局 */
  min-height: 100vh;
  -webkit-app-region: drag; /* 整个登录页面可拖拽 */
}

/* 毛玻璃 */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 渐变边框卡片背景 */
.gradient-border { position: relative; background: linear-gradient(135deg, rgba(59,130,246,0.1), rgba(255,255,255,0.1), rgba(249,115,22,0.1)); }
.gradient-border::before { content: ''; position: absolute; inset: 0; padding: 1px; background: linear-gradient(135deg, #3b82f6, #ffffff, #f97316); border-radius: inherit; -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask-composite: xor; mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; }

/* 卡片外观 - 增强圆角视觉效果 */
.card-border {
  background: rgba(59, 130, 246, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1); /* 增加边框宽度 */
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: 
    inset 0 0 30px rgba(59,130,246,0.15), 
    inset 0 0 60px rgba(255,255,255,0.08), 
    0 0 50px rgba(249,115,22,0.25),
    0 8px 32px rgba(0,0,0,0.3); /* 增强阴影效果 */
}

/* 输入框 - 增强圆角和边框效果 */
.input-field {
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(59, 130, 246, 0.4); /* 增加边框宽度和透明度 */
  color: #fff;
  transition: all 0.3s ease;
  -webkit-app-region: no-drag; /* 输入框不可拖拽 */
}
.input-field:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(249, 115, 22, 0.7); /* 增强焦点边框颜色 */
  box-shadow: 0 0 25px rgba(249, 115, 22, 0.4), 0 0 10px rgba(59, 130, 246, 0.2); /* 增加双重发光效果 */
}

/* 登录按钮 */
.login-button {
  background: #3b82f6;
  transition: all 0.3s ease;
  border: 0;
  -webkit-app-region: no-drag; /* 按钮不可拖拽 */
}
.login-button:hover { background: #2563eb; box-shadow: 0 0 20px rgba(59,130,246,0.4); }

/* 设备码模态样式 - 优化尺寸和滚动 */
.device-code-modal {
  background: rgba(59,130,246,0.08);
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: inset 0 0 30px rgba(59,130,246,0.1), inset 0 0 60px rgba(255,255,255,0.05), 0 0 50px rgba(249,115,22,0.2), 0 20px 40px rgba(0,0,0,0.3);
  max-width: 480px;
  width: 90vw;
}
.device-code-display { background: linear-gradient(135deg, rgba(59,130,246,0.05), rgba(249,115,22,0.05)); border: 1px solid transparent; position: relative; backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); }
.device-code-display::before { content: ''; position: absolute; inset: 0; padding: 1px; background: linear-gradient(135deg, #3b82f6, rgba(255,255,255,0.3), #f97316); border-radius: inherit; -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask-composite: xor; mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; }
.device-code-text { background: linear-gradient(135deg, #3b82f6, #ffffff, #f97316); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; letter-spacing: 0.15em; font-weight: 600; }
.device-code-spinner { width: 24px; height: 24px; border: 3px solid transparent; border-radius: 50%; background: linear-gradient(135deg, #3b82f6, #f97316) padding-box, linear-gradient(135deg, #3b82f6, #f97316) border-box; mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #fff calc(100% - 3px)); -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #fff calc(100% - 3px)); animation: device-code-spin 1.5s linear infinite; }

/* 动画 */
@keyframes device-code-spin { 0% { transform: rotate(0deg);} 100% { transform: rotate(360deg);} }
@keyframes modal-enter { 0% { opacity:0; transform: scale(0.9) translateY(-10px);} 100% { opacity:1; transform: scale(1) translateY(0);} }
@keyframes float { 0%,100% { transform: translateY(0);} 50% { transform: translateY(-5px);} }
@keyframes login-pulse { 0%,100% { box-shadow: 0 0 20px rgba(59,130,246,0.3);} 50% { box-shadow: 0 0 30px rgba(249,115,22,0.4);} }
@keyframes field-glow { 0% { box-shadow: 0 0 5px rgba(59,130,246,0.3);} 100% { box-shadow: 0 0 20px rgba(249,115,22,0.4);} }

.animate-modal-enter { animation: modal-enter 0.3s ease-out; }
.animate-float { animation: float 6s ease-in-out infinite; }
.animate-login-pulse { animation: login-pulse 3s ease-in-out infinite; }
.animate-field-glow { animation: field-glow 0.3s ease-out; }

/* 工具类（替代少量 Tailwind 类） */
.fixed { position: fixed; }
.inset-0 { top:0; right:0; bottom:0; left:0; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.pb-0 { padding-bottom: 0; }
.pb-4 { padding-bottom: 1rem; }
.px-2 { padding-left: .5rem; padding-right: .5rem; }
.px-3 { padding-left: .75rem; padding-right: .75rem; }
.py-1 { padding-top: .25rem; padding-bottom: .25rem; }
.py-2 { padding-top: .5rem; padding-bottom: .5rem; }
.py-3 { padding-top: .75rem; padding-bottom: .75rem; }
.rounded-2xl { border-radius: 2rem; } /* 增加主卡片圆角 */
.rounded-full { border-radius: 9999px; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-orange-400\/20 { border-color: rgba(251, 146, 60, 0.2); }
.border-blue-400\/30 { border-color: rgba(96, 165, 250, 0.3); }
.border-white\/20 { border-color: rgba(255,255,255,0.2); }
.text-white { color: #fff; }
.text-white\/60 { color: rgba(255,255,255,0.6); }
.text-white\/80 { color: rgba(255,255,255,0.8); }
.text-green-300 { color: #86efac; }
.text-blue-400 { color: #60a5fa; }
.text-orange-400 { color: #fb923c; }
.text-orange-300 { color: #fdba74; }
.text-red-300 { color: #fca5a5; }
.text-blue-300 { color: #93c5fd; }
.bg-black\/50 { background-color: rgba(0,0,0,0.5); }
.bg-white\/5 { background-color: rgba(255,255,255,0.05); }
.bg-white\/10 { background-color: rgba(255,255,255,0.1); }
.bg-blue-500\/10 { background-color: rgba(59,130,246,0.1); }
.bg-red-500\/10 { background-color: rgba(239,68,68,0.1); }
.bg-orange-400 { background-color: #fb923c; }
.bg-green-400 { background-color: #4ade80; }
.border-red-400\/30 { border-color: rgba(248,113,113,0.3); }
.border-blue-400\/20 { border-color: rgba(96,165,250,0.2); }
.border-green-400\/20 { border-color: rgba(74,222,128,0.2); }
.border-orange-400\/20 { border-color: rgba(251,146,60,0.2); }
.border-blue-400\/30 { border-color: rgba(96,165,250,0.3); }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.grid { display: grid; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0,1fr)); }
.gap-3 { gap: .75rem; }
.space-x-2 > * + * { margin-left: .5rem; }
.space-x-3 > * + * { margin-left: .75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.relative { position: relative; }
.absolute { position: absolute; }
.-top-2 { top: -0.5rem; }
.-right-2 { right: -0.5rem; }
.-bottom-1 { bottom: -0.25rem; }
.-left-2 { left: -0.5rem; }
.top-1\/2 { top: 50%; }
.rounded-lg { border-radius: 1rem; } /* 增加输入框和按钮圆角 */
.mb-2 { margin-bottom: .5rem; }
.mb-3 { margin-bottom: .75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-6 { margin-top: 1.5rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-mono { font-family: ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace; }
.text-sm { font-size: .875rem; line-height: 1.25rem; }
.w-2 { width: .5rem; } .h-2 { height: .5rem; }
.w-3 { width: .75rem; } .h-3 { height: .75rem; }
.w-4 { width: 1rem; } .h-4 { height: 1rem; }
.w-16 { width: 4rem; } .h-16 { height: 4rem; }
.w-12 { width: 3rem; } .h-12 { height: 3rem; }
/* 额外圆角优化 */
.rounded { border-radius: .375rem; }
.rounded-xl { border-radius: 1.5rem; } /* 增加内部容器圆角 */
.rounded { border-radius: .5rem; } /* 增加基础圆角 */
.border-white\/20 { border-color: rgba(255,255,255,0.2); }
.overflow-hidden { overflow: hidden; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); white-space: nowrap; border: 0; }
.hover\:bg-white\/10:hover { background-color: rgba(255,255,255,0.1); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px rgba(0,0,0,.25); }
.transition { transition: all 0.2s ease; }
/* 输入框与图标布局优化 */
.input-field {
  padding-right: 2.5rem !important; /* 增加右侧padding防止文字与图标重叠 */
  padding-left: 1rem !important; /* 增加左侧padding确保placeholder文本与边缘有足够留白 */
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

/* 输入尾部图标对齐修正 */
.input-suffix {
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding-right: 0.75rem;
  pointer-events: none;
}

.input-suffix svg{
  display:block;
  transform: translateY(1px);
}

.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59,130,246,0.5); }

/* 布局工具 */
.min-h-screen { min-height: 100vh; }
.w-screen { width: 100vw; }

/* 文本对齐 */
.text-center { text-align: center; }

/* 链接色 */
.text-blue-400 { color: #60a5fa; }
.text-blue-400:hover { color: #93c5fd; }
/* --- 扩展工具类，补齐 loin 登录所需 --- */
.flex-col { flex-direction: column; }
.h-32 { height: 8rem; }
.max-w-md { max-width: 28rem; }
.w-10 { width: 2.5rem; } .h-10 { height: 2.5rem; }
.mr-1 { margin-right: .25rem; } .mr-2 { margin-right: .5rem; }
.ml-2 { margin-left: .5rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.pr-3 { padding-right: .75rem; }
.inset-y-0 { top: 0; bottom: 0; }
.right-2 { right: .5rem; } .bottom-2 { bottom: .5rem; }
.text-xs { font-size: .75rem; line-height: 1rem; }
.text-white\/40 { color: rgba(255,255,255,0.4); }
.text-white\/70 { color: rgba(255,255,255,0.7); }
.text-blue-300 { color: #93c5fd; }
.hover\:text-blue-300:hover { color: #93c5fd; }
.tracking-wider { letter-spacing: .05em; }

/* 背景模糊 */
.backdrop-blur-sm { backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); }

/* 渐变工具（按需定义组合） */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--grad-from, rgba(96,165,250,0.1)), var(--grad-to, rgba(59,130,246,0.2))); }
.bg-gradient-to-br { background-image: linear-gradient(135deg, var(--grad-from, rgba(96,165,250,0.2)), var(--grad-to, rgba(249,115,22,0.2))); }
.from-blue-400\/10 { --grad-from: rgba(96,165,250,0.1); }
.to-blue-600\/10 { --grad-to: rgba(37,99,235,0.1); }
.from-orange-400\/10 { --grad-from: rgba(251,146,60,0.1); }
.to-orange-600\/10 { --grad-to: rgba(234,88,12,0.1); }
.from-yellow-400\/5 { --grad-from: rgba(250,204,21,0.05); }
.to-orange-400\/5 { --grad-to: rgba(251,146,60,0.05); }
.from-blue-400\/20 { --grad-from: rgba(96,165,250,0.2); }
.to-orange-400\/20 { --grad-to: rgba(251,146,60,0.2); }

/* 动画补充 */
@keyframes pulse { 50% { opacity: .5; } }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4,0,0.6,1) infinite; }
@keyframes spin { to { transform: rotate(360deg);} }
.animate-spin { animation: spin 1s linear infinite; }

/* placeholder 颜色 */
.input-field::placeholder, .placeholder-white\/40::placeholder { color: rgba(255,255,255,0.4); }

/* disabled 状态工具 */
.disabled\:opacity-50:disabled { opacity: .5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* 交互元素不可拖拽 */
button, input, select, textarea, a, .clickable {
  -webkit-app-region: no-drag;
}

/* 模态框内容不可拖拽 */
.device-code-modal, .device-code-modal * {
  -webkit-app-region: no-drag;
}


