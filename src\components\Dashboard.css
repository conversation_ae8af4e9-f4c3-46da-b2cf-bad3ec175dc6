/* Dashboard组件样式 - 优化竖向比例 */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
  min-height: calc(100vh - 60px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard::-webkit-scrollbar {
  display: none;
}

/* 欢迎横幅 - 移除渐变色，优化高度 */
.welcome-banner {
  background: #3b82f6;
  border-radius: 12px;
  padding: 24px;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.12);
  flex-shrink: 0;
  height: auto;
  min-height: 120px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: white;
}

.welcome-text p {
  margin: 4px 0;
  opacity: 0.9;
  font-size: 16px;
  color: white;
}

.welcome-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.logo-image {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

/* 统计卡片网格 - 移除margin，使用gap */
.stats-grid {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  border: 1px solid #e5e7eb;
  transition: all 0.15s ease;
  flex: 1;
  min-width: 0;
}

.stat-card:hover {
  /* transform: translateY(-1px); */
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

/* 轮播图区域 - 优化适配3:1比例图片 */
.carousel-section {
  padding: 0;
  flex-shrink: 0;
  height: 360px; /* 增加高度以更好适配3:1比例 */
  margin-bottom: 24px;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 16px;
}

.carousel-slides {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-slide {
  min-width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.carousel-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 40px;
  /* 移除背景，让图片完全展示 */
}

.carousel-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 12px 0;
  letter-spacing: -0.025em;
  /* 多重文字阴影确保可读性 */
  text-shadow: 
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(0, 0, 0, 0.6),
    0 0 16px rgba(0, 0, 0, 0.4);
}

.carousel-subtitle {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 16px 0;
  opacity: 0.95;
  /* 增强文字阴影 */
  text-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.8),
    0 0 6px rgba(0, 0, 0, 0.5);
}

.carousel-description {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 24px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 24px;
  /* 增强文字阴影 */
  text-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.8),
    0 0 6px rgba(0, 0, 0, 0.5);
}

/* 毛玻璃风格的了解更多按钮 */
.carousel-button {
  display: inline-block;
  padding: 12px 32px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.6),
    0 0 4px rgba(0, 0, 0, 0.4);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.carousel-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.carousel-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 
    0 3px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 箭头按钮 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 20;
}

.carousel-arrow:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.carousel-arrow-left {
  left: 20px;
}

.carousel-arrow-right {
  right: 20px;
}

/* 指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 20;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-indicator:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.carousel-indicator.active {
  background: white;
  border-color: white;
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 每日工作台区域 - 1x2布局 */
.daily-workspace-section {
  flex-shrink: 0;
  margin-bottom: 24px;
}

.daily-workspace-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: stretch;
}

/* 每日工作台加载状态 */
.daily-workspace-loading {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
    gap: 20px;
  }
  
  .welcome-banner {
    padding: 24px 20px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .welcome-text h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .carousel-section {
    height: 280px; /* 移动端适度降低高度 */
    margin-bottom: 20px;
    border-radius: 12px;
  }
  
  .carousel-container {
    border-radius: 12px;
  }
  
  .carousel-content {
    padding: 0 24px;
  }
  
  .carousel-button {
    padding: 10px 24px;
    font-size: 14px;
  }
  
  .carousel-title {
    font-size: 1.75rem;
  }
  
  .carousel-subtitle {
    font-size: 1rem;
  }
  
  .carousel-description {
    font-size: 0.875rem;
    line-height: 1.5;
  }
  
  .carousel-arrow {
    width: 40px;
    height: 40px;
  }
  
  .carousel-arrow-left {
    left: 12px;
  }
  
  .carousel-arrow-right {
    right: 12px;
  }
  
  .carousel-indicators {
    bottom: 16px;
    gap: 10px;
  }
  
  .carousel-indicator {
    width: 10px;
    height: 10px;
  }
  
  .daily-workspace-section {
    margin-bottom: 20px;
  }
  
  .daily-workspace-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 平板设备适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .dashboard {
    gap: 20px;
  }
  
  .carousel-section {
    height: 320px; /* 平板端适中高度 */
    border-radius: 14px;
  }
  
  .carousel-container {
    border-radius: 14px;
  }
  
  .carousel-content {
    padding: 0 32px;
  }
  
  .carousel-button {
    padding: 11px 28px;
    font-size: 15px;
  }
  
  .carousel-title {
    font-size: 2.25rem;
  }
  
  .carousel-subtitle {
    font-size: 1.125rem;
  }
  
  .carousel-arrow {
    width: 44px;
    height: 44px;
  }
  
  .carousel-arrow-left {
    left: 16px;
  }
  
  .carousel-arrow-right {
    right: 16px;
  }
  
  .stats-grid {
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
    gap: 12px;
  }
  
  .stat-value {
    font-size: 28px;
  }
}
