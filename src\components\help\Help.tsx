import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageHeader } from '../ui/Layout';
import { Phone, ExternalLink, Copy, CheckCircle2, Headset, Globe2, ScanLine } from 'lucide-react';
import './Help.css';

const Help: React.FC = () => {
  const [copied, setCopied] = useState(false);
  const phoneNumber = '************';

  const openExternal = (url: string) => {
    if (window.electronAPI?.openExternal) {
      window.electronAPI.openExternal(url);
    } else {
      window.open(url, '_blank');
    }
  };

  const handleCopyPhone = async () => {
    try {
      await navigator.clipboard.writeText(phoneNumber);
      setCopied(true);
      setTimeout(() => setCopied(false), 2500);
    } catch (e) {
      console.error('复制失败', e);
    }
  };

  return (
    <PageContainer>
      <PageHeader title="帮助中心" subtitle="官方支持渠道一览" />
      <div className="help-support-grid two-by-two">
        {/* 公众号 */}
        <div className="support-card">
          <div className="card-header">
            <div className="card-icon qr"><ScanLine size={22} /></div>
            <h3>关注公众号</h3>
          </div>
          <div className="card-body">
            <p className="card-desc">获取产品更新、功能攻略、心理健康科普。推荐第一次使用时先关注，便于后续收到重要通知。</p>
            <div className="qr-wrapper small">
              <img src="/gzh.jpg" alt="公众号二维码" className="qr-image" />
            </div>
          </div>
          <div className="card-footer hint-text">若二维码不显示，请确认安装目录 public/gzh.jpg 是否存在。</div>
        </div>

        {/* 电话支持 */}
        <div className="support-card">
            <div className="card-header">
              <div className="card-icon phone"><Phone size={22} /></div>
              <h3>电话支持</h3>
            </div>
            <div className="card-body">
              <p className="card-desc">工作日 09:00 - 18:00（节假日除外）。适合紧急访问故障、启动异常、数据损坏等需要即时协助的场景。</p>
              <div className="phone-block">
                <span className="phone-number">{phoneNumber}</span>
                <button className="btn ghost" onClick={handleCopyPhone} aria-label="复制电话">
                  {copied ? <CheckCircle2 size={16} /> : <Copy size={16} />}
                  <span>{copied ? '已复制' : '复制号码'}</span>
                </button>
              </div>
              <ul className="mini-list">
                <li>准备：问题概述 / 复现步骤 / 期望结果</li>
                <li>若涉及数据，请先备份数据库文件</li>
              </ul>
            </div>
            <div className="card-footer hint-text">非紧急问题建议优先通过客服渠道，便于留存记录。</div>
        </div>

        {/* 官网 */}
        <div className="support-card">
          <div className="card-header">
            <div className="card-icon web"><Globe2 size={22} /></div>
            <h3>官方网站</h3>
          </div>
          <div className="card-body">
            <p className="card-desc">集中查看产品路线、版本更新说明、安装包与选购信息。遇到升级提示或需要重新下载安装请前往官网。</p>
            <div className="action-row">
              <button className="btn primary" onClick={() => openExternal('https://www.bjrdtc.com')}>
                <ExternalLink size={16} /> 打开官网
              </button>
            </div>
            <ul className="mini-list">
              <li>始终从官网获取最新安装包</li>
              <li>浏览器打不开时检查本机网络/DNS</li>
            </ul>
          </div>
          <div className="card-footer hint-text">下载前建议关闭安全软件的“拦截未知应用”提示。</div>
        </div>

        {/* 售后客服 */}
        <div className="support-card">
          <div className="card-header">
            <div className="card-icon support"><Headset size={22} /></div>
            <h3>售后客服</h3>
          </div>
          <div className="card-body">
            <p className="card-desc">更适合需要持续跟进的问题：环境部署、数据迁移、功能咨询、改进建议与使用培训。</p>
            <div className="action-row">
              <button className="btn secondary" onClick={() => openExternal('https://work.weixin.qq.com/kfid/kfc605d7e78dd00133d')}>
                <ExternalLink size={16} /> 打开客服
              </button>
            </div>
            <ul className="mini-list">
              <li>支持发送截图/日志</li>
              <li>复杂问题可预约远程协助</li>
            </ul>
          </div>
          <div className="card-footer hint-text">微信内打开后即可与专属客服建立会话。</div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Help;
