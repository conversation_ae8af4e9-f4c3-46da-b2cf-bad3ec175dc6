// 沙具类别数据
export const sandToolCategories = [
  { id: '人物', name: '人物', icon: '👥' },
  { id: '动物', name: '动物', icon: '🦁' },
  { id: '植物', name: '植物', icon: '🌱' },
  { id: '建筑', name: '建筑', icon: '🏠' },
  { id: '交通工具', name: '交通工具', icon: '🚗' },
  { id: '自然元素', name: '自然元素', icon: '🌊' },
  { id: '家具用品', name: '家具用品', icon: '🪑' },
  { id: '食物', name: '食物', icon: '🍎' },
  { id: '装饰品', name: '装饰品', icon: '🎨' },
  { id: '其他', name: '其他', icon: '📦' }
];

// 沙具材质数据
export const sandToolMaterials = [
  '陶瓷', '树脂', '木质', '金属', '塑料', '玻璃', '布料', '石头', '纸制品'
];

// 沙具状况数据
export const sandToolConditions = [
  '全新', '良好', '一般', '损坏', '报废'
];

// 模拟沙具数据
export const mockSandTools = [
  {
    id: 'tool-1',
    name: '小房子',
    category: '建筑',
    subcategory: '住宅',
    description: '木质小房子模型',
    material: '木质',
    size: '5cm x 4cm x 3cm',
    color: '#8B4513',
    quantity: 5,
    available: 3,
    condition: '良好',
    location: 'A-01-01',
    imageData: null,
    notes: '常用于家庭主题沙盘',
    lastUsed: '2024-03-15',
    usageCount: 12,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: 'tool-2',
    name: '小树',
    category: '植物',
    subcategory: '树木',
    description: '绿色塑料小树',
    material: '塑料',
    size: '8cm x 3cm x 3cm',
    color: '#228B22',
    quantity: 10,
    available: 8,
    condition: '良好',
    location: 'B-02-05',
    imageData: null,
    notes: '常用于自然环境沙盘',
    lastUsed: '2024-03-18',
    usageCount: 25,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: 'tool-3',
    name: '小汽车',
    category: '交通工具',
    subcategory: '汽车',
    description: '红色小汽车模型',
    material: '塑料',
    size: '6cm x 3cm x 2cm',
    color: '#FF0000',
    quantity: 3,
    available: 1,
    condition: '一般',
    location: 'C-03-02',
    imageData: null,
    notes: '部分磨损',
    lastUsed: '2024-03-10',
    usageCount: 8,
    needsCare: false,
    replacementNeeded: true
  },
  {
    id: 'tool-4',
    name: '小人',
    category: '人物',
    subcategory: '成人',
    description: '陶瓷小人模型',
    material: '陶瓷',
    size: '4cm x 2cm x 1cm',
    color: '#F5F5DC',
    quantity: 20,
    available: 15,
    condition: '良好',
    location: 'A-01-10',
    imageData: null,
    notes: '基础人物模型',
    lastUsed: '2024-03-20',
    usageCount: 42,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: 'tool-5',
    name: '小桥',
    category: '建筑',
    subcategory: '桥梁',
    description: '木质小桥模型',
    material: '木质',
    size: '10cm x 4cm x 2cm',
    color: '#A0522D',
    quantity: 2,
    available: 2,
    condition: '良好',
    location: 'A-01-05',
    imageData: null,
    notes: '用于连接场景',
    lastUsed: '2024-03-05',
    usageCount: 6,
    needsCare: false,
    replacementNeeded: false
  }
];